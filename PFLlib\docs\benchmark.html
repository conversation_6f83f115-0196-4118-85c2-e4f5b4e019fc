<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PFLlib</title>
    <link rel="icon" href="imgs/logo-green.png" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            background-color: #f4f4f4;
            color: #333333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .navbar {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 1rem 0rem;
            position: fixed;
            width: 100%;
            z-index: 1000;
            transition: background-color 0.3s ease;
            height: 2rem;
        }
        .navbar.scrolled {
            background-color: rgba(0, 0, 0, 0.7);
        }
        .navbar-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: 1200px;
        }
        .navbar h1 {
            margin: 0;
            color: white;
        }
        .navbar nav {
            display: flex;
            gap: 1rem;
        }
        .navbar a {
            color: white;
            text-decoration: none;
            transition: color 0.3s ease;
            padding: 0rem 1rem;
        }
        .navbar a:hover {
            color: #6DA945;
        }
        .container {
            max-width: 1200px;
            margin: 8rem auto 2rem; /* Adjusted margin for container */
            padding: 0 2rem;
            flex-grow: 1; /* Ensures container takes up remaining space */
            display: flex;
        }
        .sidebar {
            width: 15rem;
            padding-right: 2rem;
            box-sizing: border-box;
        }
        .sidebar ul {
            list-style-type: none;
            padding: 0;
        }
        .sidebar li {
            margin-bottom: 0.5rem;
        }
        .sidebar a {
            color: #6DA945;
            text-decoration: none;
            font-weight: bold;
            transition: color 0.3s ease;
        }
        .sidebar a:hover {
            color: #2c6307;
        }
        .content {
            width: 75%;
            box-sizing: border-box;
        }
        h1, h2, h3 {
            color: #333333;
        }
        section {
            margin-bottom: 2rem;
        }
        pre {
            background-color: #f9f9f9;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        code {
            font-family: "Courier New", Courier, monospace;
            background-color: #f5f5f5;
            border-radius: 3px;
            padding: 2px 4px;
            color: #6DA945;
            font-weight: bold;
            font-weight: bold;
        }
        pre {
            font-family: "Courier New", Courier, monospace;
            background-color: #f5f5f5;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 1rem 0;
            position: relative;
            width: 100%;
        }
        html {
            scroll-padding-top: 4.5rem; /* Adjust to the height of your navbar */
        }
        a {
            text-decoration: none;
            color: #6DA945;
        }
        table {
            margin-left: auto;
            margin-right: auto;
            text-align: center; 
            font-size: 0.8em;
        }

        .hamburger {
            display: none;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            position: absolute;
            right: 1rem;
        }

        .hamburger span {
            display: block;
            width: 20px;
            height: 3px;
            background: white;
            margin: 5px 0;
            transition: 0.3s;
        }

        @media (max-width: 768px) {
            .container {
                max-width: 100%;
                flex-direction: column;
                margin-top: 6rem;
            }
            .sidebar, .content {
                width: 100%;
            }

            .navbar-container {
                flex-direction: row;
                flex-wrap: wrap;
            }

            .hamburger {
                display: block;
            }

            .navbar nav {
                display: none;
                flex-direction: column;
                width: 100%;
                background: #333333;
                padding: 1rem;
                margin-top: 2rem;
            }

            .navbar nav.active {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <div class="navbar">
        <div class="navbar-container">
            <h1><img src="imgs/logo-green.png" alt="icon" height="36" style="vertical-align: sub; margin-left: 10pt;"/><a href="index.html" id="PFLlib">PFLlib</a></h1>
            <button class="hamburger" aria-label="Menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <nav>
                <a href="index.html">Home</a>
                <a href="docs.html">Documentation</a>
                <a href="benchmark.html">Benchmark</a>
                <a href="about.html">About</a>
                <a href="https://github.com/TsingZ0/PFLlib" id="github-stars" class="github-stars">★ Star 1500</a>
            </nav>
        </div>
    </div>
    <div class="container">
        <div class="sidebar">
            <ul>
                <li><a href="quickstart.html">Quick Start</a></li>
                <li><a href="algo.html">FL Algorithms</a></li>
                <li><a href="data.html">Datasets & Scenarios</a></li>
                <li><a href="model.html">Models</a></li>
                <li><a href="extend.html">Easy to Extend</a></li>
                <li><a href="features.html">Other Features</a></li>
            </ul>
        </div>
        <div class="content">
            <section id="intro">
                <h2>Benchmark Platform</h2>
                <p>To integrate all the algorithms, datasets, and scenarios, we standardize the experimental settings and create a <strong>unified benchmark platform</strong> for a fair comparison of these algorithms. Here, we present the benchmark results of <strong>20 algorithms</strong> across two widely-used <em><strong>label skew</strong></em> scenarios. This is just one example. You can obtain different results by resetting all the configurations in <code>main.py</code> in our PFLlib.</p>
                <h3>Leaderboard</h3>
                <p>Our methods—<a href="https://github.com/TsingZ0/FedCP"><strong>FedCP</strong></a>, <a href="https://github.com/TsingZ0/GPFL"><strong>GPFL</strong></a>, and <a href="https://github.com/TsingZ0/DBE"><strong>FedDBE</strong></a>—lead the way. Notably, <strong>FedDBE</strong> stands out with robust performance across varying data heterogeneity levels.</p>
                <h4 style="width:100%; text-align:center;">The test accuracy (%) on the CV and NLP tasks in <em>label skew</em> settings.</h4>
                <table border="1" cellpadding="5" cellspacing="0">
                <thead>
                    <tr>
                    <th rowspan="2"><strong>Settings</strong></th>
                    <th colspan="3"><strong>Pathological <em>Label Skew</em> Setting</strong></th>
                    <th colspan="5"><strong>Practical <em>Label Skew</em> Setting</strong></th>
                    </tr>
                    <tr>
                    <th>FMNIST</th>
                    <th>Cifar100</th>
                    <th>TINY</th>
                    <th>FMNIST</th>
                    <th>Cifar100</th>
                    <th>TINY</th>
                    <th>TINY*</th>
                    <th>AG News</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                    <td><strong>FedAvg</strong></td>
                    <td>80.41 ± 0.08</td>
                    <td>25.98 ± 0.13</td>
                    <td>14.20 ± 0.47</td>
                    <td>85.85 ± 0.19</td>
                    <td>31.89 ± 0.47</td>
                    <td>19.46 ± 0.20</td>
                    <td>19.45 ± 0.13</td>
                    <td>87.12 ± 0.19</td>
                    </tr>
                    <tr>
                    <td><strong>FedProx</strong></td>
                    <td>78.08 ± 0.15</td>
                    <td>25.94 ± 0.16</td>
                    <td>13.85 ± 0.25</td>
                    <td>85.63 ± 0.57</td>
                    <td>31.99 ± 0.41</td>
                    <td>19.37 ± 0.22</td>
                    <td>19.27 ± 0.23</td>
                    <td>87.21 ± 0.13</td>
                    </tr>
                    <tr>
                    <td><strong>FedGen</strong></td>
                    <td>79.76 ± 0.60</td>
                    <td>20.80 ± 1.00</td>
                    <td>13.82 ± 0.09</td>
                    <td>84.90 ± 0.31</td>
                    <td>30.96 ± 0.54</td>
                    <td>19.39 ± 0.18</td>
                    <td>18.53 ± 0.32</td>
                    <td>89.86 ± 0.83</td>
                    </tr>
                    <tr>
                    <td><strong>Per-FedAvg</strong></td>
                    <td>99.18 ± 0.08</td>
                    <td>56.80 ± 0.26</td>
                    <td>28.06 ± 0.40</td>
                    <td>95.10 ± 0.10</td>
                    <td>44.28 ± 0.33</td>
                    <td>25.07 ± 0.07</td>
                    <td>21.81 ± 0.54</td>
                    <td>87.08 ± 0.26</td>
                    </tr>
                    <tr>
                    <td><strong>pFedMe</strong></td>
                    <td>99.35 ± 0.14</td>
                    <td>58.20 ± 0.14</td>
                    <td>27.71 ± 0.40</td>
                    <td>97.25 ± 0.17</td>
                    <td>47.34 ± 0.46</td>
                    <td>26.93 ± 0.19</td>
                    <td>33.44 ± 0.33</td>
                    <td>87.08 ± 0.18</td>
                    </tr>
                    <tr>
                    <td><strong>Ditto</strong></td>
                    <td>99.44 ± 0.06</td>
                    <td>67.23 ± 0.07</td>
                    <td>39.90 ± 0.42</td>
                    <td>97.47 ± 0.04</td>
                    <td>52.87 ± 0.64</td>
                    <td>32.15 ± 0.04</td>
                    <td>35.92 ± 0.43</td>
                    <td>91.89 ± 0.17</td>
                    </tr>
                    <tr>
                    <td><strong>APFL</strong></td>
                    <td>99.41 ± 0.02</td>
                    <td>64.26 ± 0.13</td>
                    <td>36.47 ± 0.44</td>
                    <td>97.25 ± 0.08</td>
                    <td>46.74 ± 0.60</td>
                    <td>34.86 ± 0.43</td>
                    <td>35.81 ± 0.37</td>
                    <td>89.37 ± 0.86</td>
                    </tr>
                    <tr>
                    <td><strong>FedFomo</strong></td>
                    <td>99.46 ± 0.01</td>
                    <td>62.49 ± 0.22</td>
                    <td>36.55 ± 0.50</td>
                    <td>97.21 ± 0.02</td>
                    <td>45.39 ± 0.45</td>
                    <td>26.33 ± 0.22</td>
                    <td>26.84 ± 0.11</td>
                    <td>91.20 ± 0.18</td>
                    </tr>
                    <tr>
                    <td><strong>FedAMP</strong></td>
                    <td>99.42 ± 0.03</td>
                    <td>64.34 ± 0.37</td>
                    <td>36.12 ± 0.30</td>
                    <td>97.20 ± 0.06</td>
                    <td>47.69 ± 0.49</td>
                    <td>27.99 ± 0.11</td>
                    <td>29.11 ± 0.15</td>
                    <td>83.35 ± 0.05</td>
                    </tr>
                    <tr>
                    <td><strong>APPLE</strong></td>
                    <td>99.30 ± 0.01</td>
                    <td>65.80 ± 0.08</td>
                    <td>36.22 ± 0.40</td>
                    <td>97.06 ± 0.07</td>
                    <td>53.22 ± 0.20</td>
                    <td>35.04 ± 0.47</td>
                    <td>39.93 ± 0.52</td>
                    <td>84.10 ± 0.18</td>
                    </tr>
                    <tr>
                    <td><strong>FedALA</strong></td>
                    <td>99.57 ± 0.01</td>
                    <td>67.83 ± 0.06</td>
                    <td>40.31 ± 0.30</td>
                    <td>97.66 ± 0.02</td>
                    <td>55.92 ± 0.03</td>
                    <td>40.54 ± 0.02</td>
                    <td>41.94 ± 0.02</td>
                    <td>92.45 ± 0.10</td>
                    </tr>
                    <tr>
                    <td><strong>FedPer</strong></td>
                    <td>99.47 ± 0.03</td>
                    <td>63.53 ± 0.21</td>
                    <td>39.80 ± 0.39</td>
                    <td>97.44 ± 0.06</td>
                    <td>49.63 ± 0.54</td>
                    <td>33.84 ± 0.34</td>
                    <td>38.45 ± 0.85</td>
                    <td>91.85 ± 0.24</td>
                    </tr>
                    <tr>
                    <td><strong>FedRep</strong></td>
                    <td>99.56 ± 0.03</td>
                    <td>67.56 ± 0.31</td>
                    <td>40.85 ± 0.37</td>
                    <td>97.56 ± 0.04</td>
                    <td>52.39 ± 0.35</td>
                    <td>37.27 ± 0.20</td>
                    <td>39.95 ± 0.61</td>
                    <td>92.25 ± 0.20</td>
                    </tr>
                    <tr>
                    <td><strong>FedRoD</strong></td>
                    <td>99.52 ± 0.05</td>
                    <td>62.30 ± 0.02</td>
                    <td>37.95 ± 0.22</td>
                    <td>97.52 ± 0.04</td>
                    <td>50.94 ± 0.11</td>
                    <td>36.43 ± 0.05</td>
                    <td>37.99 ± 0.26</td>
                    <td>92.16 ± 0.12</td>
                    </tr>
                    <tr>
                    <td><strong>FedBABU</strong></td>
                    <td>99.41 ± 0.05</td>
                    <td>66.85 ± 0.07</td>
                    <td>40.72 ± 0.64</td>
                    <td>97.46 ± 0.07</td>
                    <td>55.02 ± 0.33</td>
                    <td>36.82 ± 0.45</td>
                    <td>34.50 ± 0.62</td>
                    <td>95.86 ± 0.41</td>
                    </tr>
                    <tr>
                    <td><strong>FedCP</strong></td>
                    <td>99.66 ± 0.04</td>
                    <td>71.80 ± 0.16</td>
                    <td>44.52 ± 0.22</td>
                    <td><strong>97.89 ± 0.05</strong></td>
                    <td>59.56 ± 0.08</td>
                    <td><strong>43.49 ± 0.04</strong></td>
                    <td><strong>44.18 ± 0.21</strong></td>
                    <td>92.89 ± 0.10</td>
                    </tr>
                    <tr>
                    <td><strong>GPFL</strong></td>
                    <td><strong>99.85 ± 0.08</strong></td>
                    <td>71.78 ± 0.26</td>
                    <td><strong>44.58 ± 0.06</strong></td>
                    <td>97.81 ± 0.09</td>
                    <td>61.86 ± 0.31</td>
                    <td>43.37 ± 0.53</td>
                    <td>43.70 ± 0.44</td>
                    <td><strong>97.97 ± 0.14</strong></td>
                    </tr>
                    <tr>
                    <td><strong>FedDBE</strong></td>
                    <td>99.74 ± 0.04</td>
                    <td><strong>73.38 ± 0.18</strong></td>
                    <td>42.89 ± 0.29</td>
                    <td>97.69 ± 0.05</td>
                    <td><strong>64.39 ± 0.27</strong></td>
                    <td>43.32 ± 0.37</td>
                    <td>42.98 ± 0.52</td>
                    <td>96.87 ± 0.18</td>
                    </tr>
                    <tr>
                    <td><strong>FedDistill</strong></td>
                    <td>99.51 ± 0.03</td>
                    <td>66.78 ± 0.15</td>
                    <td>37.21 ± 0.25</td>
                    <td>97.43 ± 0.04</td>
                    <td>49.93 ± 0.23</td>
                    <td>30.02 ± 0.09</td>
                    <td>29.88 ± 0.41</td>
                    <td>85.76 ± 0.09</td>
                    </tr>
                    <tr>
                    <td><strong>FedProto</strong></td>
                    <td>99.49 ± 0.04</td>
                    <td>69.18 ± 0.03</td>
                    <td>36.78 ± 0.07</td>
                    <td>97.40 ± 0.02</td>
                    <td>52.70 ± 0.33</td>
                    <td>31.21 ± 0.16</td>
                    <td>26.38 ± 0.40</td>
                    <td>96.34 ± 0.58</td>
                    </tr>
                </tbody>
                </table>

                <h4 style="width:100%; text-align:center;">The test accuracy (%) under different heterogeneous degrees in the practical <em>label skew</em> setting using the 4-layer CNN.</h4>
                <table border="1" cellpadding="5" cellspacing="0">
                    <thead>
                        <tr>
                            <th rowspan="2" colspan="1"><b>Datasets</b></th>
                            <th colspan="4"><b>Cifar100</b></th>
                            <th colspan="4"><b>Tiny-ImageNet</b></th>
                        </tr>
                        <tr>
                            <th>β = 0.01</th>
                            <th>β = 0.1</th>
                            <th>β = 0.5</th>
                            <th>β = 5</th>
                            <th>β = 0.01</th>
                            <th>β = 0.1</th>
                            <th>β = 0.5</th>
                            <th>β = 5</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><b>FedAvg</b></td>
                            <td>23.58 ± 0.43</td>
                            <td>31.89 ± 0.47</td>
                            <td>27.99 ± 0.32</td>
                            <td>35.51 ± 0.33</td>
                            <td>15.70 ± 0.46</td>
                            <td>19.46 ± 0.20</td>
                            <td>21.14 ± 0.47</td>
                            <td>21.71 ± 0.27</td>
                        </tr>
                        <tr>
                            <td><b>FedProx</b></td>
                            <td>23.74 ± 0.25</td>
                            <td>31.99 ± 0.41</td>
                            <td>35.05 ± 0.20</td>
                            <td>35.31 ± 0.43</td>
                            <td>15.66 ± 0.36</td>
                            <td>19.37 ± 0.22</td>
                            <td>21.22 ± 0.47</td>
                            <td>21.69 ± 0.25</td>
                        </tr>
                        <tr>
                            <td><b>FedGen</b></td>
                            <td>20.89 ± 0.42</td>
                            <td>30.96 ± 0.54</td>
                            <td>33.88 ± 0.83</td>
                            <td>35.64 ± 0.36</td>
                            <td>15.87 ± 0.23</td>
                            <td>19.19 ± 0.18</td>
                            <td>21.06 ± 0.08</td>
                            <td>21.44 ± 0.51</td>
                        </tr>
                        <tr>
                            <td><b>Per-FedAvg</b></td>
                            <td>49.25 ± 0.54</td>
                            <td>44.28 ± 0.33</td>
                            <td>35.32 ± 0.12</td>
                            <td>24.94 ± 0.64</td>
                            <td>39.39 ± 0.30</td>
                            <td>25.07 ± 0.07</td>
                            <td>16.36 ± 0.13</td>
                            <td>12.08 ± 0.27</td>
                        </tr>
                        <tr>
                            <td><b>pFedMe</b></td>
                            <td>65.28 ± 0.83</td>
                            <td>47.34 ± 0.46</td>
                            <td>32.52 ± 1.57</td>
                            <td>14.70 ± 0.92</td>
                            <td>41.45 ± 0.14</td>
                            <td>26.93 ± 0.19</td>
                            <td>17.48 ± 0.61</td>
                            <td>4.03 ± 0.50</td>
                        </tr>
                        <tr>
                            <td><b>Ditto</b></td>
                            <td>73.29 ± 0.49</td>
                            <td>52.87 ± 0.64</td>
                            <td>26.28 ± 0.17</td>
                            <td>35.72 ± 0.21</td>
                            <td>50.62 ± 0.02</td>
                            <td>32.15 ± 0.04</td>
                            <td>18.98 ± 0.05</td>
                            <td>21.79 ± 0.62</td>
                        </tr>
                        <tr>
                            <td><b>APFL</b></td>
                            <td>72.63 ± 0.15</td>
                            <td>46.74 ± 0.60</td>
                            <td>25.69 ± 0.21</td>
                            <td>20.76 ± 9.03</td>
                            <td>49.96 ± 0.04</td>
                            <td>34.87 ± 0.43</td>
                            <td>23.31 ± 0.18</td>
                            <td>16.12 ± 0.10</td>
                        </tr>
                        <tr>
                            <td><b>FedFomo</b></td>
                            <td>71.11 ± 0.08</td>
                            <td>45.39 ± 0.45</td>
                            <td>24.35 ± 0.55</td>
                            <td>29.77 ± 0.56</td>
                            <td>46.36 ± 0.54</td>
                            <td>26.33 ± 0.22</td>
                            <td>11.59 ± 0.11</td>
                            <td>14.86 ± 0.55</td>
                        </tr>
                        <tr>
                            <td><b>FedAMP</b></td>
                            <td>72.78 ± 0.17</td>
                            <td>47.69 ± 0.49</td>
                            <td>25.94 ± 0.12</td>
                            <td>13.71 ± 0.12</td>
                            <td>48.42 ± 0.06</td>
                            <td>27.99 ± 0.11</td>
                            <td>12.48 ± 0.21</td>
                            <td>5.41 ± 0.14</td>
                        </tr>
                        <tr>
                            <td><b>APPLE</b></td>
                            <td>71.11 ± 0.10</td>
                            <td>53.22 ± 0.20</td>
                            <td>41.81 ± 0.23</td>
                            <td>32.68 ± 0.28</td>
                            <td>48.04 ± 0.10</td>
                            <td>35.04 ± 0.47</td>
                            <td>24.28 ± 0.21</td>
                            <td>17.79 ± 0.47</td>
                        </tr>
                        <tr>
                            <td><b>FedALA</b></td>
                            <td>73.82 ± 0.35</td>
                            <td>55.92 ± 0.03</td>
                            <td>44.17 ± 0.51</td>
                            <td>34.27 ± 0.70</td>
                            <td>55.75 ± 0.02</td>
                            <td>40.54 ± 0.02</td>
                            <td>29.04 ± 0.13</td>
                            <td>22.12 ± 0.22</td>
                        </tr>
                        <tr>
                            <td><b>FedPer</b></td>
                            <td>71.09 ± 0.34</td>
                            <td>49.63 ± 0.54</td>
                            <td>29.17 ± 0.21</td>
                            <td>16.09 ± 0.19</td>
                            <td>51.83 ± 0.22</td>
                            <td>33.84 ± 0.34</td>
                            <td>17.31 ± 0.19</td>
                            <td>9.61 ± 0.06</td>
                        </tr>
                        <tr>
                            <td><b>FedRep</b></td>
                            <td>74.91 ± 0.16</td>
                            <td>52.39 ± 0.35</td>
                            <td>29.74 ± 0.21</td>
                            <td>14.93 ± 0.12</td>
                            <td>55.43 ± 0.15</td>
                            <td>37.27 ± 0.20</td>
                            <td>16.74 ± 0.09</td>
                            <td>8.04 ± 0.05</td>
                        </tr>
                        <tr>
                            <td><b>FedRoD</b></td>
                            <td>67.78 ± 0.55</td>
                            <td>50.94 ± 0.11</td>
                            <td>36.29 ± 0.07</td>
                            <td>25.63 ± 0.74</td>
                            <td>49.17 ± 0.06</td>
                            <td>36.43 ± 0.05</td>
                            <td>23.23 ± 0.11</td>
                            <td>16.71 ± 0.24</td>
                        </tr>
                        <tr>
                            <td><b>FedBABU</b></td>
                            <td>73.30 ± 0.32</td>
                            <td>55.02 ± 0.33</td>
                            <td>39.35 ± 0.45</td>
                            <td>27.61 ± 0.51</td>
                            <td>53.97 ± 0.25</td>
                            <td>36.82 ± 0.45</td>
                            <td>23.08 ± 0.20</td>
                            <td>15.42 ± 0.30</td>
                        </tr>
                        <tr>
                            <td><b>FedCP</b></td>
                            <td>77.76 ± 0.03</td>
                            <td>59.56 ± 0.08</td>
                            <td>41.76 ± 0.15</td>
                            <td>26.83 ± 0.03</td>
                            <td>56.31 ± 0.39</td>
                            <td><b>43.49 ± 0.04</b></td>
                            <td>28.57 ± 0.07</td>
                            <td>19.12 ± 0.13</td>
                        </tr>
                        <tr>
                            <td><b>GPFL</b></td>
                            <td>76.27 ± 0.12</td>
                            <td>61.86 ± 0.31</td>
                            <td>43.73 ± 0.42</td>
                            <td>30.86 ± 0.28</td>
                            <td><b>57.05 ± 0.41</b></td>
                            <td>43.37 ± 0.53</td>
                            <td>26.85 ± 0.14</td>
                            <td>16.34 ± 0.39</td>
                        </tr>
                        <tr>
                            <td><b>FedDBE</b></td>
                            <td><b>78.39 ± 0.08</b></td>
                            <td><b>64.39 ± 0.27</b></td>
                            <td><b>52.58 ± 0.17</b></td>
                            <td><b>41.12 ± 0.30</b></td>
                            <td>54.61 ± 0.09</td>
                            <td>43.32 ± 0.37</td>
                            <td><b>33.71 ± 0.15</b></td>
                            <td><b>26.76 ± 0.11</b></td>
                        </tr>
                        <tr>
                            <td><b>FedDistill</b></td>
                            <td>74.63 ± 0.14</td>
                            <td>49.93 ± 0.23</td>
                            <td>29.32 ± 0.46</td>
                            <td>15.45 ± 0.44</td>
                            <td>50.49 ± 0.07</td>
                            <td>30.02 ± 0.09</td>
                            <td>14.34 ± 0.06</td>
                            <td>6.56 ± 0.06</td>
                        </tr>
                        <tr>
                            <td><b>FedProto</b></td>
                            <td>77.19 ± 0.15</td>
                            <td>52.70 ± 0.33</td>
                            <td>32.57 ± 0.23</td>
                            <td>14.20 ± 3.92</td>
                            <td>50.65 ± 0.32</td>
                            <td>31.21 ± 0.16</td>
                            <td>16.69 ± 0.02</td>
                            <td>8.92 ± 0.19</td>
                        </tr>
                    </tbody>
                </table>

                <h4 style="width:100%; text-align:center;">The time cost (in minutes) on Tiny-ImageNet using ResNet-18 in the practical <em>label skew</em> setting.</h4>
                <table border="1" cellpadding="5" cellspacing="0">
                <thead>
                    <tr>
                    <th>Items</th>
                    <th>Total time</th>
                    <th>Iterations</th>
                    <th>Average time</th>
                    </tr>
                </thead>
                <tbody>
                <tr>
                    <td><b>FedAvg</b></td>
                    <td>365</td>
                    <td>230</td>
                    <td>1.59</td>
                </tr>
                <tr>
                    <td><b>FedProx</b></td>
                    <td>325</td>
                    <td>163</td>
                    <td>1.99</td>
                </tr>
                <tr>
                    <td><b>FedGen</b></td>
                    <td>259</td>
                    <td>50</td>
                    <td>5.17</td>
                </tr>
                <tr>
                    <td><b>Per-FedAvg</b></td>
                    <td>121</td>
                    <td>34</td>
                    <td>3.56</td>
                </tr>
                <tr>
                    <td><b>pFedMe</b></td>
                    <td>1157</td>
                    <td>113</td>
                    <td>10.24</td>
                </tr>
                <tr>
                    <td><b>Ditto</b></td>
                    <td>318</td>
                    <td>27</td>
                    <td>11.78</td>
                </tr>
                <tr>
                    <td><b>APFL</b></td>
                    <td>156</td>
                    <td>57</td>
                    <td>2.74</td>
                </tr>
                <tr>
                    <td><b>FedFomo</b></td>
                    <td>193</td>
                    <td>71</td>
                    <td>2.72</td>
                </tr>
                <tr>
                    <td><b>FedAMP</b></td>
                    <td>92</td>
                    <td>60</td>
                    <td>1.53</td>
                </tr>
                <tr>
                    <td><b>APPLE</b></td>
                    <td>132</td>
                    <td>45</td>
                    <td>2.93</td>
                </tr>
                <tr>
                    <td><b>FedALA</b></td>
                    <td>123</td>
                    <td>63</td>
                    <td>1.93</td>
                </tr>
                <tr>
                    <td><b>FedPer</b></td>
                    <td>83</td>
                    <td>43</td>
                    <td>1.92</td>
                </tr>
                <tr>
                    <td><b>FedRep</b></td>
                    <td>471</td>
                    <td>115</td>
                    <td>4.09</td>
                </tr>
                <tr>
                    <td><b>FedRoD</b></td>
                    <td>87</td>
                    <td>50</td>
                    <td>1.74</td>
                </tr>
                <tr>
                    <td><b>FedBABU</b></td>
                    <td>811</td>
                    <td>513</td>
                    <td>1.58</td>
                </tr>
                <tr>
                    <td><b>FedCP</b></td>
                    <td>204</td>
                    <td>74</td>
                    <td>2.75</td>
                </tr>
                <tr>
                    <td><b>GPFL</b></td>
                    <td>171</td>
                    <td>75</td>
                    <td>2.28</td>
                </tr>
                <tr>
                    <td><b>FedDBE</b></td>
                    <td>171</td>
                    <td>107</td>
                    <td>1.60</td>
                </tr>
                <tr>
                    <td><b>FedDistill</b></td>
                    <td>45</td>
                    <td>16</td>
                    <td>2.78</td>
                </tr>
                <tr>
                    <td><b>FedProto</b></td>
                    <td>138</td>
                    <td>25</td>
                    <td>5.52</td>
                </tr>
                </tbody>
                </table>
                  
                <h3>Experimental Setup</h3>
                <p>We set up the experiments following our pFL algorithm <a href="https://arxiv.org/pdf/2308.10279v3.pdf"><strong>GPFL</strong></a>, as it provides comprehensive evaluations. Here are the details:</p>
                <h4>Datasets and Models</h4>
                <ul>
                    <li>For the CV tasks, we use three popular datasets: 
                        <ul>
                            <li>Fashion-MNIST (FMNIST) (<a href="https://github.com/TsingZ0/PFLlib/blob/master/dataset/generate_FashionMNIST.py"><code>generate_FashionMNIST.py</code></a>) | 4-layer CNN (<a href="https://github.com/TsingZ0/PFLlib/blob/master/system/flcore/trainmodel/models.py#L163">model code</a>)</li>
                            <li>Cifar100 (<a href="https://github.com/TsingZ0/PFLlib/blob/master/dataset/generate_Cifar100.py"><code>generate_Cifar100.py</code></a>) | 4-layer CNN (<a href="https://github.com/TsingZ0/PFLlib/blob/master/system/flcore/trainmodel/models.py#L163">model code</a>)</li>
                            <li>Tiny-ImageNet (<a href="https://github.com/TsingZ0/PFLlib/blob/master/dataset/generate_TinyImagenet.py"><code>generate_TinyImagenet.py</code></a>) | 4-layer CNN (<a href="https://github.com/TsingZ0/PFLlib/blob/master/system/flcore/trainmodel/models.py#L163">model code</a>) and ResNet-18 (<a href="https://pytorch.org/vision/main/models/generated/torchvision.models.resnet18.html">model code</a>)</li>
                        </ul>
                    </li>
                    <li>For the NLP task, we use one popular dataset:
                        <ul>
                            <li>AG News (<a href="https://github.com/TsingZ0/PFLlib/blob/master/dataset/generate_AGNews.py"><code>generate_AGNews.py</code></a>) | fastText (<a href="https://github.com/TsingZ0/PFLlib/blob/master/system/flcore/trainmodel/models.py#L454">model code</a>)</li>
                        </ul>
                    </li>
                </ul>
                <p>We denote TINY as the 4-layer CNN on Tiny-ImageNet, and TINY* as the ResNet-18 on Tiny-ImageNet, respectively.</p>
                <h4>Two Widely-Used <em><strong>Label Skew</strong></em> Scenarios</h4>
                <ul>
                    <li><strong>Pathological label skew:</strong> We sample data with label distribution of 2/10/20 for each client on FMNIST, Cifar100, and Tiny-ImageNet, drawn from a total of 10/100/200 categories. The data is disjoint, with varying numbers of samples across clients.</li>
                    <li><strong>Practical label skew:</strong> Data is sampled from FMNIST, CIFAR-100, Tiny-ImageNet, and AG News using Dirichlet distribution, denoted by \(Dir(\beta)\). Specifically, we sample \(q_{c, i} \sim Dir(\beta)\) (with \(\beta = 0.1\) or \(\beta = 1\) by default for CV/NLP tasks) and allocate a \(q_{c, i}\) proportion of samples with label \(c\) to client \(i\).</li>
                </ul>
                <h4>Other Implementation Details</h4>
                <p>Following pFedMe and FedRoD, we use 20 clients with a client joining ratio of \(\rho = 1\), with 75% of data for training and 25% for evaluation. We report the best global model performance for traditional FL and the best average performance across personalized models for pFL. The batch size is set to 10, and the number of local epochs is 1. We run 2000 iterations with three trials per method and report the mean and standard deviation.</p>
                
                <h3>References</h3>
                <p>If you're interested in <strong>experimental results (e.g., accuracy)</strong> for the algorithms mentioned above, you can find results in our accepted FL papers, which also utilize this library. These papers include:</p>

                <ul>
                    <li><a href="https://github.com/TsingZ0/FedALA">FedALA</a></li>
                    <li><a href="https://github.com/TsingZ0/FedCP">FedCP</a></li>
                    <li><a href="https://github.com/TsingZ0/GPFL">GPFL</a></li>
                    <li><a href="https://github.com/TsingZ0/DBE">FedDBE</a></li>
                </ul>

                <p>Here are the relevant papers for your reference:</p>
                <pre>
@inproceedings{zhang2023fedala,
    title={Fedala: Adaptive local aggregation for personalized federated learning},
    author={Zhang, Jianqing and Hua, Yang and Wang, Hao and Song, Tao and Xue, Zhengui and Ma, Ruhui and Guan, Haibing},
    booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
    volume={37},
    number={9},
    pages={11237--11244},
    year={2023}
}

@inproceedings{Zhang2023fedcp,
    author = {Zhang, Jianqing and Hua, Yang and Wang, Hao and Song, Tao and Xue, Zhengui and Ma, Ruhui and Guan, Haibing},
    title = {FedCP: Separating Feature Information for Personalized Federated Learning via Conditional Policy},
    year = {2023},
    booktitle = {Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining}
}

@inproceedings{zhang2023gpfl,
    title={GPFL: Simultaneously Learning Global and Personalized Feature Information for Personalized Federated Learning},
    author={Zhang, Jianqing and Hua, Yang and Wang, Hao and Song, Tao and Xue, Zhengui and Ma, Ruhui and Cao, Jian and Guan, Haibing},
    booktitle={Proceedings of the IEEE/CVF International Conference on Computer Vision},
    pages={5041--5051},
    year={2023}
}

@inproceedings{zhang2023eliminating,
    title={Eliminating Domain Bias for Federated Learning in Representation Space},
    author={Jianqing Zhang and Yang Hua and Jian Cao and Hao Wang and Tao Song and Zhengui XUE and Ruhui Ma and Haibing Guan},
    booktitle={Thirty-seventh Conference on Neural Information Processing Systems},
    year={2023},
    url={https://openreview.net/forum?id=nO5i1XdUS0}
}</pre>
            </section>
        </div>
    </div>
    <footer>
        <p>&copy; 2025 PFLlib. All rights reserved.</p>
    </footer>
    <script>
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        async function fetchGitHubStars() {
            try {
                const response = await fetch('https://api.github.com/repos/TsingZ0/PFLlib');
                if (!response.ok) throw new Error('Network response was not ok');
                const data = await response.json();
                document.getElementById('github-stars').textContent = `★ Star ${data.stargazers_count}`;
            } catch (error) {
                console.error('Failed to fetch GitHub stars:', error);
                document.getElementById('github-stars').textContent = '★ Star 1500';
            }
        }
        fetchGitHubStars();

        document.querySelector('.hamburger').addEventListener('click', function() {
            this.classList.toggle('active');
            document.querySelector('.navbar nav').classList.toggle('active');
        });

        document.addEventListener('click', function(e) {
            if (!e.target.closest('.navbar-container')) {
                document.querySelector('.navbar nav').classList.remove('active');
                document.querySelector('.hamburger').classList.remove('active');
            }
        });
    </script>
    <script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.7/MathJax.js?config=TeX-MML-AM_CHTML">
    </script>
</body>
</html>
