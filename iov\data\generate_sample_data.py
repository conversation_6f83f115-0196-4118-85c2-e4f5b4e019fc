import os
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from typing import Tuple, Optional, Union
from loguru import logger
import argparse

def generate_sample_data(output_path, num_samples=10000, num_features=10, num_classes=2, test_size=0.2, random_state=42):
    """
    生成示例入侵检测数据集
    
    参数:
        output_path: 输出文件路径
        num_samples: 样本数量
        num_features: 特征数量
        num_classes: 类别数量
        test_size: 测试集比例
        random_state: 随机种子
    """
    # 设置随机种子
    np.random.seed(random_state)
    
    # 生成特征
    X = np.random.randn(num_samples, num_features)
    
    # 生成标签
    if num_classes == 2:
        # 二分类问题
        # 正常流量特征通常更加集中
        normal_samples = int(num_samples * 0.7)  # 70%正常流量
        attack_samples = num_samples - normal_samples  # 30%攻击流量
        
        # 正常流量特征
        X[:normal_samples] = np.random.randn(normal_samples, num_features) * 0.5
        
        # 攻击流量特征
        X[normal_samples:] = np.random.randn(attack_samples, num_features) * 1.5 + 1.0
        
        # 标签
        y = np.zeros(num_samples)
        y[normal_samples:] = 1  # 1表示攻击
    else:
        # 多分类问题
        # 每个类别的样本数
        samples_per_class = num_samples // num_classes
        
        # 为每个类别生成特征
        for i in range(num_classes):
            start_idx = i * samples_per_class
            end_idx = (i + 1) * samples_per_class if i < num_classes - 1 else num_samples
            
            # 不同类别的特征分布不同
            X[start_idx:end_idx] = np.random.randn(end_idx - start_idx, num_features) * (0.5 + i * 0.3) + i
            
        # 标签
        y = np.zeros(num_samples)
        for i in range(num_classes):
            start_idx = i * samples_per_class
            end_idx = (i + 1) * samples_per_class if i < num_classes - 1 else num_samples
            y[start_idx:end_idx] = i
    
    # 创建特征名称
    feature_names = [f'feature_{i+1}' for i in range(num_features)]
    
    # 创建数据框
    df = pd.DataFrame(X, columns=feature_names)
    df['Label'] = y
    
    # 添加一些噪声
    df = df + np.random.randn(*df.shape) * 0.01
    
    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存数据集
    df.to_csv(output_path, index=False)
    
    print(f"生成的示例数据集保存到 {output_path}")
    print(f"数据集形状: {df.shape}")
    print(f"类别分布:\n{df['Label'].value_counts()}")
    
    return df

def load_csv_data(
    data_path: str,
    label_column: str = 'Label',
    test_size: float = 0.2,
    random_state: int = 42,
    normalize: bool = True,
    drop_columns: Optional[list] = None,
    train_path: Optional[str] = None,
    test_path: Optional[str] = None
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    加载并预处理CSV格式的入侵检测数据集。

    参数:
        data_path: CSV文件路径（当train_path和test_path为None时使用）
        label_column: 标签列的名称
        test_size: 测试集比例（当train_path和test_path为None时使用）
        random_state: 随机种子（当train_path和test_path为None时使用）
        normalize: 是否对特征进行标准化
        drop_columns: 需要删除的列名列表
        train_path: 训练集文件路径（可选）
        test_path: 测试集文件路径（可选）

    返回:
        Tuple[pd.DataFrame, pd.DataFrame]: 训练集和测试集数据框

    异常:
        FileNotFoundError: 当数据文件不存在时
        ValueError: 当标签列不存在时
    """
    # 检查是否提供了已划分的训练集和测试集
    if train_path is not None and test_path is not None:
        logger.info("使用已划分的训练集和测试集")
        
        # 检查文件是否存在
        if not os.path.exists(train_path) or not os.path.exists(test_path):
            raise FileNotFoundError(f"训练集或测试集文件不存在: {train_path}, {test_path}")
            
        # 读取训练集和测试集
        train_data = pd.read_csv(train_path)
        test_data = pd.read_csv(test_path)
        
        # 检查标签列是否存在
        if label_column not in train_data.columns or label_column not in test_data.columns:
            raise ValueError(f"标签列 '{label_column}' 不存在于数据集中")
            
        # 删除不需要的列
        if drop_columns:
            train_data = train_data.drop(columns=drop_columns, errors='ignore')
            test_data = test_data.drop(columns=drop_columns, errors='ignore')
            logger.info(f"已删除列: {drop_columns}")
            
        # 标准化特征
        if normalize:
            scaler = StandardScaler()
            # 分离特征和标签
            X_train = train_data.drop(columns=[label_column])
            y_train = train_data[label_column]
            X_test = test_data.drop(columns=[label_column])
            y_test = test_data[label_column]
            
            # 使用训练集拟合scaler
            X_train = pd.DataFrame(
                scaler.fit_transform(X_train),
                columns=X_train.columns
            )
            X_test = pd.DataFrame(
                scaler.transform(X_test),
                columns=X_test.columns
            )
            
            # 合并特征和标签
            train_data = pd.concat([X_train, y_train], axis=1)
            test_data = pd.concat([X_test, y_test], axis=1)
            logger.info("特征已标准化")
            
        # 输出数据集信息
        logger.info(f"训练集形状: {train_data.shape}")
        logger.info(f"测试集形状: {test_data.shape}")
        logger.info(f"类别分布:\n{train_data[label_column].value_counts()}")
        
        return train_data, test_data
        
    else:
        # 原有的数据加载和划分逻辑
        if not os.path.exists(data_path):
            raise FileNotFoundError(f"数据文件不存在: {data_path}")

        logger.info(f"正在加载数据: {data_path}")
        df = pd.read_csv(data_path)

        if label_column not in df.columns:
            raise ValueError(f"标签列 '{label_column}' 不存在于数据集中")

        if drop_columns:
            df = df.drop(columns=drop_columns, errors='ignore')
            logger.info(f"已删除列: {drop_columns}")

        X = df.drop(columns=[label_column])
        y = df[label_column]

        if normalize:
            scaler = StandardScaler()
            X = pd.DataFrame(
                scaler.fit_transform(X),
                columns=X.columns
            )
            logger.info("特征已标准化")

        X_train, X_test, y_train, y_test = train_test_split(
            X, y,
            test_size=test_size,
            random_state=random_state,
            stratify=y
        )

        train_data = pd.concat([X_train, y_train], axis=1)
        test_data = pd.concat([X_test, y_test], axis=1)

        logger.info(f"数据集形状: {df.shape}")
        logger.info(f"训练集形状: {train_data.shape}")
        logger.info(f"测试集形状: {test_data.shape}")
        logger.info(f"类别分布:\n{df[label_column].value_counts()}")

        return train_data, test_data

if __name__ == "__main__":
    def parse_args() -> argparse.Namespace:
        """
        解析命令行参数。

        返回:
            argparse.Namespace: 解析后的命令行参数
        """
        parser = argparse.ArgumentParser(description='入侵检测数据集生成和加载工具')
        
        # 数据源选择
        parser.add_argument('--mode', type=str, choices=['generate', 'load'], required=True,
                          help='选择模式：generate-生成示例数据，load-加载已有数据')
        
        # 生成数据参数
        parser.add_argument('--output_path', type=str, default='data/sample_cicids.csv',
                          help='输出文件路径（生成模式）或数据文件路径（加载模式）')
        parser.add_argument('--num_samples', type=int, default=500,
                          help='生成的样本数量（仅生成模式）')
        parser.add_argument('--num_features', type=int, default=50,
                          help='生成的特征数量（仅生成模式）')
        parser.add_argument('--num_classes', type=int, default=5,
                          help='生成的类别数量（仅生成模式）')
        
        # 加载数据参数
        parser.add_argument('--label_column', type=str, default='Label',
                          help='标签列名称（仅加载模式）')
        parser.add_argument('--test_size', type=float, default=0.2,
                          help='测试集比例')
        parser.add_argument('--normalize', action='store_true',
                          help='是否对特征进行标准化（仅加载模式）')
        parser.add_argument('--drop_columns', type=str, nargs='+',
                          help='需要删除的列名列表（仅加载模式）')
        
        # 已划分数据集参数
        parser.add_argument('--train_path', type=str,
                          help='训练集文件路径（可选）')
        parser.add_argument('--test_path', type=str,
                          help='测试集文件路径（可选）')
        
        return parser.parse_args()

    def main() -> None:
        """
        主函数，根据命令行参数执行相应的数据生成或加载操作。
        """
        args = parse_args()
        
        if args.mode == 'generate':
            # 生成示例数据
            logger.info("正在生成示例数据...")
            df = generate_sample_data(
                output_path=args.output_path,
                num_samples=args.num_samples,
                num_features=args.num_features,
                num_classes=args.num_classes
            )
            
            # 显示数据集信息
            logger.info("\n数据集预览:")
            logger.info(df.head())
            
        else:  # load mode
            # 加载已有数据
            logger.info("正在加载数据...")
            train_data, test_data = load_csv_data(
                data_path=args.output_path,
                label_column=args.label_column,
                test_size=args.test_size,
                normalize=args.normalize,
                drop_columns=args.drop_columns,
                train_path=args.train_path,
                test_path=args.test_path
            )
            
            # 显示数据集信息
            logger.info("\n训练集预览:")
            logger.info(train_data.head())
            logger.info("\n测试集预览:")
            logger.info(test_data.head())

    main() 

# 使用示例：
# 1. 生成示例数据：
# python generate_sample_data.py --mode generate --num_samples 1000 --num_features 50 --num_classes 5

# 2. 加载单个数据文件并划分：
# python generate_sample_data.py --mode load --output_path data/CICIDS2017.csv --label_column Label --normalize --drop_columns Flow_ID Source_IP Destination_IP

# 3. 加载已划分的训练集和测试集：
# python generate_sample_data.py --mode load --train_path data/train.csv --test_path data/test.csv --label_column Label --normalize