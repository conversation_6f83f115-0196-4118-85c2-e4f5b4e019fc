"""
Transformer入侵检测模型。
将LSTM特征编码、位置编码和Transformer（稀疏多头注意力）组合起来，用于车联网场景的入侵检测。
"""
from typing import Tuple, Optional, Dict, Any, Literal

import torch
import torch.nn as nn
import torch.nn.functional as F

from .lstm_encoder import LSTMEncoder
from .cnn_encoder import CNNEncoder
from .position_encoder import PositionalEncoding
from .sparse_transformer import SparseMultiheadAttention


class TransformerEncoderLayer(nn.Module):
    """Transformer编码器层，使用稀疏多头注意力机制。
    
    Attributes:
        d_model (int): 模型维度
        nhead (int): 注意力头数
        dim_feedforward (int): 前馈网络隐藏层维度
        dropout (float): Dropout概率
        k_ratio (float): 稀疏注意力的Top-K比例
        self_attn (SparseMultiheadAttention): 稀疏多头自注意力层
        linear1 (nn.Linear): 前馈网络第一层
        dropout1 (nn.Dropout): 自注意力后的Dropout
        linear2 (nn.Linear): 前馈网络第二层
        dropout2 (nn.Dropout): 前馈网络后的Dropout
        norm1 (nn.LayerNorm): 第一个层归一化
        norm2 (nn.LayerNorm): 第二个层归一化
        activation (callable): 激活函数
    """
    
    def __init__(
        self, 
        d_model: int, 
        nhead: int, 
        dim_feedforward: int = 2048, 
        dropout: float = 0.1, 
        k_ratio: float = 0.4, 
        activation: str = "relu"
    ) -> None:
        """初始化Transformer编码器层。
        
        Args:
            d_model: 模型维度
            nhead: 注意力头数
            dim_feedforward: 前馈网络隐藏层维度
            dropout: Dropout概率
            k_ratio: 稀疏注意力的Top-K比例
            activation: 激活函数，'relu'或'gelu'
        """
        super(TransformerEncoderLayer, self).__init__()
        
        self.d_model = d_model
        self.nhead = nhead
        self.dim_feedforward = dim_feedforward
        self.dropout = dropout
        self.k_ratio = k_ratio
        
        # 稀疏多头自注意力层
        self.self_attn = SparseMultiheadAttention(
            embed_dim=d_model,
            num_heads=nhead,
            dropout=dropout,
            k_ratio=k_ratio
        )
        
        # 前馈网络
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.dropout1 = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward, d_model)
        self.dropout2 = nn.Dropout(dropout)
        
        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # 激活函数
        self.activation = F.relu if activation == "relu" else F.gelu
        
    def forward(
        self, 
        src: torch.Tensor, 
        src_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """前向传播。
        
        Args:
            src: 输入序列，形状为 [batch_size, seq_len, d_model]
            src_mask: 源序列掩码，形状为 [batch_size, 1, seq_len, seq_len]
            
        Returns:
            tuple: (输出序列, 注意力权重)
                - 输出序列形状: [batch_size, seq_len, d_model]
                - 注意力权重形状: [batch_size, nhead, seq_len, seq_len]
        """
        # 自注意力
        src2, attn_weights = self.self_attn(src, src, src, src_mask)
        src = src + self.dropout1(src2)
        src = self.norm1(src)
        
        # 前馈网络
        src2 = self.linear2(self.dropout1(self.activation(self.linear1(src))))
        src = src + self.dropout2(src2)
        src = self.norm2(src)
        
        return src, attn_weights


class TransformerIDSModel(nn.Module):
    """Transformer入侵检测模型，集成特征编码器(LSTM或CNN)和稀疏注意力Transformer。
    
    完整的入侵检测模型架构：
    数据预处理 → 特征编码(LSTM或CNN) → 位置编码 → Transformer（Top-K稀疏多头注意力） → 分类

    
    Attributes:
        input_size (int): 输入特征维度
        hidden_size (int): LSTM/CNN隐藏层维度
        d_model (int): Transformer模型维度
        nhead (int): 注意力头数
        num_encoder_layers (int): Transformer编码器层数
        dim_feedforward (int): 前馈网络隐藏层维度
        num_classes (int): 分类类别数
        dropout (float): Dropout概率
        k_ratio (float): 稀疏注意力的Top-K比例
        encoder_type (str): 特征编码器类型，'lstm'或'cnn'
        use_position_encoding (bool): 是否使用位置编码
        feature_encoder (nn.Module): 特征编码器，LSTM或CNN
        transformer_layers (nn.ModuleList): Transformer编码器层列表
        classifier (nn.Linear): 分类器
    """
    
    def __init__(
        self, 
        input_size: int, 
        hidden_size: int = 128, 
        d_model: int = 128, 
        nhead: int = 4, 
        num_encoder_layers: int = 3, 
        dim_feedforward: int = 512, 
        num_classes: int = 2, 
        dropout: float = 0.1, 
        k_ratio: float = 0.4,
        encoder_type: Literal['lstm', 'cnn'] = 'lstm',
        cnn_kernel_size: int = 5,
        cnn_num_layers: int = 3,
        use_position_encoding: bool = True
    ) -> None:
        """初始化Transformer入侵检测模型。
        
        Args:
            input_size: 输入特征维度
            hidden_size: LSTM/CNN隐藏层维度
            d_model: Transformer模型维度
            nhead: 注意力头数
            num_encoder_layers: Transformer编码器层数
            dim_feedforward: 前馈网络隐藏层维度
            num_classes: 分类类别数
            dropout: Dropout概率
            k_ratio: 稀疏注意力的Top-K比例
            encoder_type: 特征编码器类型，'lstm'或'cnn'
            cnn_kernel_size: CNN编码器的卷积核大小
            cnn_num_layers: CNN编码器的卷积层数
            use_position_encoding: 是否使用位置编码，默认为True
        """
        super(TransformerIDSModel, self).__init__()
        
        # 确保 d_model 能被 nhead 整除
        assert d_model % nhead == 0, f"d_model({d_model})必须能被nhead({nhead})整除"
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.d_model = d_model
        self.nhead = nhead
        self.num_encoder_layers = num_encoder_layers
        self.dim_feedforward = dim_feedforward
        self.num_classes = num_classes
        self.dropout = dropout
        self.k_ratio = k_ratio
        self.encoder_type = encoder_type
        self.use_position_encoding = use_position_encoding
        
        # 特征编码器 (LSTM或CNN)
        if encoder_type == 'lstm':
            self.feature_encoder = LSTMEncoder(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=2,
                dropout=dropout,
                bidirectional=True
            )
        else:  # encoder_type == 'cnn'
            self.feature_encoder = CNNEncoder(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=cnn_num_layers,
                kernel_size=cnn_kernel_size,
                dropout=dropout
            )
        
        # 特征编码器输出的特征维度
        encoder_output_size = self.feature_encoder.output_size
        
        # 如果特征编码器输出维度与Transformer模型维度不同，添加投影层
        self.projection = nn.Linear(encoder_output_size, d_model) if encoder_output_size != d_model else None
        
        # 位置编码器（只有当use_position_encoding=True时才创建）
        self.pos_encoder = PositionalEncoding(d_model, dropout) if use_position_encoding else None
        
        # Transformer编码器层
        self.transformer_layers = nn.ModuleList([
            TransformerEncoderLayer(
                d_model=d_model,
                nhead=nhead,
                dim_feedforward=dim_feedforward,
                dropout=dropout,
                k_ratio=k_ratio
            ) for _ in range(num_encoder_layers)
        ])
        
        # 分类器
        self.classifier = nn.Linear(d_model, num_classes)
        
        # 初始化参数
        self._init_parameters()
        
    def _init_parameters(self) -> None:
        """初始化模型参数。"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(
        self, 
        x: torch.Tensor, 
        attention_mask: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """前向传播。
        
        Args:
            x: 输入序列，形状为 [batch_size, seq_len, input_size]
            attention_mask: 注意力掩码，形状为 [batch_size, seq_len]
                1表示有效位置，0表示填充位置
            
        Returns:
            dict: 包含以下键:
                'logits': 分类logits，形状为 [batch_size, num_classes]
                'attention_weights': 最后一层的注意力权重，
                    形状为 [batch_size, nhead, seq_len, seq_len]
        """
    
        
        batch_size, seq_len = x.size(0), x.size(1)
        
        
        # 转换attention_mask为Transformer需要的格式
        if attention_mask is not None:
                
            # 将mask从[batch_size, seq_len]转换为[batch_size, 1, 1, seq_len]
            attention_mask = attention_mask.unsqueeze(1).unsqueeze(2)
            # 将0转换为-inf，1保持不变
            attention_mask = (1.0 - attention_mask) * -10000.0
        
        # 特征编码 (LSTM或CNN)
        encoder_output = self.feature_encoder(x)
        # 当特征编码器（如 LSTM）返回 (output, hidden) 元组时，仅保留 output
        if isinstance(encoder_output, tuple):
            encoder_output = encoder_output[0]

        # 若存在维度对齐层，将特征投影到 d_model 维度
        if self.projection is not None:
            encoder_output = self.projection(encoder_output)

        # 位置编码
        src = self.pos_encoder(encoder_output) if self.pos_encoder is not None else encoder_output

        
        # 依次通过Transformer编码器层
        attention_weights_list = []
        try:
            for i, layer in enumerate(self.transformer_layers):
                src, attn_weights = layer(src, attention_mask)
                attention_weights_list.append(attn_weights)
        except Exception as e:
            print(f"Transformer层错误: {e}，输入形状: {src.shape}，层索引: {i if 'i' in locals() else 'unknown'}")
            raise
        
        # 获取序列的表示（使用平均池化）
        # 如果有attention_mask，只对有效位置进行平均
        if attention_mask is not None:
            # 转换mask为[batch_size, seq_len]，1表示有效，0表示填充
            # 原始的 attention_mask 是 [batch_size, 1, 1, seq_len]，其中 0 表示有效，-10000.0 表示填充
            mask = (attention_mask.squeeze(1).squeeze(1) == 0).float()
            # 计算有效长度（用于平均）
            valid_lengths = torch.sum(mask, dim=1, keepdim=True)
            # 确保 valid_lengths 不为 0，避免除零错误
            valid_lengths = torch.clamp(valid_lengths, min=1.0)
            # 对有效位置求和后除以有效长度
            pooled = torch.sum(src * mask.unsqueeze(-1), dim=1) / valid_lengths
        else:
            # 如果没有mask，对整个序列进行平均
            pooled = torch.mean(src, dim=1)

        
        # 分类
        logits = self.classifier(pooled)

        
        return {
            'logits': logits,
            'attention_weights': attention_weights_list[-1]  # 返回最后一层的注意力权重
        }
        
    def predict(self, x: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """预测样本的类别。
        
        Args:
            x: 输入序列，形状为 [batch_size, seq_len, input_size]
            attention_mask: 注意力掩码，形状为 [batch_size, seq_len]
        
        Returns:
            torch.Tensor: 预测的类别索引，形状为 [batch_size]
        """
        outputs = self.forward(x, attention_mask)
        return torch.argmax(outputs['logits'], dim=1) 