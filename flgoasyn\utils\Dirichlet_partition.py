"""
自定义的Dirichlet数据划分器实现。

该模块提供了针对联邦学习场景的Dirichlet数据划分算法的优化实现，
允许更灵活地控制数据分布的不平衡度和算法的收敛条件。
"""
import numpy as np
from typing import List, Dict, Tuple, Optional, Union, Any
import flgo.benchmark.partition as fbp
import logging

class CustomDirichletPartitioner(fbp.BasicPartitioner):
    """自定义的Dirichlet数据划分器，提供更灵活的控制和更高效的实现。
    
    该划分器使用Dirichlet分布对数据进行非IID划分，使用一次性分配的方式，
    无需迭代计算，提高性能和稳定性。
    
    Attributes:
        num_clients: 客户端数量
        alpha: Dirichlet分布的浓度参数，控制数据非IID程度
        seed: 随机数种子
        logger: 日志记录器
    """
    
    def __init__(self, 
                 num_clients: int = 10, 
                 alpha: float = 0.5,
                 seed: Optional[int] = 42) -> None:
        """初始化Dirichlet划分器。
        
        Args:
            num_clients: 客户端数量
            alpha: Dirichlet分布的浓度参数，控制数据非IID程度
                  较小的alpha值会导致更加不平衡的数据分布
                  alpha=0时，每个客户端只有一个类别的数据
                  alpha→∞时，每个客户端的数据分布趋近于整体分布
            seed: 随机数种子，用于结果复现
        """
        self.num_clients = num_clients
        self.alpha = alpha
        self.seed = seed
        self.rng = np.random.RandomState(seed)
        self.logger = logging.getLogger("DirichletPartitioner")
    
    def __call__(self, data: Any) -> List[List[int]]:
        """对数据集进行Dirichlet分布的非IID划分。
        
        Args:
            data: 数据集，需要有标签信息
                 可以是Pytorch的Dataset对象或包含标签的数据结构
        
        Returns:
            List[List[int]]: 划分后的数据索引，每个内部列表对应一个客户端的数据索引
        """
        # 获取数据集大小和标签
        n_samples = len(data)
        
        # 获取数据集的标签，尝试不同的可能属性
        try:
            if hasattr(data, 'targets'):
                labels = data.targets
            elif hasattr(data, 'target'):
                labels = data.target
            elif hasattr(data, 'labels'):
                labels = data.labels
            elif hasattr(data, 'Label'):
                labels = data.labels
            elif hasattr(data, 'y'):
                labels = data.y
            elif isinstance(data, tuple) and len(data) == 2:
                # 假设是(特征, 标签)的元组形式
                labels = data[1]
            else:
                # 尝试获取标签列表
                labels = [data[i][1] for i in range(n_samples)]
        except Exception as e:
            self.logger.warning(f"无法直接获取标签，将使用随机划分: {str(e)}")
            # 如果无法获取标签，则使用随机划分
            return self._random_partition(n_samples)
        
        # 转换为numpy数组便于处理
        if not isinstance(labels, np.ndarray):
            labels = np.array(labels)
        
        # 获取唯一类别
        unique_classes = np.unique(labels)
        n_classes = len(unique_classes)
        
        # 计算每个类别的数据索引
        class_indices = {c: np.where(labels == c)[0] for c in unique_classes}
        
        # 生成每个客户端对每个类别的权重
        proportions = self.rng.dirichlet(alpha=[self.alpha] * n_classes, size=self.num_clients)
        
        # 初始化结果列表
        client_indices = [[] for _ in range(self.num_clients)]
        
        # 对每个类别进行分配
        for c_idx, c in enumerate(unique_classes):
            # 该类别的所有样本索引
            idxs = class_indices[c]
            self.rng.shuffle(idxs)  # 打乱顺序
            
            # 计算每个客户端应该获得的该类别的样本数
            prop = proportions[:, c_idx]
            prop = prop / np.sum(prop)  # 归一化确保总和为1
            num_samples_per_client = np.round(prop * len(idxs)).astype(np.int64)
            
            # 确保分配总数等于该类别的样本总数
            # 如果分配过多，则从样本数最多的客户端开始减少
            while np.sum(num_samples_per_client) > len(idxs):
                idx = np.argmax(num_samples_per_client)
                if num_samples_per_client[idx] > 0:
                    num_samples_per_client[idx] -= 1
            
            # 如果分配不足，则随机选择客户端增加
            while np.sum(num_samples_per_client) < len(idxs):
                idx = self.rng.choice(self.num_clients)
                num_samples_per_client[idx] += 1
            
            # 分配样本
            start_idx = 0
            for client_idx in range(self.num_clients):
                num = num_samples_per_client[client_idx]
                if num > 0:
                    client_indices[client_idx].extend(idxs[start_idx:start_idx + num].tolist())
                    start_idx += num
        
        # 确保每个客户端都有数据
        for client_idx in range(self.num_clients):
            if len(client_indices[client_idx]) == 0:
                # 随机从其他客户端借用一些数据
                for other_idx in range(self.num_clients):
                    if other_idx != client_idx and len(client_indices[other_idx]) > 1:
                        # 借用一半数据或至少一个样本
                        borrow_size = max(1, len(client_indices[other_idx]) // 4)
                        borrowed_indices = client_indices[other_idx][-borrow_size:]
                        client_indices[client_idx].extend(borrowed_indices)
                        client_indices[other_idx] = client_indices[other_idx][:-borrow_size]
                        if len(client_indices[client_idx]) > 0:
                            break
        
        # 打印每个客户端分到的样本数
        for i, indices in enumerate(client_indices):
            self.logger.info(f"客户端 {i} 分到 {len(indices)} 个样本")
        
        return client_indices
    
    def _random_partition(self, n_samples: int) -> List[List[int]]:
        """当无法获取标签时进行随机划分。
        
        Args:
            n_samples: 样本总数
            
        Returns:
            List[List[int]]: 随机划分的数据索引
        """
        # 生成所有样本的索引并随机打乱
        all_indices = np.random.permutation(n_samples)
        
        # 计算每个客户端大致应该分到的样本数
        samples_per_client = n_samples // self.num_clients
        remainder = n_samples % self.num_clients
        
        # 划分索引
        client_indices = []
        start_idx = 0
        for i in range(self.num_clients):
            # 前remainder个客户端多分配一个样本
            extra = 1 if i < remainder else 0
            end_idx = start_idx + samples_per_client + extra
            client_indices.append(all_indices[start_idx:end_idx].tolist())
            start_idx = end_idx
        
        return client_indices


class SampleNumDirichletPartitioner(fbp.BasicPartitioner):
    """基于样本数的Dirichlet划分器，更适合控制每个客户端的样本总数。
    
    该划分器首先根据Dirichlet分布确定每个客户端的样本总数，
    然后在每个客户端内部再次使用Dirichlet分布确定不同类别的样本比例。
    
    Attributes:
        num_clients: 客户端数量
        alpha_n: 控制客户端样本数分布的Dirichlet参数
        alpha_c: 控制每个客户端内类别分布的Dirichlet参数
        min_size: 每个客户端最少样本数
        seed: 随机数种子
    """
    
    def __init__(self, 
                 num_clients: int = 10, 
                 alpha_n: float = 1.0,
                 alpha_c: float = 0.5,
                 min_size: int = 10,
                 seed: Optional[int] = 42) -> None:
        """初始化基于样本数的Dirichlet划分器。
        
        Args:
            num_clients: 客户端数量
            alpha_n: 控制客户端样本数分布的Dirichlet参数
                    较小的值会导致客户端间样本数差异更大
            alpha_c: 控制每个客户端内类别分布的Dirichlet参数
                    较小的值会导致每个客户端内的类别分布更加偏斜
            min_size: 每个客户端最少样本数
            seed: 随机数种子，用于结果复现
        """
        self.num_clients = num_clients
        self.alpha_n = alpha_n
        self.alpha_c = alpha_c
        self.min_size = min_size
        self.seed = seed
        self.rng = np.random.RandomState(seed)
        self.logger = logging.getLogger("SampleNumDirichletPartitioner")
    
    def __call__(self, data: Any) -> List[List[int]]:
        """对数据集进行基于样本数的Dirichlet划分。
        
        Args:
            data: 数据集，需要有标签信息
        
        Returns:
            List[List[int]]: 划分后的数据索引
        """
        # 获取数据集大小和标签
        n_samples = len(data)
        
        # 获取数据集的标签
        try:
            if hasattr(data, 'targets'):
                labels = data.targets
            elif hasattr(data, 'target'):
                labels = data.target
            elif hasattr(data, 'labels'):
                labels = data.labels
            elif hasattr(data, 'y'):
                labels = data.y
            elif isinstance(data, tuple) and len(data) == 2:
                labels = data[1]
            else:
                labels = [data[i][1] for i in range(n_samples)]
        except Exception as e:
            self.logger.warning(f"无法获取标签，将使用随机划分: {str(e)}")
            return self._random_partition(n_samples)
        
        # 转换为numpy数组
        if not isinstance(labels, np.ndarray):
            labels = np.array(labels)
        
        # 获取唯一类别
        unique_classes = np.unique(labels)
        n_classes = len(unique_classes)
        
        # 计算每个类别的数据索引
        class_indices = {c: np.where(labels == c)[0] for c in unique_classes}
        
        # 第一步：确定每个客户端的样本总数
        # 使用Dirichlet分布生成样本数比例
        client_sample_ratios = self.rng.dirichlet(alpha=[self.alpha_n] * self.num_clients)
        
        # 计算每个客户端应分配的样本数，确保每个客户端至少有min_size个样本
        client_sample_counts = np.maximum(
            np.round(client_sample_ratios * n_samples).astype(np.int64),
            self.min_size
        )
        
        # 调整总样本数以匹配原始数据集大小
        total_assigned = np.sum(client_sample_counts)
        if total_assigned > n_samples:
            # 如果分配过多，减少样本数较多的客户端的分配
            diff = total_assigned - n_samples
            sorted_indices = np.argsort(-client_sample_counts)
            for i in range(diff):
                idx = sorted_indices[i % self.num_clients]
                if client_sample_counts[idx] > self.min_size:
                    client_sample_counts[idx] -= 1
        
        # 第二步：为每个客户端分配不同类别的样本
        client_indices = [[] for _ in range(self.num_clients)]
        
        # 对每个客户端
        for client_idx in range(self.num_clients):
            # 该客户端需要的样本总数
            client_size = client_sample_counts[client_idx]
            
            # 使用Dirichlet分布确定该客户端各类别的比例
            class_ratios = self.rng.dirichlet(alpha=[self.alpha_c] * n_classes)
            
            # 计算该客户端各类别的样本数
            client_class_counts = np.round(class_ratios * client_size).astype(np.int64)
            
            # 确保总数等于client_size
            while np.sum(client_class_counts) != client_size:
                if np.sum(client_class_counts) < client_size:
                    # 随机选择一个类别增加1个样本
                    idx = self.rng.choice(n_classes)
                    client_class_counts[idx] += 1
                else:
                    # 随机选择一个类别减少1个样本
                    valid_idx = np.where(client_class_counts > 0)[0]
                    if len(valid_idx) > 0:
                        # 使用简单的均匀采样，避免可能的概率计算错误
                        idx = self.rng.choice(valid_idx)
                        client_class_counts[idx] -= 1
            
            # 为该客户端分配各类别的样本
            for c_idx, c in enumerate(unique_classes):
                c_count = client_class_counts[c_idx]
                if c_count <= 0:
                    continue
                
                # 从该类别中随机选择c_count个样本
                available_indices = [idx for idx in class_indices[c] if idx not in [ind for sublist in client_indices for ind in sublist]]
                if len(available_indices) < c_count:
                    # 如果该类别可用样本不足，则减少分配
                    c_count = len(available_indices)
                
                if c_count > 0:
                    # 转换为numpy数组以便使用choice函数
                    available_indices = np.array(available_indices)
                    # 确保c_count不超过可用样本数
                    c_count = min(c_count, len(available_indices))
                    if c_count > 0:
                        selected_indices = self.rng.choice(available_indices, size=c_count, replace=False)
                        client_indices[client_idx].extend(selected_indices)
        
        # 确保每个客户端都有数据
        for client_idx in range(self.num_clients):
            if len(client_indices[client_idx]) == 0:
                # 从其他客户端借用数据
                for other_idx in range(self.num_clients):
                    if other_idx != client_idx and len(client_indices[other_idx]) > 1:
                        borrow_size = max(1, len(client_indices[other_idx]) // 4)
                        borrowed_indices = client_indices[other_idx][-borrow_size:]
                        client_indices[client_idx].extend(borrowed_indices)
                        client_indices[other_idx] = client_indices[other_idx][:-borrow_size]
                        if len(client_indices[client_idx]) > 0:
                            break
        
        # 打印每个客户端分到的样本数
        for i, indices in enumerate(client_indices):
            self.logger.info(f"客户端 {i} 分到 {len(indices)} 个样本")
        
        return client_indices
    
    def _random_partition(self, n_samples: int) -> List[List[int]]:
        """当无法获取标签时进行随机划分。
        
        Args:
            n_samples: 样本总数
            
        Returns:
            List[List[int]]: 随机划分的数据索引
        """
        # 生成所有样本的索引并随机打乱
        all_indices = np.random.permutation(n_samples)
        
        # 计算每个客户端大致应该分到的样本数
        samples_per_client = n_samples // self.num_clients
        remainder = n_samples % self.num_clients
        
        # 划分索引
        client_indices = []
        start_idx = 0
        for i in range(self.num_clients):
            # 前remainder个客户端多分配一个样本
            extra = 1 if i < remainder else 0
            end_idx = start_idx + samples_per_client + extra
            client_indices.append(all_indices[start_idx:end_idx].tolist())
            start_idx = end_idx
        
        return client_indices


