from flgo.simulator.base import BasicSimulator
import numpy as np


class StaticUniSimulator(BasicSimulator):
    def initialize(self):
        # Uniform(600s, 10800s) is equal to Uniform(10min, 3h)
        self.client_time_response = {cid: self.random_module.randint(600, 10800) for cid in self.clients}
        self.set_variable(list(self.clients.keys()), 'latency', list(self.client_time_response.values()))

    def update_client_responsiveness(self, client_ids):
        latency = [self.client_time_response[cid] for cid in client_ids]
        self.set_variable(client_ids, 'latency', latency)


class DynamicUniSimulator(BasicSimulator):
    def initialize(self):
        # Uniform(600s, 10800s) is equal to Uniform(10min, 3h)
        client_time_response = {cid: self.random_module.randint(600, 10800) for cid in self.clients}
        self.set_variable(list(self.clients.keys()), 'latency', list(client_time_response.values()))

    def update_client_responsiveness(self, client_ids):
        client_time_response = {cid: self.random_module.randint(600, 10800) for cid in client_ids}
        self.set_variable(client_ids, 'latency', list(client_time_response.values()))



class ResponsivenessExampleSimulator(BasicSimulator):
    def initialize(self):
        self.client_time_response = {cid: np.random.randint(5, 1000) for cid in self.clients}
        self.set_variable(list(self.clients.keys()), 'latency', list(self.client_time_response.values()))

    def update_client_responsiveness(self, client_ids):
        latency = [self.client_time_response[cid] for cid in client_ids]
        self.set_variable(client_ids, 'latency', latency)


class CompletenessExampleSimulator(BasicSimulator):
    def update_client_completeness(self, client_ids):
        if not hasattr(self, '_my_working_amount'):
            rs = self.random_module.normal(1.0, 1.0, len(self.clients))
            rs = rs.clip(0.01, 2)
            self._my_working_amount = {cid:max(int(r*self.clients[cid].num_steps),1) for  cid,r in zip(self.clients, rs)}
        working_amount = [self._my_working_amount[cid] for cid in client_ids]
        self.set_variable(client_ids, 'working_amount', working_amount)

class AvailabilityExampleSimulator(BasicSimulator):
    def update_client_availability(self):
        if self.gv.clock.current_time==0:
            self.set_variable(self.all_clients, 'prob_available', [1.0 for _ in self.clients])
            self.set_variable(self.all_clients, 'prob_unavailable', [0.0 for _ in self.clients])
            return
        pa = [0.9 for _ in self.clients]
        pua = [0.9 for _ in self.clients]
        self.set_variable(self.all_clients, 'prob_available', pa)
        self.set_variable(self.all_clients, 'prob_unavailable', pua)

class ConnectivityExampleSimulator(BasicSimulator):
    def initialize(self):
        drop_probs = self.random_module.uniform(0.,0.05, len(self.clients)).tolist()
        self.client_drop_prob = {cid: dp for cid,dp in zip(self.clients, drop_probs)}

    def update_client_connectivity(self, client_ids):
        self.set_variable(client_ids, 'prob_drop', [self.client_drop_prob[cid] for cid in client_ids])


# print("simulator/__init__.py 被加载")

# class Simulator(BasicSimulator):
#     def update_client_connectivity(self, client_ids):
#         probs = [0.1 for _ in client_ids]
#         self.set_variable(client_ids, 'prob_drop', probs)

#     def update_client_availability(self):
#         self.roundwise_fixed_availability = True
#         pa = [0.9 for _ in self.clients]
#         pua = [0.1 for _ in self.clients]
#         self.set_variable(self.all_clients, 'prob_available', pa)
#         self.set_variable(self.all_clients, 'prob_unavailable', pua)

#     def update_client_responsiveness(self, client_ids, *args, **kwargs):
#         latency = [np.random.randint(5,100) for _ in client_ids]
#         self.set_variable(client_ids, 'latency', latency)

#     def update_client_completeness(self, client_ids, *args, **kwargs):
#         working_amount = [max(int(self.clients[cid].num_steps*np.random.rand()), 1) for cid in client_ids]
#         self.set_variable(client_ids, 'working_amount', working_amount)


# availability_modes = {
#     'IDL': ideal_client_availability,
#     'YMF': y_max_first_client_availability,
#     'MDF': more_data_first_client_availability,
#     'LDF': less_data_first_client_availability,
#     'YFF': y_fewer_first_client_availability,
#     'HOMO': homogeneous_client_availability,
#     'LN': lognormal_client_availability,
#     'SLN': sin_lognormal_client_availability,
#     'YC': y_cycle_client_availability,
# }

# connectivity_modes = {
#     'IDL': ideal_client_connectivity,
#     'HOMO': homogeneous_client_connectivity,
# }

# completeness_modes = {
#     'IDL': ideal_client_completeness,
#     'PDU': part_dynamic_uniform_client_completeness,
#     'FSU': full_static_unifrom_client_completeness,
#     'ADU': arbitrary_dynamic_unifrom_client_completeness,
#     'ASU': arbitrary_static_unifrom_client_completeness,
# }

# responsiveness_modes = {
#     'IDL': ideal_client_responsiveness,
#     'LN': lognormal_client_responsiveness,
#     'UNI': uniform_client_responsiveness,
# }