<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PFLlib</title>
    <link rel="icon" href="imgs/logo-green.png" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            background-color: #f4f4f4;
            color: #333333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .navbar {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 1rem 0rem;
            position: fixed;
            width: 100%;
            z-index: 1000;
            transition: background-color 0.3s ease;
            height: 2rem;
        }
        .navbar.scrolled {
            background-color: rgba(0, 0, 0, 0.7);
        }
        .navbar-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: 1200px;
        }
        .navbar h1 {
            margin: 0;
            color: white;
        }
        .navbar nav {
            display: flex;
            gap: 1rem;
        }
        .navbar a {
            color: white;
            text-decoration: none;
            transition: color 0.3s ease;
            padding: 0rem 1rem;
        }
        .navbar a:hover {
            color: #6DA945;
        }
        .container {
            max-width: 1200px;
            margin: 8rem auto 2rem; /* Adjusted margin for container */
            padding: 0 2rem;
            flex-grow: 1; /* Ensures container takes up remaining space */
            display: flex;
        }
        .sidebar {
            width: 15rem;
            padding-right: 2rem;
            box-sizing: border-box;
        }
        .sidebar ul {
            list-style-type: none;
            padding: 0;
        }
        .sidebar li {
            margin-bottom: 0.5rem;
        }
        .sidebar a {
            color: #6DA945;
            text-decoration: none;
            font-weight: bold;
            transition: color 0.3s ease;
        }
        .sidebar a:hover {
            color: #2c6307;
        }
        .content {
            width: 75%;
            box-sizing: border-box;
        }
        h1, h2, h3 {
            color: #333333;
        }
        section {
            margin-bottom: 2rem;
        }
        pre {
            background-color: #f9f9f9;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        code {
            font-family: "Courier New", Courier, monospace;
            background-color: #f5f5f5;
            border-radius: 3px;
            padding: 2px 4px;
            color: #6DA945;
            font-weight: bold;
            font-weight: bold;
        }
        pre {
            font-family: "Courier New", Courier, monospace;
            background-color: #f5f5f5;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 1rem 0;
            position: relative;
            width: 100%;
        }
        html {
            scroll-padding-top: 4.5rem; /* Adjust to the height of your navbar */
        }
        a {
            text-decoration: none;
            color: #6DA945;
        }

        .hamburger {
            display: none;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            position: absolute;
            right: 1rem;
        }

        .hamburger span {
            display: block;
            width: 20px;
            height: 3px;
            background: white;
            margin: 5px 0;
            transition: 0.3s;
        }

        @media (max-width: 768px) {
            .container {
                max-width: 100%;
                flex-direction: column;
                margin-top: 6rem;
            }
            .sidebar, .content {
                width: 100%;
            }

            .navbar-container {
                flex-direction: row;
                flex-wrap: wrap;
            }

            .hamburger {
                display: block;
            }

            .navbar nav {
                display: none;
                flex-direction: column;
                width: 100%;
                background: #333333;
                padding: 1rem;
                margin-top: 2rem;
            }

            .navbar nav.active {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <div class="navbar">
        <div class="navbar-container">
            <h1><img src="imgs/logo-green.png" alt="icon" height="36" style="vertical-align: sub; margin-left: 10pt;"/><a href="index.html" id="PFLlib">PFLlib</a></h1>
            <button class="hamburger" aria-label="Menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <nav>
                <a href="index.html">Home</a>
                <a href="docs.html">Documentation</a>
                <a href="benchmark.html">Benchmark</a>
                <a href="about.html">About</a>
                <a href="https://github.com/TsingZ0/PFLlib" id="github-stars" class="github-stars">★ Star 1500</a>
            </nav>
        </div>
    </div>
    <div class="container">
        <div class="sidebar">
            <ul>
                <li><a href="quickstart.html">Quick Start</a></li>
                <li><a href="algo.html">FL Algorithms</a></li>
                <li><a href="data.html">Datasets & Scenarios</a></li>
                <li><a href="model.html">Models</a></li>
                <li><a href="extend.html">Easy to Extend</a></li>
                <li><a href="features.html">Other Features</a></li>
            </ul>
        </div>
        <div class="content">
            <section id="features">
                <h2 id="privacy-evaluation">Privacy Evaluation</h2>
                <p>You can use the following privacy evaluation methods to assess the privacy-preserving capabilities of tFL/pFL algorithms in PFLlib. Please refer to <code>./system/flcore/servers/serveravg.py</code> for an example. Note that most of these evaluations are not typically considered in the original papers. <em>We encourage you to add more attacks and metrics for privacy evaluation.</em></p>
                <h4>Currently supported attacks:</h4>
                <ul>
                    <li><a href="https://www.ijcai.org/proceedings/2022/0324.pdf">DLG (Deep Leakage from Gradients)</a> attack</li>
                </ul>
                <h4>Currently supported metrics:</h4>
                <ul>
                    <li><strong>PSNR (Peak Signal-to-Noise Ratio)</strong>: an objective metric for image evaluation, defined as the logarithm of the ratio of the squared maximum value of RGB image fluctuations to the Mean Squared Error (MSE) between two images. A lower PSNR score indicates better privacy-preserving capabilities.</li>
                </ul>

                <h2 id="systematical-research-supprot">Systematical research support</h2>
                <p>To simulate Federated Learning (FL) under practical conditions, such as <strong>client dropout</strong>, <strong>slow trainers</strong>, <strong>slow senders</strong>, and <strong>network TTL (Time-To-Live)</strong>, you can adjust the following parameters:</p>
                <ul>
                <li><code>-cdr</code>: Dropout rate for clients. Clients are randomly dropped at each training round based on this rate.</li>
                <li><code>-tsr</code> and <code>-ssr</code>: Slow trainer and slow sender rates, respectively. These parameters define the proportion of clients that will behave as slow trainers or slow senders. Once a client is selected as a "slow trainer" or "slow sender," it will consistently train/send slower than other clients.</li>
                <li><code>-tth</code>: Threshold for network TTL in milliseconds.</li>
                </ul>
                <p>Thanks to <a href="https://github.com/Stonesjtu/pytorch_memlab/blob/d590c489236ee25d157ff60ecd18433e8f9acbe3/pytorch_memlab/mem_reporter.py#L185" target="_blank">@Stonesjtu</a>, this library can also record the <strong>GPU memory usage</strong> for the model.</p>

            </section>
        </div>
    </div>
    <footer>
        <p>&copy; 2025 PFLlib. All rights reserved.</p>
    </footer>
    <script>
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        async function fetchGitHubStars() {
            try {
                const response = await fetch('https://api.github.com/repos/TsingZ0/PFLlib');
                if (!response.ok) throw new Error('Network response was not ok');
                const data = await response.json();
                document.getElementById('github-stars').textContent = `★ Star ${data.stargazers_count}`;
            } catch (error) {
                console.error('Failed to fetch GitHub stars:', error);
                document.getElementById('github-stars').textContent = '★ Star 1500';
            }
        }
        fetchGitHubStars();

        document.querySelector('.hamburger').addEventListener('click', function() {
            this.classList.toggle('active');
            document.querySelector('.navbar nav').classList.toggle('active');
        });

        document.addEventListener('click', function(e) {
            if (!e.target.closest('.navbar-container')) {
                document.querySelector('.navbar nav').classList.remove('active');
                document.querySelector('.hamburger').classList.remove('active');
            }
        });
    </script>
</body>
</html>
