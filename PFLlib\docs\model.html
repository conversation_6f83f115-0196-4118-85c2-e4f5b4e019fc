<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PFLlib</title>
    <link rel="icon" href="imgs/logo-green.png" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            background-color: #f4f4f4;
            color: #333333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .navbar {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 1rem 0rem;
            position: fixed;
            width: 100%;
            z-index: 1000;
            transition: background-color 0.3s ease;
            height: 2rem;
        }
        .navbar.scrolled {
            background-color: rgba(0, 0, 0, 0.7);
        }
        .navbar-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: 1200px;
        }
        .navbar h1 {
            margin: 0;
            color: white;
        }
        .navbar nav {
            display: flex;
            gap: 1rem;
        }
        .navbar a {
            color: white;
            text-decoration: none;
            transition: color 0.3s ease;
            padding: 0rem 1rem;
        }
        .navbar a:hover {
            color: #6DA945;
        }
        .container {
            max-width: 1200px;
            margin: 8rem auto 2rem; /* Adjusted margin for container */
            padding: 0 2rem;
            flex-grow: 1; /* Ensures container takes up remaining space */
            display: flex;
        }
        .sidebar {
            width: 15rem;
            padding-right: 2rem;
            box-sizing: border-box;
        }
        .sidebar ul {
            list-style-type: none;
            padding: 0;
        }
        .sidebar li {
            margin-bottom: 0.5rem;
        }
        .sidebar a {
            color: #6DA945;
            text-decoration: none;
            font-weight: bold;
            transition: color 0.3s ease;
        }
        .sidebar a:hover {
            color: #2c6307;
        }
        .content {
            width: 75%;
            box-sizing: border-box;
        }
        h1, h2, h3 {
            color: #333333;
        }
        section {
            margin-bottom: 2rem;
        }
        pre {
            background-color: #f9f9f9;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        code {
            font-family: "Courier New", Courier, monospace;
            background-color: #f5f5f5;
            border-radius: 3px;
            padding: 2px 4px;
            color: #6DA945;
            font-weight: bold;
            font-weight: bold;
        }
        pre {
            font-family: "Courier New", Courier, monospace;
            background-color: #f5f5f5;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 1rem 0;
            position: relative;
            width: 100%;
        }
        html {
            scroll-padding-top: 4.5rem; /* Adjust to the height of your navbar */
        }
        a {
            text-decoration: none;
            color: #6DA945;
        }

        .hamburger {
            display: none;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            position: absolute;
            right: 1rem;
        }

        .hamburger span {
            display: block;
            width: 20px;
            height: 3px;
            background: white;
            margin: 5px 0;
            transition: 0.3s;
        }

        @media (max-width: 768px) {
            .container {
                max-width: 100%;
                flex-direction: column;
                margin-top: 6rem;
            }
            .sidebar, .content {
                width: 100%;
            }

            .navbar-container {
                flex-direction: row;
                flex-wrap: wrap;
            }

            .hamburger {
                display: block;
            }

            .navbar nav {
                display: none;
                flex-direction: column;
                width: 100%;
                background: #333333;
                padding: 1rem;
                margin-top: 2rem;
            }

            .navbar nav.active {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <div class="navbar">
        <div class="navbar-container">
            <h1><img src="imgs/logo-green.png" alt="icon" height="36" style="vertical-align: sub; margin-left: 10pt;"/><a href="index.html" id="PFLlib">PFLlib</a></h1>
            <button class="hamburger" aria-label="Menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <nav>
                <a href="index.html">Home</a>
                <a href="docs.html">Documentation</a>
                <a href="benchmark.html">Benchmark</a>
                <a href="about.html">About</a>
                <a href="https://github.com/TsingZ0/PFLlib" id="github-stars" class="github-stars">★ Star 1500</a>
            </nav>
        </div>
    </div>
    <div class="container">
        <div class="sidebar">
            <ul>
                <li><a href="quickstart.html">Quick Start</a></li>
                <li><a href="algo.html">FL Algorithms</a></li>
                <li><a href="data.html">Datasets & Scenarios</a></li>
                <li><a href="model.html">Models</a></li>
                <li><a href="extend.html">Easy to Extend</a></li>
                <li><a href="features.html">Other Features</a></li>
            </ul>
        </div>
        <div class="content">
            <section id="model">
                <h2>Models</h2>
                <p>We currently support multiple models for various datasets, some of which include pre-trained model weights. Please refer to the code for more details.</p>
                <ul>
                <li>for MNIST and Fashion-MNIST
                    <ul>
                    <li>Mclr_Logistic(1*28*28) # convex</li>
                    <li>LeNet()</li>
                    <li>DNN(1*28*28, 100)</li>
                    </ul>
                </li>

                <li>for Cifar10, Cifar100 and Tiny-ImageNet
                    <ul>
                    <li>Mclr_Logistic(3*32*32) # convex</li>
                    <li>FedAvgCNN()</li>
                    <li>DNN(3*32*32, 100)</li>
                    <li>ResNet18, AlexNet, MobileNet, GoogleNet, etc.</li>
                    </ul>
                </li>

                <li>for AG_News and Sogou_News
                    <ul>
                    <li>LSTM()</li>
                    <li>fastText() in <a href="https://aclanthology.org/E17-2068/">Bag of Tricks for Efficient Text Classification</a></li>
                    <li>TextCNN() in <a href="https://aclanthology.org/D14-1181/">Convolutional Neural Networks for Sentence Classification</a></li>
                    <li>TransformerModel() in <a href="https://proceedings.neurips.cc/paper/2017/hash/3f5ee243547dee91fbd053c1c4a845aa-Abstract.html">Attention is all you need</a></li>
                    </ul>
                </li>

                <li>for AmazonReview
                    <ul>
                    <li>AmazonMLP() in <a href="https://link.springer.com/chapter/10.1007/978-3-030-58568-6_36">Curriculum manager for source selection in multi-source domain adaptation</a></li>
                    </ul>
                </li>

                <li>for Omniglot
                    <ul>
                    <li>FedAvgCNN()</li>
                    </ul>
                </li>

                <li>for HAR and PAMAP
                    <ul>
                    <li>HARCNN() in <a href="https://eudl.eu/pdf/10.4108/icst.mobicase.2014.257786">Convolutional neural networks for human activity recognition using mobile sensors</a></li>
                    </ul>
                </li>
                </ul>
            </section>
        </div>
    </div>
    <footer>
        <p>&copy; 2025 PFLlib. All rights reserved.</p>
    </footer>
    <script>
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        async function fetchGitHubStars() {
            try {
                const response = await fetch('https://api.github.com/repos/TsingZ0/PFLlib');
                if (!response.ok) throw new Error('Network response was not ok');
                const data = await response.json();
                document.getElementById('github-stars').textContent = `★ Star ${data.stargazers_count}`;
            } catch (error) {
                console.error('Failed to fetch GitHub stars:', error);
                document.getElementById('github-stars').textContent = '★ Star 1500';
            }
        }
        fetchGitHubStars();

        document.querySelector('.hamburger').addEventListener('click', function() {
            this.classList.toggle('active');
            document.querySelector('.navbar nav').classList.toggle('active');
        });

        document.addEventListener('click', function(e) {
            if (!e.target.closest('.navbar-container')) {
                document.querySelector('.navbar nav').classList.remove('active');
                document.querySelector('.hamburger').classList.remove('active');
            }
        });
    </script>
</body>
</html>
