<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PFLlib</title>
    <link rel="icon" href="imgs/logo-green.png" type="image/x-icon">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            background-color: #f4f4f4;
            color: #333333;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .navbar {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 1rem 0rem;
            position: fixed;
            width: 100%;
            z-index: 1000;
            transition: background-color 0.3s ease;
            height: 2rem;
        }
        .navbar.scrolled {
            background-color: rgba(0, 0, 0, 0.7);
        }
        .navbar-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            max-width: 1200px;
        }
        .navbar h1 {
            margin: 0;
            color: white;
        }
        .navbar nav {
            display: flex;
            gap: 1rem;
        }
        .navbar a {
            color: white;
            text-decoration: none;
            transition: color 0.3s ease;
            padding: 0rem 1rem;
        }
        .navbar a:hover {
            color: #6DA945;
        }
        .container {
            max-width: 1200px;
            margin: 8rem auto 2rem; /* Adjusted margin for container */
            padding: 0 2rem;
            flex-grow: 1; /* Ensures container takes up remaining space */
            display: flex;
        }
        .sidebar {
            width: 15rem;
            padding-right: 2rem;
            box-sizing: border-box;
        }
        .sidebar ul {
            list-style-type: none;
            padding: 0;
        }
        .sidebar li {
            margin-bottom: 0.5rem;
        }
        .sidebar a {
            color: #6DA945;
            text-decoration: none;
            font-weight: bold;
            transition: color 0.3s ease;
        }
        .sidebar a:hover {
            color: #2c6307;
        }
        .content {
            width: 75%;
            box-sizing: border-box;
        }
        h1, h2, h3 {
            color: #333333;
        }
        section {
            margin-bottom: 2rem;
        }
        pre {
            background-color: #f9f9f9;
            padding: 1rem;
            border-radius: 5px;
            overflow-x: auto;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        code {
            font-family: "Courier New", Courier, monospace;
            background-color: #f5f5f5;
            border-radius: 3px;
            padding: 2px 4px;
            color: #6DA945;
            font-weight: bold;
            font-weight: bold;
        }
        pre {
            font-family: "Courier New", Courier, monospace;
            background-color: #f5f5f5;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 1rem 0;
            position: relative;
            width: 100%;
        }
        html {
            scroll-padding-top: 4.5rem; /* Adjust to the height of your navbar */
        }
        a {
            text-decoration: none;
            color: #6DA945;
        }

        .hamburger {
            display: none;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            position: absolute;
            right: 1rem;
        }

        .hamburger span {
            display: block;
            width: 20px;
            height: 3px;
            background: white;
            margin: 5px 0;
            transition: 0.3s;
        }

        @media (max-width: 768px) {
            .container {
                max-width: 100%;
                flex-direction: column;
                margin-top: 6rem;
            }
            .sidebar, .content {
                width: 100%;
            }

            .navbar-container {
                flex-direction: row;
                flex-wrap: wrap;
            }

            .hamburger {
                display: block;
            }

            .navbar nav {
                display: none;
                flex-direction: column;
                width: 100%;
                background: #333333;
                padding: 1rem;
                margin-top: 2rem;
            }

            .navbar nav.active {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <div class="navbar">
        <div class="navbar-container">
            <h1><img src="imgs/logo-green.png" alt="icon" height="36" style="vertical-align: sub; margin-left: 10pt;"/><a href="index.html" id="PFLlib">PFLlib</a></h1>
            <button class="hamburger" aria-label="Menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <nav>
                <a href="index.html">Home</a>
                <a href="docs.html">Documentation</a>
                <a href="benchmark.html">Benchmark</a>
                <a href="about.html">About</a>
                <a href="https://github.com/TsingZ0/PFLlib" id="github-stars" class="github-stars">★ Star 1500</a>
            </nav>
        </div>
    </div>
    <div class="container">
        <div class="sidebar">
            <ul>
                <li><a href="quickstart.html">Quick Start</a></li>
                <li><a href="algo.html">FL Algorithms</a></li>
                <li><a href="data.html">Datasets & Scenarios</a></li>
                <li><a href="model.html">Models</a></li>
                <li><a href="extend.html">Easy to Extend</a></li>
                <li><a href="features.html">Other Features</a></li>
            </ul>
        </div>
        <div class="content">
            <section id="extend">
                <h2>Easy to Extend</h2>

                <p>This library is designed to be easily extendable with new algorithms and datasets. Here’s how you can add them:</p>
                
                <ul>
                  <li>
                    <strong>New Dataset</strong>: To add a new dataset, simply create a <code>generate_DATA.py</code> file in <code>./dataset</code> and then write the download code and use the 
                    <a href="https://github.com/TsingZ0/PFLlib/tree/master/dataset/utils">utils</a> 
                    as shown in <code>./dataset/generate_MNIST.py</code> (you can consider it as a template):
                    <pre>
# `generate_DATA.py`
import necessary pkgs
from utils import necessary processing funcs

def generate_dataset(...):
    # download dataset as usual
    # pre-process dataset as usual
    X, y, statistic = separate_data((dataset_content, dataset_label), ...)
    train_data, test_data = split_data(X, y)
    save_file(config_path, train_path, test_path, train_data, test_data, statistic, ...)

# call the generate_dataset func</pre>
                  </li>
                
                  <li>
                    <strong>New Algorithm</strong>: To add a new algorithm, extend the base classes <strong>Server</strong> and 
                    <strong>Client</strong>, which are defined in 
                    <code>./system/flcore/servers/serverbase.py</code> and 
                    <code>./system/flcore/clients/clientbase.py</code>, respectively.
                    <ul>
                      <li>Server
                        <pre>
# serverNAME.py
import necessary pkgs
from flcore.clients.clientNAME import clientNAME
from flcore.servers.serverbase import Server

class NAME(Server):
    def __init__(self, args, times):
        super().__init__(args, times)

        # select slow clients
        self.set_slow_clients()
        self.set_clients(clientAVG)
    def train(self):
        # server scheduling code of your algorithm</pre>
                      </li>
                      <li>Client
                        <pre>
# clientNAME.py
import necessary pkgs
from flcore.clients.clientbase import Client

class clientNAME(Client):
    def __init__(self, args, id, train_samples, test_samples, **kwargs):
        super().__init__(args, id, train_samples, test_samples, **kwargs)
        # add specific initialization
    
    def train(self):
        # client training code of your algorithm</pre>
                      </li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>New Model</strong>: To add a new model, simply include it in 
                    <code>./system/flcore/trainmodel/models.py</code>.
                  </li>
                
                  <li>
                    <strong>New Optimizer</strong>: If you need a new optimizer for training, add it to 
                    <code>./system/flcore/optimizers/fedoptimizer.py</code>.
                  </li>
                
                  <li>
                    <strong>New Benchmark Platform or Library</strong>: Our code framework is flexible, allowing users to build 
                    custom platforms or libraries for specific applications, such as 
                    <a href="https://github.com/TsingZ0/FL-IoT">FL-IoT</a> and 
                    <a href="https://github.com/TsingZ0/HtFLlib">HtFLlib</a>.
                  </li>
                </ul>                
            </section>
        </div>
    </div>
    <footer>
        <p>&copy; 2025 PFLlib. All rights reserved.</p>
    </footer>
    <script>
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        async function fetchGitHubStars() {
            try {
                const response = await fetch('https://api.github.com/repos/TsingZ0/PFLlib');
                if (!response.ok) throw new Error('Network response was not ok');
                const data = await response.json();
                document.getElementById('github-stars').textContent = `★ Star ${data.stargazers_count}`;
            } catch (error) {
                console.error('Failed to fetch GitHub stars:', error);
                document.getElementById('github-stars').textContent = '★ Star 1500';
            }
        }
        fetchGitHubStars();

        document.querySelector('.hamburger').addEventListener('click', function() {
            this.classList.toggle('active');
            document.querySelector('.navbar nav').classList.toggle('active');
        });

        document.addEventListener('click', function(e) {
            if (!e.target.closest('.navbar-container')) {
                document.querySelector('.navbar nav').classList.remove('active');
                document.querySelector('.hamburger').classList.remove('active');
            }
        });
    </script>
</body>
</html>
