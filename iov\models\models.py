import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import copy
from typing import Tuple, List, Optional, Union
from loguru import logger

class EfficientAttention(nn.Module):
    """高效注意力机制"""
    
    def __init__(self, dim: int, num_heads: int = 8, qkv_bias: bool = False, attn_drop: float = 0., proj_drop: float = 0.):
        super().__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5
        
        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        # 高效注意力计算
        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x

class TransformerEncoderLayer(nn.Module):
    """Transformer编码器层"""
    
    def __init__(self, d_model: int, nhead: int, dim_feedforward: int = 2048, dropout: float = 0.1):
        super().__init__()
        self.self_attn = EfficientAttention(d_model, num_heads=nhead, attn_drop=dropout, proj_drop=dropout)
        
        # 前馈网络
        self.linear1 = nn.Linear(d_model, dim_feedforward)
        self.dropout = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward, d_model)
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout1 = nn.Dropout(dropout)
        self.dropout2 = nn.Dropout(dropout)
        
        self.activation = F.gelu
        
    def forward(self, src: torch.Tensor, src_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        src2 = self.norm1(src)
        src2 = self.self_attn(src2)
        src = src + self.dropout1(src2)
        
        src2 = self.norm2(src)
        src2 = self.linear2(self.dropout(self.activation(self.linear1(src2))))
        src = src + self.dropout2(src2)
        
        return src

class CNNBlock(nn.Module):
    """卷积神经网络块，用于从序列数据中提取多尺度局部特征"""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_sizes: List[int] = [3, 5, 7], dropout: float = 0.1):
        """
        初始化CNN模块
        
        Args:
            in_channels: 输入通道数
            out_channels: 输出通道数
            kernel_sizes: 不同尺度的卷积核大小列表
            dropout: Dropout比率
        """
        super().__init__()
        
        # 多尺度卷积层
        self.convs = nn.ModuleList([
            nn.Conv1d(
                in_channels=in_channels,
                out_channels=out_channels // len(kernel_sizes),
                kernel_size=k,
                padding=(k - 1) // 2
            ) for k in kernel_sizes
        ])
        
        self.pool = nn.AdaptiveMaxPool1d(1)  # 全局最大池化
        self.dropout = nn.Dropout(dropout)
        self.norm = nn.BatchNorm1d(out_channels)
        self.activation = nn.ReLU()
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播函数
        
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, in_channels]
            
        Returns:
            输出张量，形状为 [batch_size, seq_len, out_channels]
        """
        # 转换输入形状以适应卷积操作 [batch_size, seq_len, channels] -> [batch_size, channels, seq_len]
        x = x.transpose(1, 2)
        
        # 多尺度特征提取
        conv_results = []
        for conv in self.convs:
            conv_out = conv(x)
            conv_results.append(conv_out)
        
        # 连接多尺度特征
        multi_scale_features = torch.cat(conv_results, dim=1)
        
        # 应用激活、正则化和dropout
        multi_scale_features = self.activation(multi_scale_features)
        multi_scale_features = self.norm(multi_scale_features)
        multi_scale_features = self.dropout(multi_scale_features)
        
        # 转回原始形状 [batch_size, channels, seq_len] -> [batch_size, seq_len, channels]
        return multi_scale_features.transpose(1, 2)

class PositionalEncoding(nn.Module):
    """位置编码"""
    
    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        position = torch.arange(max_len).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model))
        pe = torch.zeros(max_len, d_model)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        添加位置编码到输入张量
        
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, d_model]
            
        Returns:
            添加位置编码后的张量，形状保持不变
        """
        x = x + self.pe[:x.size(1), :]
        return self.dropout(x)

class CNNTransformerExtractor(nn.Module):
    """
    CNN+Transformer混合全局特征提取器 (E2)
    
    CNN先提取多尺度局部特征，然后Transformer捕获全局依赖关系
    """
    
    def __init__(self, 
                 input_dim: int, 
                 hidden_dim: int = 128, 
                 cnn_layers: int = 2, 
                 transformer_layers: int = 2, 
                 num_heads: int = 4, 
                 kernel_sizes: List[int] = [3, 5, 7],
                 dropout: float = 0.1):
        """
        初始化CNN+Transformer混合特征提取器
        
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            cnn_layers: CNN层数
            transformer_layers: Transformer层数
            num_heads: 注意力头数
            kernel_sizes: CNN卷积核大小列表
            dropout: Dropout比率
        """
        super().__init__()
        
        # 初始投影层
        self.input_proj = nn.Linear(input_dim, hidden_dim)
        
        # CNN层
        self.cnn_blocks = nn.ModuleList()
        for _ in range(cnn_layers):
            self.cnn_blocks.append(CNNBlock(
                in_channels=hidden_dim,
                out_channels=hidden_dim,
                kernel_sizes=kernel_sizes,
                dropout=dropout
            ))
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(hidden_dim, dropout)
        
        # Transformer编码器层
        encoder_layer = TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout
        )
        
        self.transformer_encoder = nn.ModuleList([copy.deepcopy(encoder_layer) for _ in range(transformer_layers)])
        self.output_norm = nn.LayerNorm(hidden_dim)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播函数
        
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_dim]
            
        Returns:
            全局特征表示，形状为 [batch_size, hidden_dim]
        """
        # 初始投影 [batch_size, seq_len, input_dim] -> [batch_size, seq_len, hidden_dim]
        x = self.input_proj(x)
        
        # CNN特征提取
        for cnn_block in self.cnn_blocks:
            x = x + cnn_block(x)  # 残差连接
        
        # 添加位置编码
        x = self.pos_encoder(x)
        
        # Transformer处理
        for layer in self.transformer_encoder:
            x = layer(x)
        
        # 取序列的平均值作为特征表示
        x = self.output_norm(x)
        x = torch.mean(x, dim=1)
        
        return x

class GlobalFeatureExtractor(nn.Module):
    """
    纯Transformer全局同质特征提取器 (E2) - 使用多头注意力机制的轻量级Transformer编码器
    """
    
    def __init__(self, input_dim: int, hidden_dim: int = 128, num_layers: int = 2, num_heads: int = 4, dropout: float = 0.1):
        """
        初始化纯Transformer全局特征提取器
        
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            num_layers: Transformer层数
            num_heads: 注意力头数
            dropout: Dropout比率
        """
        super().__init__()
        
        self.input_proj = nn.Linear(input_dim, hidden_dim)
        self.pos_encoder = PositionalEncoding(hidden_dim, dropout)
        
        encoder_layer = TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=num_heads,
            dim_feedforward=hidden_dim * 4,
            dropout=dropout
        )
        
        self.transformer_encoder = nn.ModuleList([copy.deepcopy(encoder_layer) for _ in range(num_layers)])
        self.output_norm = nn.LayerNorm(hidden_dim)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播函数
        
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_dim]
            
        Returns:
            全局特征表示，形状为 [batch_size, hidden_dim]
        """
        # 输入形状: [batch_size, seq_len, input_dim]
        x = self.input_proj(x)
        x = self.pos_encoder(x)
        
        for layer in self.transformer_encoder:
            x = layer(x)
            
        # 取序列的平均值作为特征表示
        x = self.output_norm(x)
        x = torch.mean(x, dim=1)
        
        return x

class LocalFeatureExtractor(nn.Module):
    """本地异质特征提取器 (E1) - LSTM结构"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 128, num_layers: int = 2, dropout: float = 0.1):
        super().__init__()
        
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=True
        )
        
        self.output_proj = nn.Linear(hidden_dim * 2, hidden_dim)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播函数
        
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_dim]
            
        Returns:
            本地特征表示，形状为 [batch_size, hidden_dim]
        """
        # 输入形状: [batch_size, seq_len, input_dim]
        output, (hidden, _) = self.lstm(x)
        
        # 使用最后一个时间步的隐藏状态
        # 连接双向LSTM的最后一个隐藏状态
        hidden = torch.cat([hidden[-2], hidden[-1]], dim=1)
        hidden = self.output_proj(hidden)
        
        return hidden

class GatingNetwork(nn.Module):
    """门控网络 - 多层感知机"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 64):
        super().__init__()
        
        self.mlp = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 2),
            nn.Softmax(dim=1)
        )
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播函数
        
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_dim]
            
        Returns:
            门控权重，形状为 [batch_size, 2]
        """
        # 输入形状: [batch_size, seq_len, input_dim]
        # 取序列的平均值作为输入
        x = torch.mean(x, dim=1)
        weights = self.mlp(x)
        return weights

class PredictionClassifier(nn.Module):
    """预测分类器 - 全连接网络"""
    
    def __init__(self, input_dim: int, hidden_dims: List[int], num_classes: int, dropout: float = 0.1):
        super().__init__()
        
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout))
            prev_dim = hidden_dim
            
        layers.append(nn.Linear(prev_dim, num_classes))
        
        self.classifier = nn.Sequential(*layers)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播函数
        
        Args:
            x: 输入特征张量，形状为 [batch_size, input_dim]
            
        Returns:
            分类logits，形状为 [batch_size, num_classes]
        """
        return self.classifier(x)

class VehicleIDSModel(nn.Module):
    """车辆入侵检测模型 - 混合专家模型"""
    
    def __init__(self, input_dim: int, seq_len: int, hidden_dim: int = 128, num_classes: int = 2, 
                 local_layers: int = 2, global_layers: int = 2, classifier_dims: List[int] = [64]):
        super().__init__()
        
        # 本地特征提取器 (E1)
        self.local_extractor = LocalFeatureExtractor(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            num_layers=local_layers
        )
        
        # 全局特征提取器 (E2) - 在边缘节点共享
        self.global_extractor = None  # 将由边缘节点提供
        
        # 门控网络
        self.gating_network = GatingNetwork(
            input_dim=input_dim
        )
        
        # 预测分类器
        self.classifier = PredictionClassifier(
            input_dim=hidden_dim,
            hidden_dims=classifier_dims,
            num_classes=num_classes
        )
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.seq_len = seq_len
        
    def set_global_extractor(self, global_extractor: nn.Module) -> None:
        """
        设置全局特征提取器
        
        Args:
            global_extractor: 全局特征提取器模型
        """
        self.global_extractor = global_extractor
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播函数
        
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, input_dim]
            
        Returns:
            logits: 分类logits，形状为 [batch_size, num_classes]
            weights: 门控权重，形状为 [batch_size, 2]
        """
        # 输入形状: [batch_size, seq_len, input_dim]
        
        # 确保全局特征提取器已设置
        if self.global_extractor is None:
            raise ValueError("全局特征提取器未设置")
        
        # 获取本地特征表示
        local_features = self.local_extractor(x)
        
        # 获取全局特征表示
        global_features = self.global_extractor(x)
        
        # 获取门控权重
        weights = self.gating_network(x)
        
        # 混合特征表示
        mixed_features = weights[:, 0].unsqueeze(1) * local_features + weights[:, 1].unsqueeze(1) * global_features
        
        # 预测
        logits = self.classifier(mixed_features)
        
        return logits, weights

def create_global_extractor(input_dim: int, hidden_dim: int = 128, num_layers: int = 2, num_heads: int = 4, 
                           model_type: str = "transformer") -> Union[GlobalFeatureExtractor, CNNTransformerExtractor]:
    """
    创建全局特征提取器
    
    Args:
        input_dim: 输入特征维度
        hidden_dim: 隐藏层维度
        num_layers: Transformer层数
        num_heads: 注意力头数
        model_type: 模型类型，可选值为 'transformer' 或 'cnn_transformer'
        
    Returns:
        全局特征提取器模型实例
    """
    if model_type == "transformer":
        return GlobalFeatureExtractor(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            num_heads=num_heads
        )
    elif model_type == "cnn_transformer":
        return CNNTransformerExtractor(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            cnn_layers=2,
            transformer_layers=num_layers,
            num_heads=num_heads
        )
    else:
        raise ValueError(f"不支持的模型类型: {model_type}，可选值为 'transformer' 或 'cnn_transformer'") 