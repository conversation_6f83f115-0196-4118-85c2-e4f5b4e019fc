from flgo.experiment.logger import BasicLogger
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import os
import datetime
import pickle

class FullLogger(BasicLogger):
    def log_once(self, *args, **kwargs):
        self.info('Current_time:{}'.format(self.clock.current_time))
        self.output['time'].append(self.clock.current_time)
        test_metric, metrics1 = self.coordinator.test()
        for met_name, met_val in test_metric.items():
            self.output['test_' + met_name].append(met_val)
        val_metrics = self.coordinator.global_test(flag='val')
        local_data_vols = [c.datavol for c in self.participants]
        total_data_vol = sum(local_data_vols)
        for met_name, met_val in val_metrics.items():
            self.output['val_'+met_name+'_dist'].append(met_val)
            self.output['val_' + met_name].append(1.0 * sum([client_vol * client_met for client_vol, client_met in zip(local_data_vols, met_val)]) / total_data_vol)
            self.output['mean_val_' + met_name].append(np.mean(met_val))
            self.output['std_val_' + met_name].append(np.std(met_val))

        # print("======================asasa", self.server.current_round)

        # 检查是否是第100轮，如果是则进行T-SNE可视化
        if hasattr(self.server, 'current_round') and self.server.current_round == 100:
            self._perform_tsne_visualization(metrics1)

        
        
        # for met_name, met_val in test_metric.items():
            # self.output['test_' + met_name].append(met_val)
        # test_metric = self.server.test()
        # val_metric = self.server.test(flag='val')
        # for met_name, met_val in val_metric.items():
        #     self.output['val_' + met_name].append(met_val)
        # # calculate weighted averaging of metrics on training datasets across participants
        # local_data_vols = [c.datavol for c in self.clients]
        # total_data_vol = sum(local_data_vols)
        # train_metrics = self.server.global_test(flag='train')
        # for met_name, met_val in train_metrics.items():
        #     self.output['train_' + met_name + '_dist'].append(met_val)
        #     self.output['train_' + met_name].append(1.0 * sum([client_vol * client_met for client_vol, client_met in zip(local_data_vols, met_val)]) / total_data_vol)
        # # calculate weighted averaging and other statistics of metrics on validation datasets across clients
        # local_val_metrics = self.server.global_test(flag='val')
        # for met_name, met_val in local_val_metrics.items():
        #     self.output['local_val_'+met_name+'_dist'].append(met_val)
        #     self.output['local_val_' + met_name].append(1.0 * sum([client_vol * client_met for client_vol, client_met in zip(local_data_vols, met_val)]) / total_data_vol)
        #     self.output['mean_local_val_' + met_name].append(np.mean(met_val))
        #     self.output['std_local_val_' + met_name].append(np.std(met_val))
        # local_test_metrics = self.server.global_test(flag='test')
        # for met_name, met_val in local_test_metrics.items():
        #     self.output['local_test_'+met_name+'_dist'].append(met_val)
        #     self.output['local_test_' + met_name].append(1.0 * sum([client_vol * client_met for client_vol, client_met in zip(local_data_vols, met_val)]) / total_data_vol)
        #     self.output['mean_local_test_' + met_name].append(np.mean(met_val))
        #     self.output['std_local_test_' + met_name].append(np.std(met_val))
        # output to stdout
        self.show_current_output()

    def _perform_tsne_visualization(self, test_metric):
        """在第100轮时对test_metric中的features_before_classifier进行T-SNE可视化。

        Args:
            test_metric: 测试指标字典，包含features_before_classifier
        """
        try:
            print(f"\n🎨 开始进行T-SNE可视化分析（第100轮）...")

            # 检查是否存在features_before_classifier
            if 'features_before_classifier' not in test_metric or test_metric['features_before_classifier'] is None:
                print("❌ 未找到features_before_classifier数据，跳过T-SNE可视化")
                return

            # 提取特征和标签
            features = np.array(test_metric['features_before_classifier'])

            # 检查是否有真实标签
            if 'y_true' in test_metric and test_metric['y_true'] is not None:
                y_true = np.array(test_metric['y_true'])
            else:
                print("❌ 未找到真实标签数据，跳过T-SNE可视化")
                return

            print(f"特征形状: {features.shape}")
            print(f"标签形状: {y_true.shape}")
            print(f"唯一标签: {np.unique(y_true)}")

            # 对特征进行归一化到[0,1]范围
            print("对特征进行归一化到[0,1]范围...")
            from sklearn.preprocessing import MinMaxScaler
            scaler = MinMaxScaler(feature_range=(0, 1))
            features_normalized = scaler.fit_transform(features)
            print(f"归一化后特征统计: 最小值={np.min(features_normalized):.6f}, 最大值={np.max(features_normalized):.6f}")

            # 如果特征维度太高，先进行PCA降维
            if features_normalized.shape[1] > 50:
                print(f"特征维度较高({features_normalized.shape[1]})，先使用PCA降维到50维...")
                pca = PCA(n_components=50, random_state=42)
                features_pca = pca.fit_transform(features_normalized)
                print(f"PCA后特征形状: {features_pca.shape}")
                print(f"PCA解释的方差比例: {pca.explained_variance_ratio_.sum():.3f}")
            else:
                features_pca = features_normalized

            # 执行T-SNE降维
            print("执行T-SNE降维...")
            tsne = TSNE(
                n_components=2,
                random_state=42,  # 确保结果可重现
                perplexity=min(30, len(features_pca) - 1),  # 确保perplexity不超过样本数
                n_iter=1000,
                # verbose=1,
                # learning_rate=200
            )

            features_2d = tsne.fit_transform(features_pca)

            # 对T-SNE输出的2D坐标进行归一化到[-1,1]范围
            print("对T-SNE输出坐标进行归一化到[-1,1]范围...")
            scaler_2d = MinMaxScaler(feature_range=(-1, 1))
            features_2d_normalized = scaler_2d.fit_transform(features_2d)
            print(f"T-SNE坐标归一化前范围: X[{np.min(features_2d[:, 0]):.2f}, {np.max(features_2d[:, 0]):.2f}], Y[{np.min(features_2d[:, 1]):.2f}, {np.max(features_2d[:, 1]):.2f}]")
            print(f"T-SNE坐标归一化后范围: X[{np.min(features_2d_normalized[:, 0]):.2f}, {np.max(features_2d_normalized[:, 0]):.2f}], Y[{np.min(features_2d_normalized[:, 1]):.2f}, {np.max(features_2d_normalized[:, 1]):.2f}]")

            # 创建可视化
            plt.figure() #figsize=(12, 8)

            # 获取唯一标签和对应的颜色
            unique_labels = np.unique(y_true)
            colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))

            # 为每个类别绘制散点图
            for i, label in enumerate(unique_labels):
                mask = y_true == label
                plt.scatter(
                    features_2d_normalized[mask, 0],
                    features_2d_normalized[mask, 1],
                    c=[colors[i]],
                    label=f'Class {int(label)}',
                    alpha=0.7,
                    s=10,
                    # edgecolors='black',
                    linewidth=0.5
                )

            # 设置坐标轴刻度，以0.5为间隔，字体大小为14
            plt.xticks([-1.0, -0.5, 0.0, 0.5, 1.0], fontsize=12)
            plt.yticks([-1.0, -0.5, 0.0, 0.5, 1.0], fontsize=12)

            # plt.title('T-SNE Visualization of Features Before Classifier (Round 100)',
            #          fontsize=16, fontweight='bold')
            # plt.xlabel('T-SNE Component 1', fontsize=12)
            # plt.ylabel('T-SNE Component 2', fontsize=12)
            plt.legend(loc='upper right', fontsize=12, markerscale=2)  #bbox_to_anchor=(1.05, 1),
            # plt.grid(True, alpha=0.3)
            plt.tight_layout()

            # 保存图像
            save_dir = "tsne_visualizations"
            os.makedirs(save_dir, exist_ok=True)

            # 生成文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"tsne_round100_{timestamp}.png"
            filepath = os.path.join(save_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            print(f"✅ T-SNE可视化图已保存到: {filepath}")

            # 也保存为PDF格式
            pdf_filepath = filepath.replace('.png', '.pdf')
            plt.savefig(pdf_filepath, bbox_inches='tight')
            print(f"✅ T-SNE可视化图(PDF)已保存到: {pdf_filepath}")

            # 显示图像（如果在交互环境中）
            try:
                plt.show()
            except:
                pass

            plt.close()

            # 保存T-SNE结果数据
            tsne_data = {
                'features_2d_original': features_2d.tolist(),
                'features_2d_normalized': features_2d_normalized.tolist(),
                'labels': y_true.tolist(),
                'unique_labels': unique_labels.tolist(),
                'original_feature_shape': features.shape,
                'normalized_feature_shape': features_normalized.shape,
                'tsne_params': {
                    'random_state': 42,
                    'perplexity': tsne.perplexity,
                    'n_iter': 1000,
                    'learning_rate': 200
                },
                'round': 100
            }

            data_filepath = os.path.join(save_dir, f"tsne_data_round100_{timestamp}.pkl")
            with open(data_filepath, 'wb') as f:
                pickle.dump(tsne_data, f)
            print(f"✅ T-SNE数据已保存到: {data_filepath}")

        except Exception as e:
            print(f"❌ T-SNE可视化失败: {str(e)}")
            import traceback
            traceback.print_exc()
