"""
分片分区（Shards Partition）模块。

这种分区方式使每个客户端只持有数据集的标签子集。例如，即使整个数据集包含10个标签，
每个客户端可能只持有其中的2个标签。这导致客户端之间的数据分布高度偏斜。

这种分区方式对应于Non-IID场景下的"标签偏斜"情况，可用于测试联邦学习算法在
极端数据异构性条件下的性能。
"""
import numpy as np
from typing import Dict, List, Tuple, Union, Optional
from flgo.benchmark.partition import BasicPartitioner


class ShardPartitioner(BasicPartitioner):
    """分片分区器，实现标签偏斜的Non-IID数据分区。
    
    在这种分区方式下，数据按标签排序后被划分为多个分片，每个客户端随机获取固定数量的分片。
    这种方式确保了每个客户端只拥有数据集中的部分标签，模拟现实世界中的极端数据异构性情况。
    
    Attributes:
        num_clients: 客户端数量
        num_shards_per_client: 每个客户端分配的分片数量
    """
    
    def __init__(self, num_clients: int, num_shards_per_client: int = 2):
        """初始化分片分区器。
        
        Args:
            num_clients: 客户端的数量
            num_shards_per_client: 每个客户端分配的分片数量，默认为2
        """
        super().__init__(num_clients=num_clients)
        self.num_shards_per_client = num_shards_per_client
        
    def __call__(self, targets: np.ndarray) -> Dict[int, List[int]]:
        """执行分片分区。
        
        Args:
            targets: 数据集的标签数组
            
        Returns:
            分区结果字典，键为客户端ID，值为分配给该客户端的样本索引列表
        """
        # 获取数据总量
        num_samples = len(targets)
        
        # 按标签对样本索引进行排序
        indices = np.argsort(targets)
        
        # 计算总分片数量
        num_shards = self.num_clients * self.num_shards_per_client
        
        # 每个分片的样本数量
        samples_per_shard = int(np.ceil(num_samples / num_shards))
        
        # 将排序后的索引划分为若干个分片
        all_shards = []
        for i in range(0, num_samples, samples_per_shard):
            end_idx = min(i + samples_per_shard, num_samples)
            all_shards.append(indices[i:end_idx])
        
        # 如果分片数量不足，复制最后一个分片以满足需求
        while len(all_shards) < num_shards:
            all_shards.append(all_shards[-1].copy())
        
        # 打乱分片顺序
        np.random.shuffle(all_shards)
        
        # 为每个客户端分配分片
        client_indices = {}
        for client_id in range(self.num_clients):
            # 为当前客户端选择分片
            client_shards = all_shards[client_id * self.num_shards_per_client: 
                                      (client_id + 1) * self.num_shards_per_client]
            
            # 合并分片中的索引
            client_indices[client_id] = np.concatenate(client_shards).tolist()
        
        return client_indices 