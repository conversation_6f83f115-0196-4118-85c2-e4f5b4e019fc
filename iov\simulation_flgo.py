import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import random
import time
from tqdm import tqdm
from loguru import logger
import argparse
import json

from data.flgo_data_loader import CICIDSBenchmark, get_dataloader
from models.models import VehicleIDSModel, create_global_extractor
from federated.flgo_fedbuff import IOVFedBuffServer, IOVFedBuffClient
from federated.flgo_fedavg import IOVFedAvgServer, IOVFedAvgClient
from utils.utils import set_seed, get_device, setup_logger, plot_training_history, plot_confusion_matrix, plot_roc_curve, print_classification_report

import flgo.algorithm.fedavg as fedavg
import flgo.algorithm.fedbuff as fedbuff
import flgo.benchmark.toolkits.partition as fpart

class IOVFlgoSimulation:
    """车联网联邦学习入侵检测模拟 - 基于flgo实现"""
    
    def __init__(self, config):
        """
        初始化模拟器
        
        参数:
            config: 配置字典
        """
        self.config = config
        
        # 获取设备
        self.device = torch.device(config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu'))
        logger.info(f"使用设备: {self.device}")
        
        # 检查配置中的算法类型
        self.algorithm_type = config.get('algorithm', 'fedbuff').lower()
        if self.algorithm_type not in ['fedbuff', 'fedavg']:
            logger.warning(f"不支持的算法类型: {self.algorithm_type}，默认使用 'fedbuff'")
            self.algorithm_type = 'fedbuff'
        
        logger.info(f"使用联邦学习算法: {self.algorithm_type}")
        
        # 检查模型类型配置
        self.model_type = config.get('model_type', 'transformer').lower()
        if self.model_type not in ['transformer', 'cnn_transformer']:
            logger.warning(f"不支持的模型类型: {self.model_type}，默认使用 'transformer'")
            self.model_type = 'transformer'
        
        logger.info(f"使用全局特征提取器类型: {self.model_type}")
        
        # 设置随机种子
        set_seed(config['seed'])
        
        # 设置日志
        setup_logger(config['log_file'])
        
        # 创建输出目录
        os.makedirs(config['output_dir'], exist_ok=True)
        
        # 加载数据
        self._load_data()
        
        # 初始化边缘服务器
        self._init_servers()
        
        # 初始化车辆客户端
        self._init_vehicles()
        
        # 训练历史
        self.history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': [],
            'edge_rounds': []
        }
        
    def _load_data(self):
        """加载和预处理数据"""
        logger.info("加载和预处理数据")
        
        # 使用flgo的基准数据集
        self.benchmark = CICIDSBenchmark(
            data_path=self.config['data_path'],
            test_size=self.config['test_size'],
            random_state=self.config['seed']
        )
        
        # 创建非独立同分布的数据分区
        self.client_data = self.benchmark.get_client_data(
            num_clients=self.config['num_vehicles'],
            alpha=self.config['alpha'],
            partition_type=self.config['partition_type']  # 使用flgo的分区方法
        )
        
        # 保存测试数据
        self.X_test = self.benchmark.X_test
        self.y_test = self.benchmark.y_test
        
        # 获取特征维度和类别数
        self.input_dim = self.benchmark.input_dim
        self.num_classes = self.benchmark.num_classes
        
        logger.info(f"数据加载完成: 特征维度={self.input_dim}, 类别数={self.num_classes}")
        
    def _init_servers(self):
        """初始化边缘服务器"""
        logger.info("初始化边缘服务器")
        
        # 设置分类器维度
        if not hasattr(self.config, 'classifier_dims'):
            self.config['classifier_dims'] = [64]
            
        # 创建边缘服务器
        self.edge_servers = {}
        
        for edge_id in range(self.config['num_edges']):
            # 创建全局模型
            global_model = VehicleIDSModel(
                input_dim=self.input_dim,
                seq_len=1,  # 假设序列长度为1
                hidden_dim=self.config['hidden_dim'],
                num_classes=self.num_classes,
                local_layers=self.config['local_layers'],
                global_layers=self.config['global_layers'],
                classifier_dims=self.config['classifier_dims']
            ).to(self.device)
            
            # 创建全局特征提取器
            global_extractor = create_global_extractor(
                input_dim=self.input_dim,
                hidden_dim=self.config['hidden_dim'],
                num_layers=self.config['global_layers'],
                num_heads=self.config['num_heads'],
                model_type=self.model_type
            ).to(self.device)
            
            # 设置全局特征提取器
            global_model.set_global_extractor(global_extractor)
            
            if self.algorithm_type == 'fedbuff':
                # 使用FedBuff创建边缘服务器
                edge_server = IOVFedBuffServer(
                    global_model=global_model,
                    buffer_size=self.config['buffer_size'],
                    staleness_threshold=self.config['staleness_threshold'],
                    device=self.device,
                    edge_id=edge_id
                )
            else:
                # 使用FedAvg创建边缘服务器
                edge_server = IOVFedAvgServer(
                    global_model=global_model,
                    device=self.device,
                    edge_id=edge_id
                )
            
            self.edge_servers[edge_id] = edge_server
        
        logger.info(f"初始化了 {len(self.edge_servers)} 个边缘服务器")
        
    def _init_vehicles(self):
        """初始化车辆客户端"""
        logger.info("初始化车辆客户端")
        
        self.vehicles = {}
        
        for vehicle_id in range(self.config['num_vehicles']):
            # 获取车辆数据
            vehicle_data = self.client_data[vehicle_id]
            X, y = vehicle_data['x'], vehicle_data['y']
            
            # 创建数据加载器
            dataloader = get_dataloader(
                features=X,
                labels=y,
                batch_size=self.config['batch_size'],
                shuffle=True
            )
            
            # 创建车辆模型
            vehicle_model = VehicleIDSModel(
                input_dim=self.input_dim,
                seq_len=1,  # 假设序列长度为1
                hidden_dim=self.config['hidden_dim'],
                num_classes=self.num_classes,
                local_layers=self.config['local_layers'],
                global_layers=self.config['global_layers'],
                classifier_dims=self.config['classifier_dims']
            ).to(self.device)
            
            # 创建优化器
            optimizer = optim.Adam(
                vehicle_model.parameters(),
                lr=self.config['learning_rate'],
                weight_decay=self.config['weight_decay']
            )
            
            # 创建损失函数
            criterion = nn.CrossEntropyLoss()
            
            if self.algorithm_type == 'fedbuff':
                # 使用FedBuff创建车辆客户端
                vehicle = IOVFedBuffClient(
                    client_id=vehicle_id,
                    model=vehicle_model,
                    dataloader=dataloader,
                    optimizer=optimizer,
                    criterion=criterion,
                    device=self.device
                )
            else:
                # 使用FedAvg创建车辆客户端
                vehicle = IOVFedAvgClient(
                    client_id=vehicle_id,
                    model=vehicle_model,
                    dataloader=dataloader,
                    optimizer=optimizer,
                    criterion=criterion,
                    device=self.device
                )
            
            self.vehicles[vehicle_id] = vehicle
            
        logger.info(f"初始化了 {len(self.vehicles)} 个车辆客户端")
        
        # 创建测试数据加载器
        self.test_dataloader = get_dataloader(
            features=self.X_test,
            labels=self.y_test,
            batch_size=self.config['batch_size'],
            shuffle=False
        )
        
    def simulate_vehicle_movement(self):
        """模拟车辆移动"""
        logger.info("模拟车辆移动")
        
        # 随机分配车辆到边缘服务器
        vehicle_assignments = {}
        
        for vehicle_id, vehicle in self.vehicles.items():
            # 随机选择一个边缘服务器
            edge_id = random.choice(list(self.edge_servers.keys()))
            
            # 连接车辆到边缘服务器
            vehicle.connect_to_edge(self.edge_servers[edge_id])
            
            # 记录分配
            vehicle_assignments[vehicle_id] = edge_id
            
        logger.info(f"车辆分配到边缘服务器: {vehicle_assignments}")
        
        return vehicle_assignments
        
    def simulate_vehicle_movement_step(self, vehicle_assignments):
        """
        模拟车辆移动的一个步骤
        
        参数:
            vehicle_assignments: 当前车辆分配
            
        返回:
            new_assignments: 新的车辆分配
        """
        # 随机选择一部分车辆进行移动
        num_moving_vehicles = max(1, int(self.config['num_vehicles'] * self.config['movement_ratio']))
        moving_vehicles = random.sample(list(self.vehicles.keys()), num_moving_vehicles)
        
        new_assignments = vehicle_assignments.copy()
        
        for vehicle_id in moving_vehicles:
            # 当前边缘服务器
            current_edge_id = vehicle_assignments[vehicle_id]
            
            # 可能的新边缘服务器（排除当前的）
            possible_edges = [edge_id for edge_id in self.edge_servers.keys() if edge_id != current_edge_id]
            
            if possible_edges:
                # 随机选择一个新的边缘服务器
                new_edge_id = random.choice(possible_edges)
                
                # 断开与当前边缘服务器的连接
                self.vehicles[vehicle_id].disconnect_from_edge()
                
                # 连接到新的边缘服务器
                self.vehicles[vehicle_id].connect_to_edge(self.edge_servers[new_edge_id])
                
                # 更新分配
                new_assignments[vehicle_id] = new_edge_id
                
                logger.info(f"车辆 {vehicle_id} 从边缘服务器 {current_edge_id} 移动到 {new_edge_id}")
                
        return new_assignments

    def aggregate_edge_models(self):
        """
        聚合所有边缘服务器的模型
        
        返回:
            是否进行了聚合
        """
        if not self.edge_servers:
            logger.warning("没有边缘服务器，无法聚合")
            return False
            
        # 获取每个边缘服务器管理的车辆数量作为权重
        edge_weights = {}
        total_vehicles = 0
        
        for edge_id, edge_server in self.edge_servers.items():
            num_vehicles = len(edge_server.get_managed_vehicles())
            edge_weights[edge_id] = num_vehicles
            total_vehicles += num_vehicles
            
        # 归一化权重
        if total_vehicles > 0:
            for edge_id in edge_weights:
                edge_weights[edge_id] /= total_vehicles
        else:
            # 如果没有车辆，使用均匀权重
            for edge_id in edge_weights:
                edge_weights[edge_id] = 1.0 / len(self.edge_servers)
                
        # 聚合模型
        # 选择一个边缘服务器作为主服务器
        main_edge_id = list(self.edge_servers.keys())[0]
        main_edge_server = self.edge_servers[main_edge_id]
        
        # 从所有边缘服务器收集模型
        aggregated_model = {}
        
        # 初始化聚合模型
        for name, param in main_edge_server.global_model.named_parameters():
            if param.requires_grad:
                aggregated_model[name] = torch.zeros_like(param.data)
                
        # 加权聚合
        for edge_id, edge_server in self.edge_servers.items():
            weight = edge_weights[edge_id]
            for name, param in edge_server.global_model.named_parameters():
                if name in aggregated_model and param.requires_grad:
                    aggregated_model[name] += weight * param.data.to(self.device)
        
        # 将聚合后的模型分发给所有边缘服务器
        for edge_id, edge_server in self.edge_servers.items():
            with torch.no_grad():
                for name, param in edge_server.global_model.named_parameters():
                    if name in aggregated_model and param.requires_grad:
                        param.data = aggregated_model[name].clone()
        
        logger.info(f"完成边缘服务器全局聚合，聚合了 {len(self.edge_servers)} 个边缘服务器的模型")
        return True
        
    def train_round(self):
        """
        训练一轮
        
        返回:
            train_loss: 训练损失
            train_acc: 训练准确率
            val_loss: 验证损失
            val_acc: 验证准确率
            edge_aggregations: 边缘聚合次数
        """
        logger.info("开始训练轮次")
        
        # 记录聚合次数
        edge_aggregations = 0
        
        # 记录训练指标
        total_train_loss = 0
        total_train_acc = 0
        total_vehicles = 0
        
        # 训练每个车辆
        for vehicle_id, vehicle in self.vehicles.items():
            # 训练车辆模型
            train_loss, train_acc = vehicle.train(num_epochs=self.config['local_epochs'])
            
            if train_loss is not None and train_acc is not None:
                total_train_loss += train_loss * vehicle.data_size
                total_train_acc += train_acc * vehicle.data_size
                total_vehicles += vehicle.data_size
                
                # 上传模型到边缘服务器
                triggered = vehicle.upload_model()
                
                # 如果触发了聚合（在FedBuff中可能触发，在FedAvg中当所有车辆都上传后触发）
                if triggered:
                    edge_aggregations += 1
        
        # 计算平均训练指标
        if total_vehicles > 0:
            avg_train_loss = total_train_loss / total_vehicles
            avg_train_acc = total_train_acc / total_vehicles
        else:
            avg_train_loss = 0
            avg_train_acc = 0
            
        # 执行边缘服务器之间的全局聚合
        if self.algorithm_type == 'fedavg' or self.config.get('edge_aggregation_interval', 1) == 1:
            # FedAvg每轮都进行全局聚合
            self.aggregate_edge_models()
            edge_aggregations += 1
        else:
            # FedBuff根据配置的间隔进行全局聚合
            if self.current_round % self.config.get('edge_aggregation_interval', 5) == 0:
                self.aggregate_edge_models()
                edge_aggregations += 1
        
        # 评估全局模型
        val_loss, val_acc = self.evaluate_global_model()
        
        # 更新轮次
        self.current_round += 1
        
        logger.info(f"训练轮次 {self.current_round} 完成: 训练损失={avg_train_loss:.4f}, 训练准确率={avg_train_acc:.4f}, "
                   f"验证损失={val_loss:.4f}, 验证准确率={val_acc:.4f}")
        
        return avg_train_loss, avg_train_acc, val_loss, val_acc, edge_aggregations
    
    def evaluate_global_model(self):
        """
        评估全局模型
        
        返回:
            val_loss: 验证损失
            val_acc: 验证准确率
        """
        # 使用第一个边缘服务器的模型作为全局模型评估
        main_edge_id = list(self.edge_servers.keys())[0]
        global_model = self.edge_servers[main_edge_id].global_model
        global_model.eval()
        
        # 在测试集上评估
        total_loss = 0
        correct = 0
        total = 0
        
        criterion = nn.CrossEntropyLoss()
        
        with torch.no_grad():
            for data, target in self.test_dataloader:
                data, target = data.to(self.device), target.to(self.device)
                
                # 确保数据是3D的 [batch_size, seq_len, input_dim]
                if len(data.shape) == 2:
                    data = data.unsqueeze(1)  # 添加序列维度
                
                output, _ = global_model(data)
                loss = criterion(output, target)
                
                total_loss += loss.item() * data.size(0)
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += data.size(0)
        
        val_loss = total_loss / total
        val_acc = correct / total
        
        logger.info(f"全局模型评估: 损失={val_loss:.4f}, 准确率={val_acc:.4f}")
        
        return val_loss, val_acc
        
    def run_simulation(self):
        """运行模拟"""
        logger.info("开始运行模拟")
        
        # 初始车辆分配
        vehicle_assignments = self.simulate_vehicle_movement()
        
        # 训练历史
        history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': [],
            'edge_rounds': []
        }
        
        # 边缘聚合总次数
        total_edge_rounds = 0
        self.current_round = 0
        
        # 训练循环
        for round_idx in tqdm(range(self.config['num_rounds'])):
            logger.info(f"开始第 {round_idx+1}/{self.config['num_rounds']} 轮训练")
            
            # 模拟车辆移动
            if round_idx > 0 and round_idx % self.config['movement_interval'] == 0:
                vehicle_assignments = self.simulate_vehicle_movement_step(vehicle_assignments)
                
            # 训练一轮
            train_loss, train_acc, val_loss, val_acc, edge_rounds = self.train_round()
            
            # 更新聚合总次数
            total_edge_rounds += edge_rounds
            
            # 更新训练历史
            history['train_loss'].append(train_loss)
            history['train_acc'].append(train_acc)
            history['val_loss'].append(val_loss)
            history['val_acc'].append(val_acc)
            history['edge_rounds'].append(total_edge_rounds)
            
            # 保存训练历史
            plot_training_history(
                history,
                save_path=os.path.join(self.config['output_dir'], 'training_history.png')
            )
            
            # 如果达到目标准确率，提前结束
            if val_acc >= self.config['target_accuracy']:
                logger.info(f"达到目标准确率 {self.config['target_accuracy']:.4f}，提前结束训练")
                break
                
        # 最终评估
        self.final_evaluation()
        
        logger.info("模拟完成")
        
        return history
        
    def final_evaluation(self):
        """最终评估"""
        logger.info("进行最终评估")
        
        # 使用第一个边缘服务器的模型作为全局模型评估
        main_edge_id = list(self.edge_servers.keys())[0]
        global_model = self.edge_servers[main_edge_id].global_model
        global_model.eval()
        
        # 在测试集上评估
        y_true = []
        y_pred = []
        y_score = []
        
        with torch.no_grad():
            for data, target in self.test_dataloader:
                data, target = data.to(self.device), target.to(self.device)
                
                # 确保数据是3D的 [batch_size, seq_len, input_dim]
                if len(data.shape) == 2:
                    data = data.unsqueeze(1)  # 添加序列维度
                
                output, _ = global_model(data)
                pred = output.argmax(dim=1, keepdim=True)
                
                y_true.extend(target.cpu().numpy())
                y_pred.extend(pred.cpu().numpy().flatten())
                y_score.extend(torch.softmax(output, dim=1).cpu().numpy())
                
        y_true = np.array(y_true)
        y_pred = np.array(y_pred)
        y_score = np.array(y_score)
        
        # 打印分类报告
        print_classification_report(y_true, y_pred)
        
        # 绘制混淆矩阵
        class_names = [f"类别{i}" for i in range(self.num_classes)]
        plot_confusion_matrix(
            y_true, y_pred, class_names,
            save_path=os.path.join(self.config['output_dir'], 'confusion_matrix.png')
        )
        
        # 绘制ROC曲线
        plot_roc_curve(
            y_true, y_score,
            save_path=os.path.join(self.config['output_dir'], 'roc_curve.png')
        )
        
        logger.info("最终评估完成")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='车联网联邦学习模拟器')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--algorithm', type=str, default='fedbuff', choices=['fedbuff', 'fedavg'], help='联邦学习算法类型')
    parser.add_argument('--model_type', type=str, default='transformer', choices=['transformer', 'cnn_transformer'], 
                        help='全局特征提取器模型类型')
    args = parser.parse_args()
    
    # 设置随机种子
    seed = args.seed
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    
    # 读取配置文件
    with open(args.config, 'r') as f:
        config = json.load(f)
    
    # 如果命令行参数指定了算法类型，则覆盖配置文件中的值
    if args.algorithm:
        config['algorithm'] = args.algorithm
        
    # 如果命令行参数指定了模型类型，则覆盖配置文件中的值
    if args.model_type:
        config['model_type'] = args.model_type
    
    # 创建模拟器并运行
    simulator = IOVFlgoSimulation(config)
    simulator.run_simulation()
    simulator.final_evaluation()

if __name__ == "__main__":
    main() 