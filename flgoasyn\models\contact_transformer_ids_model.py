"""Contact-Transformer 入侵检测模型

这个模型使用已有的特征编码器（LSTM 或 CNN）与 Transformer（Top-K 稀疏多头注意力）
的上下文表示进行拼接（concatenate），无需位置编码，最终用于分类任务。
支持可选的多尺度注意力机制，可以捕获不同窗口大小的上下文信息。
"""
from __future__ import annotations

import math
from typing import Dict, List, Literal, Optional, Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F

from .cnn_encoder import CNNEncoder
from .lstm_encoder import LSTMEncoder
from .sparse_transformer import SparseMultiheadAttention


class TransformerEncoderLayer(nn.Module):
    """Transformer编码器层，使用稀疏多头注意力机制。
    
    Attributes:
        d_model (int): 模型维度
        nhead (int): 注意力头数
        dim_feedforward (int): 前馈网络隐藏层维度
        dropout (float): Dropout概率
        k_ratio (float): 稀疏注意力的Top-K比例
        use_glu (bool): 是否使用门控线性单元
        pre_norm (bool): 是否在注意力和前馈网络前使用层归一化
        self_attn (SparseMultiheadAttention): 稀疏多头自注意力层
        linear1 (nn.Linear): 前馈网络第一层
        dropout1 (nn.Dropout): 自注意力后的Dropout
        linear2 (nn.Linear): 前馈网络第二层
        dropout2 (nn.Dropout): 前馈网络后的Dropout
        norm1 (nn.LayerNorm): 第一个层归一化
        norm2 (nn.LayerNorm): 第二个层归一化
        activation (callable): 激活函数
    """
    
    def __init__(
        self, 
        d_model: int, 
        nhead: int, 
        dim_feedforward: int = 2048, 
        dropout: float = 0.1, 
        k_ratio: float = 0.4,
        use_glu: bool = True,
        pre_norm: bool = True,
        activation: str = "relu"
    ) -> None:
        """初始化Transformer编码器层。
        
        Args:
            d_model: 模型维度
            nhead: 注意力头数
            dim_feedforward: 前馈网络隐藏层维度
            dropout: Dropout概率
            k_ratio: 稀疏注意力的Top-K比例
            use_glu: 是否使用门控线性单元
            pre_norm: 是否在注意力和前馈网络前使用层归一化
            activation: 激活函数，'relu'或'gelu'
        """
        super(TransformerEncoderLayer, self).__init__()
        
        self.d_model = d_model
        self.nhead = nhead
        self.dim_feedforward = dim_feedforward
        self.dropout = dropout
        self.k_ratio = k_ratio
        self.use_glu = use_glu
        self.pre_norm = pre_norm
        
        # 稀疏多头自注意力层
        self.self_attn = SparseMultiheadAttention(
            embed_dim=d_model,
            num_heads=nhead,
            dropout=dropout,
            k_ratio=k_ratio
        )
        
        # 前馈网络
        if use_glu:
            # 门控线性单元 (GLU) 版本
            self.linear1 = nn.Linear(d_model, dim_feedforward * 2)  # 双倍大小用于门控机制
        else:
            self.linear1 = nn.Linear(d_model, dim_feedforward)
            
        self.dropout1 = nn.Dropout(dropout)
        self.linear2 = nn.Linear(dim_feedforward, d_model)
        self.dropout2 = nn.Dropout(dropout)
        
        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # 激活函数
        self.activation = F.relu if activation == "relu" else F.gelu
        
    def forward(
        self, 
        src: torch.Tensor, 
        src_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """前向传播。
        
        Args:
            src: 输入序列，形状为 [batch_size, seq_len, d_model]
            src_mask: 源序列掩码，形状为 [batch_size, 1, seq_len, seq_len]
            
        Returns:
            tuple: (输出序列, 注意力权重)
                - 输出序列形状: [batch_size, seq_len, d_model]
                - 注意力权重形状: [batch_size, nhead, seq_len, seq_len]
        """
        # 自注意力
        if self.pre_norm:
            # 先归一化再注意力
            src_norm = self.norm1(src)
            src2, attn_weights = self.self_attn(src_norm, src_norm, src_norm, src_mask)
            src = src + self.dropout1(src2)
        else:
            # 先注意力再归一化
            src2, attn_weights = self.self_attn(src, src, src, src_mask)
            src = self.norm1(src + self.dropout1(src2))
        
        # 前馈网络
        if self.pre_norm:
            src_norm = self.norm2(src)
            if self.use_glu:
                # 门控线性单元实现
                x = self.linear1(src_norm)
                x, gates = x.chunk(2, dim=-1)
                x = x * torch.sigmoid(gates)  # 门控机制
            else:
                x = self.activation(self.linear1(src_norm))
            src2 = self.linear2(self.dropout1(x))
            src = src + self.dropout2(src2)
        else:
            if self.use_glu:
                # 门控线性单元实现
                x = self.linear1(src)
                x, gates = x.chunk(2, dim=-1)
                x = x * torch.sigmoid(gates)  # 门控机制
            else:
                x = self.activation(self.linear1(src))
            src2 = self.linear2(self.dropout1(x))
            src = self.norm2(src + self.dropout2(src2))
        
        return src, attn_weights


class MultiScaleAttention(nn.Module):
    """多尺度注意力模块，使用不同窗口大小的自注意力捕获不同尺度的特征。
    
    Attributes:
        d_model (int): 模型维度
        nhead (int): 注意力头数
        window_sizes (list): 注意力窗口大小列表
        dropout (float): Dropout概率
        k_ratio (float): 稀疏注意力的Top-K比例
    """
    
    def __init__(
        self,
        d_model: int,
        nhead: int,
        window_sizes: List[int] = [3, 5, 7],
        dropout: float = 0.1,
        k_ratio: float = 0.6
    ) -> None:
        """初始化多尺度注意力模块。
        
        Args:
            d_model: 模型维度
            nhead: 每个窗口的注意力头数
            window_sizes: 注意力窗口大小列表，默认为[3, 5, 7]
            dropout: Dropout概率
            k_ratio: 稀疏注意力的Top-K比例
        """
        super(MultiScaleAttention, self).__init__()
        
        self.d_model = d_model
        self.nhead = nhead
        self.window_sizes = window_sizes
        
        # 每个窗口大小对应一个自注意力层
        self.attention_layers = nn.ModuleList([
            SparseMultiheadAttention(
                embed_dim=d_model,
                num_heads=nhead,
                dropout=dropout,
                k_ratio=k_ratio
            ) for _ in window_sizes
        ])
        
        # 融合不同尺度的注意力输出
        self.fusion = nn.Linear(d_model * len(window_sizes), d_model)
        self.norm = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def _get_window_mask(self, seq_len: int, window_size: int, device: torch.device) -> torch.Tensor:
        """生成局部窗口注意力掩码。
        
        Args:
            seq_len: 序列长度
            window_size: 窗口大小
            device: 设备
            
        Returns:
            torch.Tensor: 窗口注意力掩码，形状为 [seq_len, seq_len]
        """
        # 创建距离矩阵
        i, j = torch.meshgrid(torch.arange(seq_len, device=device), torch.arange(seq_len, device=device))
        distance = torch.abs(i - j)
        
        # 窗口内为1，窗口外为0
        window_mask = (distance <= window_size // 2).float()
        
        # 转换为注意力掩码格式：窗口内为0，窗口外为-inf
        attention_mask = (1.0 - window_mask) * -10000.0
        
        return attention_mask
    
    def forward(
        self,
        x: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """前向传播。
        
        Args:
            x: 输入序列，形状为 [batch_size, seq_len, d_model]
            attention_mask: 注意力掩码，形状为 [batch_size, 1, seq_len, seq_len]
            
        Returns:
            tuple: (输出序列, 注意力权重列表)
                - 输出序列形状: [batch_size, seq_len, d_model]
                - 注意力权重列表，每个元素形状为 [batch_size, nhead, seq_len, seq_len]
        """
        batch_size, seq_len, _ = x.size()
        device = x.device
        
        # 存储不同窗口大小的注意力输出
        outputs = []
        attention_weights = []
        
        # 对每个窗口大小应用自注意力
        for i, (window_size, attn_layer) in enumerate(zip(self.window_sizes, self.attention_layers)):
            # 创建窗口注意力掩码
            window_mask = self._get_window_mask(seq_len, window_size, device)
            window_mask = window_mask.unsqueeze(0).unsqueeze(1).expand(batch_size, 1, seq_len, seq_len)
            
            # 组合窗口掩码和输入掩码
            if attention_mask is not None:
                combined_mask = window_mask + attention_mask
            else:
                combined_mask = window_mask
            
            # 应用自注意力
            output, attn_weight = attn_layer(x, x, x, combined_mask)
            outputs.append(output)
            attention_weights.append(attn_weight)
        
        # 拼接不同窗口的输出
        concat_output = torch.cat(outputs, dim=2)
        
        # 融合不同尺度的特征
        fused_output = self.fusion(concat_output)
        fused_output = self.dropout(fused_output)
        fused_output = self.norm(fused_output + x)  # 残差连接
        
        return fused_output, attention_weights


class ContactTransformerIDSModel(nn.Module):
    """基于特征编码 + 稀疏 Transformer 的入侵检测模型。

    模型流程：
        1. 特征编码（LSTM 或 CNN）提取局部时序特征；
        2. 将特征编码结果送入若干稀疏 Transformer 编码器层捕获全局依赖；
        3. 分别对特征编码输出与 Transformer 输出做平均池化；
        4. 拼接两种表示作为最终特征并进行分类。

    与 :class:`flgoasyn.models.TransformerIDSModel` 的主要区别：
        * 不使用位置编码；
        * 分类器输入为 *encoder pooling* 与 *transformer pooling* 的拼接。
        * 可选使用多尺度注意力机制，捕获不同窗口大小的上下文信息。
    """

    def __init__(
        self,
        input_size: int,
        hidden_size: int = 128,
        d_model: int = 128,
        nhead: int = 4,
        num_encoder_layers: int = 3,
        dim_feedforward: int = 512,
        num_classes: int = 2,
        dropout: float = 0.1,
        k_ratio: float = 0.4,
        encoder_type: Literal["lstm", "cnn"] = "lstm",
        cnn_kernel_size: int = 5,
        cnn_num_layers: int = 3,
        use_multi_scale: bool = False,
        window_sizes: List[int] = [3, 5, 7],
        use_glu: bool = True,
        pre_norm: bool = True,
    ) -> None:
        super().__init__()

        # 校验参数
        if d_model % nhead != 0:
            raise ValueError(f"d_model({d_model}) 必须能被 nhead({nhead}) 整除")

        self.input_size = input_size
        self.hidden_size = hidden_size
        self.d_model = d_model
        self.nhead = nhead
        self.num_encoder_layers = num_encoder_layers
        self.dim_feedforward = dim_feedforward
        self.num_classes = num_classes
        self.dropout_rate = dropout
        self.k_ratio = k_ratio
        self.encoder_type = encoder_type
        self.use_multi_scale = use_multi_scale
        self.window_sizes = window_sizes
        self.use_glu = use_glu
        self.pre_norm = pre_norm

        # 1) 特征编码器
        if encoder_type == "lstm":
            self.feature_encoder = LSTMEncoder(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=2,
                dropout=dropout,
                bidirectional=True,
            )
        else:  # "cnn"
            self.feature_encoder = CNNEncoder(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=cnn_num_layers,
                kernel_size=cnn_kernel_size,
                dropout=dropout,
            )

        encoder_out_dim: int = self.feature_encoder.output_size

        # 如果特征编码输出维度与 d_model 不一致，则投影
        self.projection: Optional[nn.Linear] = (
            nn.Linear(encoder_out_dim, d_model) if encoder_out_dim != d_model else None
        )

        # 多尺度注意力模块（如果启用）
        self.multi_scale_attention = None
        if use_multi_scale:
            self.multi_scale_attention = MultiScaleAttention(
                d_model=d_model,
                nhead=nhead // 2,  # 减少每个窗口的头数以控制参数量
                window_sizes=window_sizes,
                dropout=dropout,
                k_ratio=k_ratio
        )

        # 2) Transformer 编码器层（不使用位置编码）
        self.transformer_layers = nn.ModuleList(
            [
                TransformerEncoderLayer(
                    d_model=d_model,
                    nhead=nhead,
                    dim_feedforward=dim_feedforward,
                    dropout=dropout,
                    k_ratio=k_ratio,
                    use_glu=use_glu,
                    pre_norm=pre_norm,
                )
                for _ in range(num_encoder_layers)
            ]
        )

        # 分类器：输入维度为 pooled_encoder + pooled_transformer
        concat_dim: int = d_model * 2
        self.classifier = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(concat_dim, num_classes),
        )

        self._init_parameters()

    # ---------------------------------------------------------------------
    # Private helpers
    # ---------------------------------------------------------------------
    def _init_parameters(self) -> None:  # noqa: D401  — short description already in docstring
        """使用 Xavier 均匀初始化所有多维参数。"""
        for param in self.parameters():
            if param.dim() > 1:
                nn.init.xavier_uniform_(param)

    # ---------------------------------------------------------------------
    # Forward
    # ---------------------------------------------------------------------
    def forward(
        self,
        x: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
    ) -> Dict[str, torch.Tensor]:
        """前向传播函数。

        Args:
            x: 输入张量，形状为 ``[batch_size, seq_len, input_size]``。
            attention_mask: 形状为 ``[batch_size, seq_len]``，值为 1 表示有效，0 表示填充。

        Returns
        -------
        Dict[str, torch.Tensor]
            ``'logits'`` — 分类 logits，形状 ``[batch_size, num_classes]``；
            ``'attention_weights'`` — 最后一个 Transformer 层的注意力权重，
            形状 ``[batch_size, nhead, seq_len, seq_len]``；
            ``'multi_scale_attention_weights'`` — 多尺度注意力权重（如果启用），
            列表，每个元素形状 ``[batch_size, nhead, seq_len, seq_len]``。
        """
        # ----------------------------------------
        # 0. 处理 attention mask
        # ----------------------------------------
        if attention_mask is not None:
            # 转换为 Transformer 需要的形状与取值范围
            # [B, S] -> [B, 1, 1, S]; 1->0, 0->-10000
            attention_mask = (1.0 - attention_mask.unsqueeze(1).unsqueeze(2)) * -10000.0

        # ----------------------------------------
        # 1. 特征编码
        # ----------------------------------------
        encoder_output = self.feature_encoder(x)
        if isinstance(encoder_output, tuple):  # LSTM 额外返回 hidden, cell
            encoder_output = encoder_output[0]

        # 按需投影到 d_model 维度
        if self.projection is not None:
            encoder_output = self.projection(encoder_output)

        # ----------------------------------------
        # 2. 多尺度注意力处理（如果启用）
        # ----------------------------------------
        multi_scale_attn_weights = None
        src = encoder_output
        
        if self.use_multi_scale and self.multi_scale_attention is not None:
            src, multi_scale_attn_weights = self.multi_scale_attention(src, attention_mask)

        # ----------------------------------------
        # 3. Transformer 编码器
        # ----------------------------------------
        attention_weights_list = []
        for layer in self.transformer_layers:
            src, attn_w = layer(src, attention_mask)
            attention_weights_list.append(attn_w)

        # ----------------------------------------
        # 4. 表示聚合
        # ----------------------------------------
        pooled_encoder = torch.mean(encoder_output, dim=1)  # [B, d_model]
        pooled_transformer = torch.mean(src, dim=1)  # [B, d_model]
        concat_repr = torch.cat([pooled_encoder, pooled_transformer], dim=1)  # [B, 2*d_model]

        

        # ----------------------------------------
        # 5. 分类
        # ----------------------------------------
        logits = self.classifier(concat_repr)

        result = {
            "logits": logits,
            "attention_weights": attention_weights_list[-1],   
            "concat_repr": concat_repr 
        }
        
        if multi_scale_attn_weights is not None:
            result["multi_scale_attention_weights"] = multi_scale_attn_weights
            
        return result

    # ---------------------------------------------------------------------
    # Public helpers
    # ---------------------------------------------------------------------
    def predict(
        self,
        x: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
    ) -> torch.Tensor:
        """预测类别索引。"""
        logits, concat_repr = self.forward(x, attention_mask)["logits"], self.forward(x, attention_mask)["concat_repr"]
        return torch.argmax(logits, dim=1), concat_repr