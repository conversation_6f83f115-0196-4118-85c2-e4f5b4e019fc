"""
位置编码模块，为Transformer模型提供位置信息。
"""
import math
from typing import Optional

import torch
import torch.nn as nn


class PositionalEncoding(nn.Module):
    """位置编码模块，使用正弦和余弦函数生成位置编码。
    
    Transformer模型中，位置编码用于为序列中的每个元素提供位置信息，
    因为自注意力机制本身不包含顺序信息。
    
    Attributes:
        d_model (int): 编码维度
        max_len (int): 最大序列长度
        dropout (nn.Dropout): Dropout层
        pe (torch.Tensor): 预计算的位置编码
    """
    
    def __init__(
        self, 
        d_model: int, 
        dropout: float = 0.1, 
        max_len: int = 5000
    ) -> None:
        """初始化位置编码模块。
        
        Args:
            d_model: 编码维度，通常等于模型的隐藏层维度
            dropout: Dropout概率
            max_len: 支持的最大序列长度
        """
        super(PositionalEncoding, self).__init__()
        self.d_model = d_model
        self.max_len = max_len
        self.dropout = nn.Dropout(p=dropout)
        
        # 创建位置编码矩阵
        pe = torch.zeros(max_len, d_model)
        
        # 创建位置向量 [max_len, 1]
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        # 创建除数向量，用于计算不同频率的正弦和余弦函数
        # 使用对数空间来创建不同频率，避免数值不稳定
        div_term = torch.exp(
            torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model)
        )
        
        # 应用正弦函数到偶数索引位置
        pe[:, 0::2] = torch.sin(position * div_term)
        
        # 应用余弦函数到奇数索引位置
        # 处理d_model为奇数的情况
        if d_model % 2 == 1:
            pe[:, 1::2] = torch.cos(position * div_term)[:, :-1]
        else:
            pe[:, 1::2] = torch.cos(position * div_term)
        
        # 添加批次维度并转置为 [1, max_len, d_model]
        pe = pe.unsqueeze(0)
        
        # 注册位置编码为缓冲区（非模型参数）
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播，将位置编码添加到输入中。
        
        Args:
            x: 输入张量，形状为 [batch_size, seq_len, d_model]
            
        Returns:
            torch.Tensor: 添加位置编码后的张量，形状与输入相同
        """
        
            
        # 确保序列长度不超过最大长度
        # print('x.size(1)',x.size)
        seq_len = x.size(1)
        if seq_len > self.max_len:
            raise ValueError(f"序列长度 {seq_len} 超过了最大支持长度 {self.max_len}")
        
            
        # 将位置编码加到输入上
        # pe的形状为 [1, max_len, d_model]，这里我们只取需要的长度
        x = x + self.pe[:, :seq_len, :]
        return self.dropout(x) 