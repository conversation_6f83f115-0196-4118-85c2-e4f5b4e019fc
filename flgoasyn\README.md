# 车联网基于联邦学习的入侵检测系统

基于异步联邦学习和差分隐私保护的车联网入侵检测系统，采用LSTM+Transformer+稀疏注意力结构，实现高效、隐私保护的分布式异常检测。

## 项目特点

- **异步联邦学习**：采用FedAsync异步联邦学习框架，基于staleness度量的自适应学习率机制，提高系统效率和鲁棒性
- **双层差分隐私保护**：
  - 个性化本地RDP：客户端侧基于本地损失和梯度相似度的自适应差分隐私SGD (通过 `DPManager` 实现)。具体来说，噪声大小由一个自适应因子 `β_k` 调节，该因子由 `gamma` 参数加权本地损失和梯度相似度计算得出，并随训练轮次动态衰减。
  - 全局RDP修正：服务器端根据隐私预算和预设的预算分配策略动态调整全局噪声 (通过 `DPManager` 实现)
  - **灵活的噪声基准计算**：支持两种σ_{initial_base}计算模式：
    - `fixed`模式：使用固定的初始噪声值(`initial_max_noise`)作为基准
    - `dynamic`模式：使用当前噪声乘数(`noise_scale`)作为基准，随训练动态调整
  - **多样化的噪声衰减策略**：支持两种噪声随轮次衰减的模式：
    - `exponential`模式(默认)：σ_k^2 = σ_{initial_base}^2 · β_k · e^(-λ_{noise} · t_{global})
    - `hyperbolic`模式：σ_k^2 = σ_{initial_base}^2 · β_k / (1 + k · t_{global})
- **高效网络架构**：LSTM特征编码 → 位置编码 → Transformer(Top-K稀疏多头注意力) → 分类
- **通信压缩**：集成项目内 `CompressionManager` 的QSGD量化压缩算法，支持多种位宽，大幅减少通信开销
- **全面评估指标**：提供准确率、召回率、精确率、误报率、F1分数等指标，配合丰富可视化
- **灵活扩展**：支持不同数据集、不同数量的客户端和各种隐私设置
- **数据异构性模拟**：支持IID、Dirichlet和Shards分区，模拟真实场景下的数据异构性
- **历史模型管理**：服务器端支持完整模型存储，用于处理陈旧度计算

## 异步联邦学习方案详情

### 系统设计与初始化

本系统实现了针对车联网场景的异步联邦学习框架，结合个性化本地Rényi差分隐私(RDP)和边缘节点全局RDP修正，平衡模型精度、训练效率和隐私保护需求。

#### 关键参数

- **全局RDP预算**：通过`epsilon_total`定义整个训练过程的隐私预算
- **隐私松弛参数**：`delta`
- **梯度裁剪阈值**：使用`clip_norm`参数控制梯度敏感度
- **噪声基准比例**：`noise_scale` (用于初始化 `DPManager`)
- **自适应学习率超参数**：通过 `lr_scheme` 选择方案
  - 方案0：固定学习率
  - 方案1：基于Staleness范数比率，使用`lambda_alr`和`epsilon_alr`作为参数
  - 方案2：基于Staleness轮次差和隐私预算，使用`eta_0`、`staleness_exponent`和`gamma_priv`作为参数，并有`min_lr`确保最低学习率
- **Staleness 度量分母平滑项**：`delta_sm`
- **服务器端预算分配策略**：`budget_allocation_strategy`，选项包括 `fixed`, `uniform`, `rdp_optimal`
  - `expected_total_rounds`: 预期总轮次 (用于 `uniform` 和 `rdp_optimal` 策略)
  - `max_budget_per_round`: 每轮最大预算上限
- **有效更新范数计算方式**：使用模型差异范数计算 `N_k^{eff}`
- **模型历史管理**：
  - `history_size`: 历史模型保存数量
  - `checkpoint_interval`: 完整模型检查点间隔

### 客户端侧：个性化本地差分隐私SGD

每个参与更新的车辆客户端执行以下步骤：

1. **个性化噪声计算**：由 `DPManager` 根据本地损失、梯度方向余弦相似度、`gamma` 和噪声衰减参数动态计算自适应噪声乘数。该方法实现了 `a1.md` 中描述的自适应噪声公式，旨在为低损失、高梯度相似度的"高质量"更新分配更小的隐私噪声。
2. **差分隐私SGD**：通过 `DPManager` 转换模型和优化器，在本地数据上执行DP-SGD训练，该过程由 `DPManager` 自动处理（类似Opacus）。
3. **隐私管理**：`DPManager` 在客户端自动计算并监控本地RDP消耗。
4. **有效更新范数计算**：使用模型差异范数计算 `N_k^{eff}`，即使用加噪后的模型差异范数计算有效更新范数
5. **上传**：将隐私化后的模型更新、有效更新范数(`N_k^{eff}`)、版本号、本地RDP成本和本地训练步数发送给边缘节点。

### 边缘节点：异步聚合与自适应全局RDP修正

边缘节点实现以下功能：

1. **异步缓存和聚合**：缓存并管理来自客户端的异步更新。
2. **Staleness度量**：使用新的staleness度量方法评估客户端更新的"新鲜度"，其中分母 `N_k^{eff}` 是客户端上传的有效更新范数。
3. **自适应学习率**：根据staleness度量和隐私预算 (`lr_scheme` 参数) 动态调整每个客户端更新的聚合学习率。
4. **全局RDP修正**：根据所选的预算分配策略 (`budget_allocation_strategy`)、敏感度计算方法 (`sensitivity_method`) 和剩余隐私预算，通过 `DPManager` 计算并添加适量的全局噪声，确保整体隐私保护。
5. **隐私预算管理**：由 `DPManager` 统一追踪累积的隐私消耗，确保不超出总预算。

### 通信压缩技术

为减少通信开销，系统集成了 `CompressionManager` 提供的QSGD量化压缩算法：

- **压缩比例**：根据 `compression_bits` (2/4/8/16位) 实现2-16倍的压缩比
- **误差控制**：使用随机舍入策略，保持梯度期望不变
- **统计跟踪**：`CompressionManager` 自动记录和报告通信量统计。

## 目录结构

```
flgoasyn/
├── algorithm/              # 算法实现模块
│   ├── asyncfl.py         # 异步联邦学习核心实现
│   ├── fedasync.py        # FedAsync算法
│   └── ...
├── data/                   # 数据处理模块
│   ├── __init__.py
│   └── ...
├── models/                 # 模型模块
│   ├── lstm_encoder.py     # LSTM特征编码器
│   ├── position_encoder.py # 位置编码器
│   ├── sparse_transformer.py # 稀疏多头注意力
│   └── transformer_ids_model.py # 完整模型架构
├── utils/                  # 工具模块
│   ├── privacy.py          # 差分隐私工具
│   ├── compression.py      # 通信压缩工具
│   ├── shards_partition.py # 分片分区工具
│   ├── myLogger.py         # 自定义日志工具，扩展了FLGO的BasicLogger，记录更全面的训练指标
│   └── ...
├── configs/                # 配置文件
├── examples/               # 示例代码
├── benchmark/              # 基准测试
├── output/                 # 输出目录
├── simulator/              # 模拟器
├── main.py                 # 主程序入口
├── asyn.md                 # 方案详细说明
└── README.md               # 项目文档
```

## 安装

1. 克隆仓库：

```bash
git clone <repository-url>
cd flgoasyn
```

2. 安装依赖：

```bash
pip install -r requirements.txt
```

## API命名约定

**重要提示**：本项目基于FLGO框架开发，遵循FLGO的API命名约定。在所有自定义代码中（特别是扩展FLGO的基类时）：

- 使用`server`而非`coordinator`引用联邦学习服务器/协调器
- 例如，在自定义Logger类中，应使用`self.server`而不是`self.coordinator`来访问服务器组件
- 在`myLogger.py`中，所有与联邦学习服务器相关的方法调用（如`test()`、`global_test()`等）都应通过`self.server`而非`self.coordinator`访问

不遵循这一命名约定可能导致运行时错误和不兼容问题。如果您正在开发新的组件或修改现有代码，请确保使用正确的API命名。

## 自定义日志系统

本项目实现了一个强大的自定义日志系统(`utils/myLogger.py`)，扩展了FLGO的`BasicLogger`类，用于记录和可视化联邦学习过程中的详细指标。

### myLogger类特点

- **全面的指标记录**：除了基本的准确率外，还记录精确率、召回率、F1分数等多维评估指标
- **细粒度测试支持**：通过`flag`参数支持在不同数据集（训练、验证、测试）上进行评估
- **多层次指标统计**：
  - 全局模型在整体测试集上的性能指标
  - 全局模型在各数据划分上的性能（训练、验证、测试）
  - 本地模型在各客户端数据上的性能分布
  - 本地性能指标的统计特征（均值、标准差）
- **加权平均计算**：根据客户端数据量对本地指标进行加权平均，更准确地反映系统整体性能
- **实时可视化**：在每轮训练后输出关键指标，便于监控训练进度

### 使用方法

在`main.py`中，`myLogger`被集成到FLGO的初始化过程中：

```python
# 初始化flgo runner
runner = flgo.init(
    task=task_output_dir,  
    option=train_option, 
    simulator=Simulator, 
    logger=myLogger  # 使用自定义的myLogger类
)
```

### 日志输出示例

每轮训练后，`myLogger`会输出类似以下的指标统计：

```
轮次 10 详细指标:
  test_accuracy: 0.9234
  test_precision: 0.9156
  test_recall: 0.9312
  test_F1: 0.9233
  val_accuracy: 0.9187
  val_precision: 0.9102
  val_recall: 0.9278
  val_F1: 0.9189
```

### 自定义扩展

如需进一步扩展日志功能，可以在`myLogger`类中添加新的指标或方法：

```python
# 添加新指标示例
def initialize(self):
    super().initialize()
    # 添加自定义指标
    self.output['new_metric'] = []
    
def log_once(self, *args, **kwargs):
    # ... 现有代码 ...
    # 记录新指标
    new_metric_value = calculate_new_metric()
    self.output['new_metric'].append(new_metric_value)
```

## 使用

### 运行示例

```bash
python main.py --algorithm asyncfl --data_path data/car_hacking.csv --num_clients 10 --global_rounds 100 --use_dp
```

### 使用差分隐私

```bash
python main.py --algorithm asyncfl --use_dp --epsilon_total 5.0 --delta 1e-5 --clip_norm 1.0 --noise_scale 2.0 \
--adaptive_noise --noise_decay_rate 0.01 --gamma 0.5 \
--budget_allocation_strategy rdp_optimal --expected_total_rounds 100 --max_budget_per_round 0.2 --sensitivity_method max
```

### 设置噪声基准模式

```bash
# 使用固定初始噪声值作为基准
python main.py --algorithm asyncfl --use_dp --noise_base_mode fixed --initial_max_noise 5.0

# 使用动态调整的噪声值作为基准（默认方式）
python main.py --algorithm asyncfl --use_dp --noise_base_mode dynamic --noise_scale 2.0
```

### 设置噪声衰减模式

```bash
# 使用指数衰减模式（默认方式）
python main.py --algorithm asyncfl --use_dp --noise_decay_mode exponential --noise_decay_rate 0.01

# 使用双曲衰减模式
python main.py --algorithm asyncfl --use_dp --noise_decay_mode hyperbolic --hyperbolic_decay_k 0.05
```

### 使用通信压缩

```bash
python main.py --algorithm asyncfl --use_compression --compression_method qsgd --compression_bits 4
```

### 不同数据分区方式

```bash
# IID分区
python main.py --algorithm asyncfl --partitioner_name IIDPartitioner

# Dirichlet分区（控制异构性）
python main.py --algorithm asyncfl --partitioner_name DirichletPartitioner --dirichlet_alpha 0.1

# 分片分区（标签偏斜）
python main.py --algorithm asyncfl --partitioner_name ShardPartitioner --num_shards_per_client 2

# 多样性分区（模拟非iid场景）
python main.py --algorithm asyncfl --partitioner_name DiversityPartitioner --diversity 0.7
```

### 主要参数

数据集参数：
- `--data_path`: 数据集路径
- `--seq_len`: 序列长度
- `--stride`: 滑动窗口步长
- `--test_size`: 测试集比例

模型参数：
- `--input_size`: 输入特征维度
- `--hidden_size`: LSTM隐藏层维度
- `--d_model`: Transformer模型维度
- `--nhead`: 注意力头数
- `--num_encoder_layers`: Transformer编码器层数
- `--num_classes`: 分类类别数
- `--k_ratio`: 稀疏注意力的Top-K比例

联邦学习参数：
- `--num_clients`: 客户端数量
- `--num_clients_per_round`: 每轮参与的客户端数量 (注意：asyncfl使用 `buffer_size` 控制聚合频率)
- `--buffer_size`: 异步更新缓冲区大小
- `--global_rounds`: 全局轮数
- `--num_epochs`: 本地训练轮数
- `--batch_size`: 训练批次大小
- `--learning_rate`: 学习率
- `--optimizer`: 优化器类型
- `--partitioner_name`: 数据分区方式
- `--lr_scheme`: 学习率方案(0:固定, 1:基于Staleness范数比率, 2:基于Staleness轮次差和隐私预算)

差分隐私参数：
- `--use_dp`: 是否使用差分隐私
- `--epsilon_total`: 总隐私预算
- `--delta`: 隐私松弛参数
- `--clip_norm`: 梯度裁剪阈值
- `--noise_scale`: 噪声尺度，dynamic模式下作为σ_{initial_base}
- `--adaptive_noise`: 是否使用自适应噪声 (客户端)
- `--noise_decay_mode`: 噪声衰减模式，可选值为'exponential'(指数衰减)或'hyperbolic'(双曲衰减)
- `--noise_decay_rate`: 指数衰减模式下的衰减率，控制噪声随训练轮次增加而减小的速度
- `--hyperbolic_decay_k`: 双曲衰减模式下的系数k，用于计算σ_k^2 = σ_{initial_base}^2 / (1 + k * t_{global})
- `--gamma`: 噪声自适应因子权重 (客户端)，用于计算 β_k = γ·(1-sim_k) + (1-γ)·L̂_k
- `--noise_base_mode`: 噪声基准模式，可选值为'fixed'或'dynamic'
- `--initial_max_noise`: 固定模式下的初始最大噪声值，默认为5.0
- `--alphas`: RDP阶数列表
- `--budget_allocation_strategy`: 服务器端预算分配策略 (`fixed`, `uniform`, `rdp_optimal`)
- `--expected_total_rounds`: 预期总轮次
- `--max_budget_per_round`: 每轮最大预算上限
- `--sensitivity_method`: 服务器端敏感度计算方法 (`max` 或 `median`)

通信压缩参数：
- `--use_compression`: 是否使用通信压缩
- `--compression_method`: 压缩方法，当前支持`qsgd`
- `--compression_bits`: QSGD量化位数(2, 4, 8, 16)

模型历史管理参数：
- `--history_size`: 最多保存的历史版本数
- `--checkpoint_interval`: 完整模型检查点间隔

其他参数：
- `--seed`: 随机种子
- `--device`: 训练设备
- `--output_dir`: 输出目录

## 模型架构

该项目采用LSTM+Transformer的混合架构，包含以下关键组件：

1. **LSTM特征编码器**：提取时序特征
2. **位置编码**：为Transformer添加序列位置信息
3. **Top-K稀疏多头注意力**：提高模型效率和性能，只保留最重要的注意力分数
4. **Transformer编码器**：捕获复杂特征之间的关系
5. **分类器**：对入侵行为进行分类

## 评估指标与可视化

系统提供全面的评估指标和可视化功能：

1. **多维指标**：
   - 准确率(Accuracy)：正确分类的整体能力
   - 召回率(Recall)：检测所有实际入侵的能力
   - 精确率(Precision)：预测为入侵时的准确性
   - F1分数：精确率和召回率的平衡指标
   - 误报率(False Alarm Rate)：错误将正常行为识别为入侵的比例

2. **可视化功能**：
   - 指标演变曲线：展示各项指标随训练轮次的变化
   - 客户端性能分布：分析不同客户端之间的性能差异
   - 客户端准确率热力图：展示每个客户端随轮次的准确率变化
   - 混淆矩阵：详细分析模型在各类别上的预测情况

## 引用

如果您在研究中使用了本项目，请引用以下论文：

[论文引用信息]

## 许可证

[许可证信息] 