"""
车联网入侵检测系统配置文件。
为flgo提供必要的模型和数据集配置。
"""
import os
import sys
import pickle
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import TensorDataset, Dataset

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from flgoasyn.models.transformer_ids_model import TransformerIDSModel
from flgoasyn.models.cnn_lstm_ids_model import CNNLSTMIDSModel
from flgoasyn.models.sparse_transformer_ids_model import SparseTransformerIDSModel
from flgoasyn.models.contact_transformer_ids_model import ContactTransformerIDSModel
from flgoasyn.models.sparse_transformer_ids_model_original import SparseTransformerIDSModel as SparseTransformerIDSModelOriginal
from flgoasyn.data import load_data, load_sequence_data

# 自定义数据集类，行为类似于torchvision.datasets.CIFAR100
class CustomDataset(Dataset):
    """自定义数据集类，使其行为类似于torchvision.datasets.CIFAR100。
    
    每个样本是(image, label)元组，其中image是tensor，label是整数。
    
    Args:
        features (torch.Tensor): 特征数据
        labels (torch.Tensor): 标签数据
    """
    def __init__(self, features, labels):
        """初始化数据集。
        
        Args:
            features (torch.Tensor): 特征数据
            labels (torch.Tensor): 标签数据
        """
        self.features = features
        self.labels = labels
        
    def __len__(self):
        """返回数据集大小。
        
        Returns:
            int: 数据集中的样本数量
        """
        return len(self.features)
    
    def __getitem__(self, idx):
        """获取指定索引的样本。
        
        Args:
            idx (int): 样本索引
            
        Returns:
            tuple: (feature, label) 元组，其中feature是tensor，label是整数
        """
        feature = self.features[idx]
        label = self.labels[idx].item()  # 转换为Python整数
        return feature, label

# 设置随机种子以确保实验可重复
def set_seed(seed: int) -> None:
    """设置随机种子以确保实验可重复。
    
    Args:
        seed: 随机种子
    """
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def get_model() -> nn.Module:
    """获取模型实例。
    
    从环境变量获取用户在命令行中设置的模型参数，使模型配置与main.py中用户输入参数一致。
    可以选择不同类型的模型：TransformerIDSModel、CNNLSTMIDSModel或SparseTransformerIDSModel。
    
    Returns:
        nn.Module: 创建的模型实例
    """
    # 从环境变量获取参数，如果不存在则使用默认值
    input_size = int(os.environ.get('IDS_INPUT_SIZE', 16))
    hidden_size = int(os.environ.get('IDS_HIDDEN_SIZE', 128))
    d_model = int(os.environ.get('IDS_D_MODEL', 256))
    nhead = int(os.environ.get('IDS_NHEAD', 8))
    num_encoder_layers = int(os.environ.get('IDS_NUM_ENCODER_LAYERS', 3))
    dim_feedforward = int(os.environ.get('IDS_DIM_FEEDFORWARD', 128))
    num_classes = int(os.environ.get('IDS_NUM_CLASSES', 2))
    dropout = float(os.environ.get('IDS_DROPOUT', 0.1))
    k_ratio = float(os.environ.get('IDS_K_RATIO', 0.4))
    
    # 特征编码器相关参数
    encoder_type = os.environ.get('IDS_ENCODER_TYPE', 'cnn')
    cnn_kernel_size = int(os.environ.get('IDS_CNN_KERNEL_SIZE', 5))
    cnn_num_layers = int(os.environ.get('IDS_CNN_NUM_LAYERS', 3))
    lstm_num_layers = int(os.environ.get('IDS_LSTM_NUM_LAYERS', 2))
    bidirectional = os.environ.get('IDS_BIDIRECTIONAL', 'True').lower() == 'true'
    
    # 模型类型选择
    model_type = os.environ.get('IDS_MODEL_TYPE', 'transformer')  # 'transformer', 'cnn_lstm', 'sparse_transformer', 'contact_transformer', 'sparse_transformer_original'
    
    # 获取数据集名称（如果环境变量中设置了）
    data_name = os.environ.get('IDS_DATA_NAME', 'default')
    
    if 'carhacking' in data_name:
        # 根据车辆入侵检测数据集特点进行调整
        if 'IDS_INPUT_SIZE' in os.environ:
            input_size = 11
        if 'IDS_NUM_CLASSES' in os.environ:
            num_classes = 5
    elif 'toniot' in data_name:
        # 根据TON_IOT数据集特点进行调整
        if 'IDS_INPUT_SIZE' in os.environ:
            input_size = 42
        if 'IDS_NUM_CLASSES' in os.environ:
            num_classes = 10
    elif 'cicids2017' in data_name:
        # 根据CIC-IDS数据集特点进行调整
        if 'IDS_INPUT_SIZE' in os.environ:
            input_size = 78
        if 'IDS_NUM_CLASSES' in os.environ:
            num_classes = 8
    elif 'carh' in data_name:
        # 根据CIC-IDS数据集特点进行调整
        if 'IDS_INPUT_SIZE' in os.environ:
            input_size = 11
        if 'IDS_NUM_CLASSES' in os.environ:
            num_classes = 5
    
    # 根据模型类型创建相应的模型实例
    if model_type == 'transformer':
        model = TransformerIDSModel(
            input_size=input_size,
            hidden_size=hidden_size,
            d_model=d_model,
            nhead=nhead,
            num_encoder_layers=num_encoder_layers,
            dim_feedforward=dim_feedforward,
            num_classes=num_classes,
            dropout=dropout,
            k_ratio=k_ratio,
            encoder_type=encoder_type,
            cnn_kernel_size=cnn_kernel_size,
            cnn_num_layers=cnn_num_layers
        )
        # print(f"创建TransformerIDSModel，参数：input_size={input_size}, hidden_size={hidden_size}, d_model={d_model}, "
        #       f"nhead={nhead}, num_encoder_layers={num_encoder_layers}, dim_feedforward={dim_feedforward}, "
        #       f"num_classes={num_classes}, dropout={dropout}, k_ratio={k_ratio}, encoder_type={encoder_type}")
    
    elif model_type == 'cnn_lstm':
        model = CNNLSTMIDSModel(
            input_size=input_size,
            hidden_size=hidden_size,
            num_classes=num_classes,
            dropout=dropout,
            encoder_type=encoder_type,
            cnn_kernel_size=cnn_kernel_size,
            cnn_num_layers=cnn_num_layers,
            lstm_num_layers=lstm_num_layers,
            bidirectional=bidirectional
        )
        # print(f"创建CNNLSTMIDSModel，参数：input_size={input_size}, hidden_size={hidden_size}, "
        #       f"num_classes={num_classes}, dropout={dropout}, encoder_type={encoder_type}")
    
    elif model_type == 'sparse_transformer':
        model = SparseTransformerIDSModel(
            input_size=input_size,
            d_model=d_model,
            nhead=nhead,
            num_encoder_layers=num_encoder_layers,
            dim_feedforward=dim_feedforward,
            num_classes=num_classes,
            dropout=dropout,
            k_ratio=k_ratio
        )
        # print(f"创建SparseTransformerIDSModel，参数：input_size={input_size}, d_model={d_model}, "
        #       f"nhead={nhead}, num_encoder_layers={num_encoder_layers}, dim_feedforward={dim_feedforward}, "
        #       f"num_classes={num_classes}, dropout={dropout}, k_ratio={k_ratio}")
    
    elif model_type == 'contact_transformer':
        model = ContactTransformerIDSModel(
            input_size=input_size,
            hidden_size=hidden_size,
            d_model=d_model,
            nhead=nhead,
            num_encoder_layers=num_encoder_layers,
            dim_feedforward=dim_feedforward,
            num_classes=num_classes,
            dropout=dropout,
            k_ratio=k_ratio,
            encoder_type=encoder_type,
            cnn_kernel_size=cnn_kernel_size,
            cnn_num_layers=cnn_num_layers,
            use_multi_scale=os.environ.get('IDS_USE_MULTI_SCALE', 'False').lower() == 'true',
            window_sizes=[int(x) for x in os.environ.get('IDS_WINDOW_SIZES', '3,5,7').split(',')],
            use_glu=os.environ.get('IDS_USE_GLU', 'True').lower() == 'true',
            pre_norm=os.environ.get('IDS_PRE_NORM', 'True').lower() == 'true',
        )
        # print(f"创建ContactTransformerIDSModel，参数：input_size={input_size}, hidden_size={hidden_size}, "
        #       f"d_model={d_model}, nhead={nhead}, num_encoder_layers={num_encoder_layers}, "
        #       f"dim_feedforward={dim_feedforward}, num_classes={num_classes}, dropout={dropout}, "
        #       f"k_ratio={k_ratio}, encoder_type={encoder_type}, use_multi_scale={os.environ.get('IDS_USE_MULTI_SCALE', 'False').lower() == 'true'}, "
        #       f"window_sizes={[int(x) for x in os.environ.get('IDS_WINDOW_SIZES', '3,5,7').split(',')]}")
    
    elif model_type == 'sparse_transformer_original':
        # 使用原始稀疏Transformer模型
        window_sizes = [int(x) for x in os.environ.get('IDS_WINDOW_SIZES', '3,5,7').split(',')]
        use_glu = os.environ.get('IDS_USE_GLU', 'True').lower() == 'true'
        pre_norm = os.environ.get('IDS_PRE_NORM', 'True').lower() == 'true'
        use_multi_scale = os.environ.get('IDS_USE_MULTI_SCALE', 'True').lower() == 'true'
        
        model = SparseTransformerIDSModelOriginal(
            input_size=input_size,
            d_model=d_model,
            nhead=nhead,
            num_encoder_layers=num_encoder_layers,
            dim_feedforward=dim_feedforward,
            num_classes=num_classes,
            dropout=dropout,
            k_ratio=k_ratio,
            use_glu=use_glu,
            pre_norm=pre_norm,
            use_multi_scale=use_multi_scale,
            window_sizes=window_sizes
        )
        # print(f"创建SparseTransformerIDSModelOriginal，参数：input_size={input_size}, d_model={d_model}, "
        #       f"nhead={nhead}, num_encoder_layers={num_encoder_layers}, dim_feedforward={dim_feedforward}, "
        #       f"num_classes={num_classes}, dropout={dropout}, k_ratio={k_ratio}, use_glu={use_glu}, "
        #       f"pre_norm={pre_norm}, use_multi_scale={use_multi_scale}, window_sizes={window_sizes}")
    
    else:
        raise ValueError(f"不支持的模型类型: {model_type}，请选择 'transformer', 'cnn_lstm', 'sparse_transformer', 'contact_transformer' 或 'sparse_transformer_original'")
    
    return model

# 加载和处理数据
# 设置随机种子
seed = int(os.environ.get('IDS_SEED', 42))
set_seed(seed)

# 从环境变量获取数据参数
data_path = os.environ.get('IDS_DATA_PATH', 'D:/experiment/PFL/flgoasyn/benchmark/RawData/cicids2017.csv')
data_name = os.environ.get('IDS_DATA_NAME', 'cicids2017')
seq_len = int(os.environ.get('IDS_SEQ_LEN', 1))
stride = int(os.environ.get('IDS_STRIDE', 1))
test_size = float(os.environ.get('IDS_TEST_SIZE', 0.3))
input_size = int(os.environ.get('IDS_INPUT_SIZE', 16))
num_classes = int(os.environ.get('IDS_NUM_CLASSES', 2))

# 获取环境变量中的客户端数量，如果未设置则使用默认值
num_clients = int(os.environ.get('IDS_NUM_CLIENTS', 20))

# 初始化数据变量
train_data = None
test_data = None
val_data = None

# 准备标签列名
label_col = os.environ.get('IDS_LABEL_COL', 'Label')
# 如果没有设置环境变量，则根据数据集名称设置默认值
# if 'IDS_LABEL_COL' not in os.environ:
if 'carhacking' in data_name.lower():
    label_col = 'Label'
elif 'cicids2017' in data_name.lower():
    label_col = 'Label'
elif 'toniot' in data_name.lower():
    label_col = 'Label'
elif 'carh' in data_name.lower():
    label_col = 'Label'


try:
    # 修改输出目录路径，使用__file__确保路径计算正确
    current_file = os.path.abspath(__file__)
    output_dir = os.path.abspath(os.path.join(os.path.dirname(current_file), "../output"))
    
    print(f"当前配置文件路径: {current_file}")
    print(f"设置输出目录路径: {output_dir}")
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        print(f"创建输出目录: {output_dir}")
    
    # 初始化变量
    processed_data_found = False
    processed_data_path = ""
    
    # 如果提供了数据名称，构造对应的数据文件路径
    if data_name:
        # 对于data_name为"random"的特殊情况，寻找benchmark_data_random.pkl
        if data_name.lower() == "random":
            random_data_path = os.path.join(output_dir, f"benchmark_data_random.pkl")
            if os.path.exists(random_data_path):
                processed_data_path = random_data_path
                processed_data_found = True
                print(f"找到随机数据文件: {processed_data_path}")
        else:
            # 处理正常数据名称情况，移除客户端数量和序列长度信息
            specific_data_path = os.path.join(output_dir, f"benchmark_data_{data_name}.pkl")
            if os.path.exists(specific_data_path):
                processed_data_path = specific_data_path
                processed_data_found = True
                print(f"找到指定数据文件: {processed_data_path}")
    
    # 如果没找到指定名称文件，尝试查找任何benchmark数据文件
    if not processed_data_found and os.path.exists(output_dir):
        data_files = [f for f in os.listdir(output_dir) if f.startswith('benchmark_data_') and f.endswith('.pkl')]
        if data_files:
            latest_file = max(data_files, key=lambda x: os.path.getmtime(os.path.join(output_dir, x)))
            processed_data_path = os.path.join(output_dir, latest_file)
            processed_data_found = True
            print(f"找到最新数据文件: {processed_data_path}")
    
    # 尝试查找默认数据文件
    if not processed_data_found:
        default_data_path = os.path.join(output_dir, f"benchmark_data.pkl")
        if os.path.exists(default_data_path):
            processed_data_path = default_data_path
            processed_data_found = True
            print(f"找到默认数据文件: {processed_data_path}")
    
    # 如果找到了预处理好的数据，加载数据
    if processed_data_found:
        print(f"加载预处理数据文件: {processed_data_path}")
        with open(processed_data_path, "rb") as f:
            data_dict = pickle.load(f)
            X_train = data_dict.get("X_train")
            y_train = data_dict.get("y_train")
            X_test = data_dict.get("X_test")
            y_test = data_dict.get("y_test")
            
            # 添加关于序列数据形状的打印信息
            if seq_len > 1:
                print(f"加载的是序列数据，形状: X_train={X_train.shape}, y_train={y_train.shape}")
            
            # 输出数据统计信息
            if y_train is not None:
                unique_classes = np.unique(y_train).astype(int)
                class_counts = {int(c): np.sum(y_train == c) for c in unique_classes}
                print("\n========== 训练数据统计信息 ==========")
                print(f"样本总数: {len(y_train)}")
                print(f"特征维度: {X_train.shape[2] if len(X_train.shape) >= 3 else 1}")
                print(f"类别数量: {len(unique_classes)}")
                print("类别分布:")
                for cls, count in class_counts.items():
                    percentage = 100.0 * count / len(y_train)
                    print(f"  类别 {cls}: {count} 样本 ({percentage:.2f}%)")
            
            if y_test is not None:
                unique_classes_test = np.unique(y_test).astype(int)
                class_counts_test = {int(c): np.sum(y_test == c) for c in unique_classes_test}
                print("\n========== 测试数据统计信息 ==========")
                print(f"样本总数: {len(y_test)}")
                print(f"类别数量: {len(unique_classes_test)}")
                print("类别分布:")
                for cls, count in class_counts_test.items():
                    percentage = 100.0 * count / len(y_test)
                    print(f"  类别 {cls}: {count} 样本 ({percentage:.2f}%)")
            
            # 将加载的NumPy数组转换为PyTorch张量
            if X_train is not None and y_train is not None:
                X_train_tensor = torch.tensor(X_train, dtype=torch.float32)
                y_train_tensor = torch.tensor(y_train, dtype=torch.long)
                # 创建训练数据集
                train_data = CustomDataset(X_train_tensor, y_train_tensor)
            
            if X_test is not None and y_test is not None:
                X_test_tensor = torch.tensor(X_test, dtype=torch.float32)
                y_test_tensor = torch.tensor(y_test, dtype=torch.long)
                # 创建测试数据集
                test_data = CustomDataset(X_test_tensor, y_test_tensor)
            
            # 保存处理后的数据到指定名称文件
            if data_name:
                processed_data_path = os.path.join(output_dir, f"benchmark_data_{data_name}.pkl")
                with open(processed_data_path, "wb") as f:
                    data_dict = {
                        "X_train": X_train,
                        "y_train": y_train,
                        "X_test": X_test,
                        "y_test": y_test
                    }
                    pickle.dump(data_dict, f)
                print(f"已保存处理后的数据到: {processed_data_path}")
                print(f"保存的数据形状: X_train={X_train.shape}, X_test={X_test.shape}")
            
            # 同时创建一个默认备份
            backup_path = os.path.join(output_dir, f"benchmark_data.pkl")
            with open(backup_path, "wb") as f:
                pickle.dump(data_dict, f)
            print(f"已创建数据备份: {backup_path}")
            if seq_len > 1:
                print(f"备份的序列数据维度: 序列长度={X_train.shape[1]}, 特征维度={X_train.shape[2]}")
        
    # 如果没找到预处理数据但有原始数据路径，则从原始数据加载并处理
    elif data_path and os.path.exists(data_path):
        print(f"从原始数据路径加载数据: {data_path}")
        try:
            # 使用序列模型时，使用load_sequence_data
            if seq_len > 1:
                print(f"使用序列数据，序列长度={seq_len}，步长={stride}")
                data_dict = load_sequence_data(
                    data_path=data_path,
                    label_col=label_col,
                    seq_length=seq_len,
                    stride=stride, 
                    test_size=test_size,
                    valid_size=None,  # 不使用验证集
                    random_state=seed,
                    normalize=True,
                    standardize=False
                )
                X_train = data_dict['X_train']
                y_train = data_dict['y_train']
                X_test = data_dict['X_test']
                y_test = data_dict['y_test']
                
                # 这里不需要再转换为三维结构，因为load_sequence_data已经返回了正确的三维形状
                print(f"序列数据已就绪，形状: X_train={X_train.shape}, y_train={y_train.shape}")
            else:
                # 使用普通数据加载
                data_dict = load_data(
                    data_path=data_path,
                    label_col=label_col,
                    test_size=test_size,
                    valid_size=None,  # 不使用验证集
                    random_state=seed,
                    normalize=True,
                    standardize=False
                )
                X_train = data_dict['X_train']
                y_train = data_dict['y_train']
                X_test = data_dict['X_test']
                y_test = data_dict['y_test']
            
            # 输出数据统计信息
            if y_train is not None:
                unique_classes = np.unique(y_train).astype(int)
                class_counts = {int(c): np.sum(y_train == c) for c in unique_classes}
                print("\n========== 训练数据统计信息 ==========")
                print(f"样本总数: {len(y_train)}")
                print(f"特征维度: {X_train.shape[2] if len(X_train.shape) >= 3 else 1}")
                print(f"类别数量: {len(unique_classes)}")
                print("类别分布:")
                for cls, count in class_counts.items():
                    percentage = 100.0 * count / len(y_train)
                    print(f"  类别 {cls}: {count} 样本 ({percentage:.2f}%)")
            
            if y_test is not None:
                unique_classes_test = np.unique(y_test).astype(int)
                class_counts_test = {int(c): np.sum(y_test == c) for c in unique_classes_test}
                print("\n========== 测试数据统计信息 ==========")
                print(f"样本总数: {len(y_test)}")
                print(f"类别数量: {len(unique_classes_test)}")
                print("类别分布:")
                for cls, count in class_counts_test.items():
                    percentage = 100.0 * count / len(y_test)
                    print(f"  类别 {cls}: {count} 样本 ({percentage:.2f}%)")
            
            # 转换为PyTorch张量
            X_train_tensor = torch.tensor(X_train, dtype=torch.float32)
            y_train_tensor = torch.tensor(y_train, dtype=torch.long)
            X_test_tensor = torch.tensor(X_test, dtype=torch.float32)
            y_test_tensor = torch.tensor(y_test, dtype=torch.long)
            
            # 创建数据集
            train_data = CustomDataset(X_train_tensor, y_train_tensor)
            test_data = CustomDataset(X_test_tensor, y_test_tensor)
            
            # 保存处理后的数据到指定名称文件
            if data_name:
                processed_data_path = os.path.join(output_dir, f"benchmark_data_{data_name}.pkl")
                with open(processed_data_path, "wb") as f:
                    data_dict = {
                        "X_train": X_train,
                        "y_train": y_train,
                        "X_test": X_test,
                        "y_test": y_test
                    }
                    pickle.dump(data_dict, f)
                print(f"已保存处理后的数据到: {processed_data_path}")
            
            # 同时创建一个默认备份
            backup_path = os.path.join(output_dir, f"benchmark_data.pkl")
            with open(backup_path, "wb") as f:
                pickle.dump(data_dict, f)
            print(f"已创建数据备份: {backup_path}")
        
        except Exception as e:
            print(f"从原始数据加载失败: {str(e)}")
            raise
    
    # 如果既没有预处理数据也没有原始数据，则创建随机数据
    else:
        print("未找到数据文件或原始数据路径，将使用随机数据")
        # 生成随机数据
        num_samples = 1000
        
        if seq_len > 1:
            # 为序列数据生成适当的三维结构 [数据大小, 序列长度, 特征维度]
            print(f"生成随机序列数据，序列长度={seq_len}")
            X_train = np.random.randn(num_samples, seq_len, input_size)
            y_train = np.random.randint(0, num_classes, size=num_samples)
            X_test = np.random.randn(num_samples // 5, seq_len, input_size)
            y_test = np.random.randint(0, num_classes, size=num_samples // 5)
            print(f"生成的随机序列数据形状: X_train={X_train.shape}, y_train={y_train.shape}")
        else:
            # 直接生成三维数据结构 [数据大小, 1, 特征维度]
            X_train = np.random.randn(num_samples, 1, input_size)
            y_train = np.random.randint(0, num_classes, size=num_samples)
            X_test = np.random.randn(num_samples // 5, 1, input_size)
            y_test = np.random.randint(0, num_classes, size=num_samples // 5)
        
        # 输出数据统计信息
        if y_train is not None:
            unique_classes = np.unique(y_train).astype(int)
            class_counts = {int(c): np.sum(y_train == c) for c in unique_classes}
            print("\n========== 随机训练数据统计信息 ==========")
            print(f"样本总数: {len(y_train)}")
            print(f"特征维度: {X_train.shape[2] if len(X_train.shape) >= 3 else 1}")
            print(f"类别数量: {len(unique_classes)}")
            print("类别分布:")
            for cls, count in class_counts.items():
                percentage = 100.0 * count / len(y_train)
                print(f"  类别 {cls}: {count} 样本 ({percentage:.2f}%)")
        
        if y_test is not None:
            unique_classes_test = np.unique(y_test).astype(int)
            class_counts_test = {int(c): np.sum(y_test == c) for c in unique_classes_test}
            print("\n========== 随机测试数据统计信息 ==========")
            print(f"样本总数: {len(y_test)}")
            print(f"类别数量: {len(unique_classes_test)}")
            print("类别分布:")
            for cls, count in class_counts_test.items():
                percentage = 100.0 * count / len(y_test)
                print(f"  类别 {cls}: {count} 样本 ({percentage:.2f}%)")
        
        # 转换为PyTorch张量
        X_train_tensor = torch.tensor(X_train, dtype=torch.float32)
        y_train_tensor = torch.tensor(y_train, dtype=torch.long)
        X_test_tensor = torch.tensor(X_test, dtype=torch.float32)
        y_test_tensor = torch.tensor(y_test, dtype=torch.long)
        
        # 创建数据集
        train_data = CustomDataset(X_train_tensor, y_train_tensor)
        test_data = CustomDataset(X_test_tensor, y_test_tensor)
        
        # 保存随机数据
        processed_data_path = os.path.join(output_dir, f"benchmark_data_random.pkl")
            
        with open(processed_data_path, "wb") as f:
            data_dict = {
                "X_train": X_train,
                "y_train": y_train,
                "X_test": X_test,
                "y_test": y_test
            }
            pickle.dump(data_dict, f)
        print(f"已保存随机数据到: {processed_data_path}")
        
except Exception as e:
    print(f"数据加载或处理出错: {str(e)}")
    # 如果出错，创建空数据结构
    train_data = None
    test_data = None

# 添加辅助函数，根据数据统计提供DirichletPartitioner参数建议
def recommend_dirichlet_parameters(y_train, verbose=True):
    """根据训练数据统计情况推荐DirichletPartitioner参数设置。
    
    Args:
        y_train: 训练数据标签
        verbose: 是否打印详细建议
        
    Returns:
        dict: 包含推荐参数的字典
    """
    if y_train is None:
        return None
    
    # 获取样本数和类别数
    num_samples = len(y_train)
    unique_classes = np.unique(y_train).astype(int)
    num_classes = len(unique_classes)
    
    # 根据数据量推荐客户端数量
    if num_samples < 5000:
        # 小型数据集: 每个客户端至少100个样本
        recommended_clients = min(10, max(2, num_samples // 100))
    elif num_samples < 50000:
        # 中型数据集: 每个客户端至少200个样本
        recommended_clients = min(50, max(5, num_samples // 200))
    else:
        # 大型数据集: 每个客户端至少500个样本
        recommended_clients = min(100, max(10, num_samples // 500))
    
    # 确保客户端数量至少是类别数的2倍
    recommended_clients = max(recommended_clients, num_classes * 2)
    
    # 根据类别数推荐alpha值
    # 类别越多，需要更大的alpha以确保每个客户端有足够多的类别
    if num_classes <= 2:
        # 二分类问题可以用较小的alpha实现非IID
        recommended_alpha = 0.3
    elif num_classes <= 10:
        # 中等类别数，适中的alpha
        recommended_alpha = 0.5
    else:
        # 大量类别，需要更大的alpha以确保分布
        recommended_alpha = 1.0
    
    # 根据数据量推荐error_bar值
    if num_samples < 5000:
        # 小型数据集需要较大的误差容忍度
        recommended_error_bar = 1e-3
    elif num_samples < 50000:
        # 中型数据集
        recommended_error_bar = 1e-4
    else:
        # 大型数据集
        recommended_error_bar = 1e-5
    
    # 创建推荐参数字典
    recommendations = {
        'num_clients': recommended_clients,
        'alpha': recommended_alpha,
        'error_bar': recommended_error_bar
    }
    
    # 打印建议
    if verbose:
        print("\n========== DirichletPartitioner参数建议 ==========")
        print(f"推荐客户端数量 (num_clients): {recommended_clients}")
        print(f"推荐alpha值 (控制非IID程度): {recommended_alpha}")
        print(f"推荐error_bar值 (误差容忍度): {recommended_error_bar:.1e}")
        print("\n建议命令行参数:")
        print(f"--partitioner_name DirichletPartitioner --num_clients {recommended_clients} "
              f"--dirichlet_alpha {recommended_alpha}")
        print("\n如何在代码中设置error_bar:")
        print("task_config['partitioner']['para'] = {")
        print(f"    'num_clients': {recommended_clients},")
        print(f"    'alpha': {recommended_alpha},")
        print(f"    'error_bar': {recommended_error_bar:.1e}")
        print("}")
    
    return recommendations

# # 尝试为已加载的数据提供参数建议
# try:
#     if 'y_train' in locals() and y_train is not None:
#         recommend_dirichlet_parameters(y_train)
# except Exception as e:
#     print(f"生成参数建议时出错: {str(e)}")


# ========== 模型参数计算量和FLOPs计算功能 ==========

def count_parameters(model: nn.Module, trainable_only: bool = True) -> dict:
    """计算模型的参数数量。

    Args:
        model: PyTorch模型
        trainable_only: 是否只计算可训练参数，默认为True

    Returns:
        dict: 包含参数统计信息的字典
    """
    if trainable_only:
        total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        param_details = {name: p.numel() for name, p in model.named_parameters() if p.requires_grad}
    else:
        total_params = sum(p.numel() for p in model.parameters())
        param_details = {name: p.numel() for name, p in model.named_parameters()}

    # 计算参数大小（以MB为单位，假设float32）
    param_size_mb = total_params * 4 / (1024 * 1024)

    return {
        'total_params': total_params,
        'param_size_mb': param_size_mb,
        'param_details': param_details,
        'trainable_only': trainable_only
    }


def calculate_flops_transformer(model, input_shape: tuple, seq_len: int = 1) -> dict:
    """计算Transformer模型的FLOPs（浮点运算次数）。

    Args:
        model: Transformer模型实例
        input_shape: 输入形状 (batch_size, seq_len, input_size)
        seq_len: 序列长度

    Returns:
        dict: 包含FLOPs统计信息的字典
    """
    batch_size, sequence_length, input_size = input_shape

    # 获取模型参数
    d_model = getattr(model, 'd_model', 256)
    nhead = getattr(model, 'nhead', 8)
    num_layers = getattr(model, 'num_encoder_layers', 3)
    dim_feedforward = getattr(model, 'dim_feedforward', 128)

    flops_breakdown = {}

    # 1. 输入嵌入层 FLOPs
    # Linear: input_size -> d_model
    embedding_flops = batch_size * sequence_length * input_size * d_model
    flops_breakdown['embedding'] = embedding_flops

    # 2. Transformer编码器层 FLOPs
    encoder_flops = 0

    for layer_idx in range(num_layers):
        layer_flops = 0

        # Multi-Head Attention FLOPs
        # Q, K, V projections: 3 * (seq_len * d_model * d_model)
        qkv_proj_flops = 3 * batch_size * sequence_length * d_model * d_model

        # Attention computation: Q @ K^T
        attention_flops = batch_size * nhead * sequence_length * sequence_length * (d_model // nhead)

        # Attention @ V
        attention_v_flops = batch_size * nhead * sequence_length * sequence_length * (d_model // nhead)

        # Output projection
        output_proj_flops = batch_size * sequence_length * d_model * d_model

        mha_flops = qkv_proj_flops + attention_flops + attention_v_flops + output_proj_flops
        layer_flops += mha_flops

        # Feed Forward Network FLOPs
        # First linear: d_model -> dim_feedforward
        ffn1_flops = batch_size * sequence_length * d_model * dim_feedforward
        # Second linear: dim_feedforward -> d_model
        ffn2_flops = batch_size * sequence_length * dim_feedforward * d_model

        ffn_flops = ffn1_flops + ffn2_flops
        layer_flops += ffn_flops

        encoder_flops += layer_flops
        flops_breakdown[f'encoder_layer_{layer_idx}'] = layer_flops

    flops_breakdown['total_encoder'] = encoder_flops

    # 3. 分类头 FLOPs
    num_classes = getattr(model, 'num_classes', 2)
    classifier_flops = batch_size * d_model * num_classes
    flops_breakdown['classifier'] = classifier_flops

    # 总FLOPs
    total_flops = embedding_flops + encoder_flops + classifier_flops
    flops_breakdown['total'] = total_flops

    # 转换为更易读的单位
    flops_breakdown['total_gflops'] = total_flops / 1e9
    flops_breakdown['total_mflops'] = total_flops / 1e6

    return flops_breakdown


def calculate_flops_cnn_lstm(model, input_shape: tuple) -> dict:
    """计算CNN-LSTM模型的FLOPs。

    Args:
        model: CNN-LSTM模型实例
        input_shape: 输入形状 (batch_size, seq_len, input_size)

    Returns:
        dict: 包含FLOPs统计信息的字典
    """
    batch_size, sequence_length, input_size = input_shape

    # 获取模型参数
    hidden_size = getattr(model, 'hidden_size', 128)
    num_classes = getattr(model, 'num_classes', 2)

    flops_breakdown = {}

    # 1. CNN编码器 FLOPs（如果存在）
    cnn_flops = 0
    if hasattr(model, 'encoder') and hasattr(model.encoder, 'conv_layers'):
        # 简化的CNN FLOPs计算
        # 假设每个卷积层的FLOPs = batch_size * output_size * kernel_size * input_channels * output_channels
        cnn_flops = batch_size * sequence_length * input_size * hidden_size

    flops_breakdown['cnn_encoder'] = cnn_flops

    # 2. LSTM FLOPs
    # LSTM的FLOPs计算：4 * (input_size * hidden_size + hidden_size * hidden_size + hidden_size) per time step
    lstm_flops_per_step = 4 * (input_size * hidden_size + hidden_size * hidden_size + hidden_size)
    lstm_flops = batch_size * sequence_length * lstm_flops_per_step

    flops_breakdown['lstm'] = lstm_flops

    # 3. 分类头 FLOPs
    classifier_flops = batch_size * hidden_size * num_classes
    flops_breakdown['classifier'] = classifier_flops

    # 总FLOPs
    total_flops = cnn_flops + lstm_flops + classifier_flops
    flops_breakdown['total'] = total_flops
    flops_breakdown['total_gflops'] = total_flops / 1e9
    flops_breakdown['total_mflops'] = total_flops / 1e6

    return flops_breakdown


def analyze_model_complexity(model: nn.Module, input_shape: tuple = None,
                           model_type: str = 'auto', verbose: bool = True) -> dict:
    """分析模型的复杂度，包括参数数量和FLOPs。

    Args:
        model: PyTorch模型
        input_shape: 输入形状 (batch_size, seq_len, input_size)
        model_type: 模型类型，'auto', 'transformer', 'cnn_lstm'
        verbose: 是否打印详细信息

    Returns:
        dict: 包含完整复杂度分析的字典
    """
    # 计算参数数量
    param_stats = count_parameters(model, trainable_only=True)

    analysis_result = {
        'parameter_analysis': param_stats,
        'flops_analysis': None
    }

    # 如果提供了输入形状，计算FLOPs
    if input_shape is not None:
        # 自动检测模型类型
        if model_type == 'auto':
            model_class_name = model.__class__.__name__.lower()
            if 'transformer' in model_class_name:
                model_type = 'transformer'
            elif 'cnn' in model_class_name or 'lstm' in model_class_name:
                model_type = 'cnn_lstm'
            else:
                model_type = 'transformer'  # 默认使用transformer计算

        # 计算FLOPs
        if model_type == 'transformer':
            flops_stats = calculate_flops_transformer(model, input_shape)
        elif model_type == 'cnn_lstm':
            flops_stats = calculate_flops_cnn_lstm(model, input_shape)
        else:
            flops_stats = calculate_flops_transformer(model, input_shape)  # 默认

        analysis_result['flops_analysis'] = flops_stats

    # 打印详细信息
    if verbose:
        print("\n" + "="*60)
        print("模型复杂度分析报告")
        print("="*60)

        # 参数统计
        print(f"\n📊 参数统计:")
        print(f"  总参数数量: {param_stats['total_params']:,}")
        print(f"  模型大小: {param_stats['param_size_mb']:.2f} MB")
        print(f"  可训练参数: {'是' if param_stats['trainable_only'] else '否'}")

        # 参数分布（只显示前10个最大的层）
        if param_stats['param_details']:
            sorted_params = sorted(param_stats['param_details'].items(),
                                 key=lambda x: x[1], reverse=True)
            print(f"\n🔍 主要参数分布 (前10层):")
            for i, (name, count) in enumerate(sorted_params[:10]):
                percentage = 100.0 * count / param_stats['total_params']
                print(f"  {i+1:2d}. {name:<40} {count:>10,} ({percentage:5.1f}%)")

        # FLOPs统计
        if analysis_result['flops_analysis'] is not None:
            flops_stats = analysis_result['flops_analysis']
            print(f"\n⚡ FLOPs统计 (输入形状: {input_shape}):")
            print(f"  总FLOPs: {flops_stats['total']:,}")
            print(f"  总FLOPs: {flops_stats['total_gflops']:.3f} GFLOPs")
            print(f"  总FLOPs: {flops_stats['total_mflops']:.1f} MFLOPs")

            # FLOPs分布
            print(f"\n🔍 FLOPs分布:")
            for key, value in flops_stats.items():
                if key not in ['total', 'total_gflops', 'total_mflops'] and not key.startswith('encoder_layer_'):
                    percentage = 100.0 * value / flops_stats['total'] if flops_stats['total'] > 0 else 0
                    print(f"  {key:<20} {value:>15,} ({percentage:5.1f}%)")

        print("="*60)

    return analysis_result


def get_model_complexity_summary(model: nn.Module, input_shape: tuple = None) -> str:
    """获取模型复杂度的简要摘要字符串。

    Args:
        model: PyTorch模型
        input_shape: 输入形状

    Returns:
        str: 复杂度摘要字符串
    """
    analysis = analyze_model_complexity(model, input_shape, verbose=False)

    param_count = analysis['parameter_analysis']['total_params']
    param_size = analysis['parameter_analysis']['param_size_mb']

    summary = f"参数: {param_count:,} ({param_size:.1f}MB)"

    if analysis['flops_analysis'] is not None:
        gflops = analysis['flops_analysis']['total_gflops']
        summary += f", FLOPs: {gflops:.2f}G"

    return summary


def demo_model_complexity_analysis():
    """演示模型复杂度分析功能的使用方法。"""
    print("\n" + "="*60)
    print("模型复杂度分析功能演示")
    print("="*60)

    try:
        # 获取当前配置的模型
        model = get_model()

        # 从环境变量获取输入参数
        seq_len = int(os.environ.get('IDS_SEQ_LEN', 1))
        input_size = int(os.environ.get('IDS_INPUT_SIZE', 16))
        batch_size = 1  # 用于FLOPs计算的批次大小

        # 定义输入形状
        input_shape = (batch_size, seq_len, input_size)

        print(f"模型类型: {model.__class__.__name__}")
        print(f"输入形状: {input_shape}")

        # 执行完整的复杂度分析
        analysis_result = analyze_model_complexity(
            model=model,
            input_shape=input_shape,
            model_type='auto',
            verbose=True
        )

        # 获取简要摘要
        summary = get_model_complexity_summary(model, input_shape)
        print(f"\n📋 简要摘要: {summary}")

        # 返回分析结果供进一步使用
        return analysis_result

    except Exception as e:
        print(f"模型复杂度分析演示失败: {str(e)}")
        return None


# 使用示例函数
def example_usage():
    """展示如何在代码中使用模型复杂度分析功能。"""

    # 示例1: 基本参数统计
    model = get_model()
    param_stats = count_parameters(model)
    print(f"模型参数数量: {param_stats['total_params']:,}")

    # 示例2: 完整复杂度分析
    input_shape = (1, 1, 16)  # (batch_size, seq_len, input_size)
    analysis = analyze_model_complexity(model, input_shape)

    # 示例3: 获取简要摘要
    summary = get_model_complexity_summary(model, input_shape)
    print(f"模型摘要: {summary}")


# 如果直接运行此文件，执行演示
if __name__ == "__main__":
    demo_model_complexity_analysis()


