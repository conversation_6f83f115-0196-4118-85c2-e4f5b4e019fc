#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
绘制客户端模型更新范数(update_norm)随轮次变化的折线图。
从pkl文件中读取不同客户端的update_norm数据，并绘制范数值随轮次变化的折线图。
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import argparse


def load_update_norms_from_pkl(file_path: str) -> List[Dict]:
    """
    从pkl文件中加载update_norm数据。
    
    参数:
        file_path: pkl文件的路径
        
    返回:
        包含update_norm的列表，每个元素是一个字典，包含'round'和'update_norm'
    """
    try:
        with open(file_path, 'rb') as f:
            update_norms = pickle.load(f)
        
        # 确保返回的是列表
        if not isinstance(update_norms, list):
            update_norms = [update_norms]
            
        return update_norms
    except Exception as e:
        print(f"加载文件 {file_path} 时出错: {e}")
        return []


def extract_update_norms(update_norms_list: List[Dict]) -> Tuple[List[int], List[float]]:
    """
    从update_norms列表中提取轮次和范数数据。
    
    参数:
        update_norms_list: 包含update_norm的列表，每个元素是一个字典
        
    返回:
        轮次列表和对应的update_norm列表的元组
    """
    rounds = []
    norms = []
    
    for record in update_norms_list:
        if isinstance(record, dict) and 'round' in record and 'update_norm' in record:
            rounds.append(record['round'])
            norms.append(record['update_norm'])
        elif isinstance(record, (int, float)):
            # 兼容直接存储update_norm值的旧格式
            norms.append(record)
            # 假设轮次从1开始连续递增
            rounds.append(len(rounds) + 1)
    
    # 按轮次排序
    sorted_data = sorted(zip(rounds, norms), key=lambda x: x[0])
    if sorted_data:
        rounds, norms = zip(*sorted_data)
    else:
        rounds, norms = [], []
    
    return list(rounds), list(norms)


def plot_update_norms(
    clients_data: Dict[str, Tuple[List[int], List[float]]],
    title: str = "Model Update Norms",
    xlabel: str = "Rounds",
    ylabel: str = "Update Norm",
    figsize: Tuple[float, float] = (10, 6),
    save_path: Optional[str] = None,
    ylim: Optional[Tuple[float, float]] = None,
    rounds_step: int = 20,
    max_rounds: int = 100,
    linewidth: float = 2.0
) -> None:
    """
    绘制多个客户端的update_norm值比较折线图，不带标记。
    
    参数:
        clients_data: 字典，格式为 {客户端名: (轮次列表, update_norm列表)}
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        figsize: 图表大小
        save_path: 保存路径，如果为None则不保存
        ylim: y轴范围，格式为(ymin, ymax)
        rounds_step: 轮次标签的步长
        max_rounds: 最大轮次数
        linewidth: 线宽
    """
    # 设置全局字体为 Times New Roman
    plt.rcParams['font.family'] = 'Times New Roman'
    plt.rcParams['mathtext.fontset'] = 'stix'
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    plt.figure(figsize=figsize)
    
    # 定义线型和颜色
    linestyles = ['-', '--', '-.', ':']
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    # 绘制每个客户端的折线
    for i, (client_name, (rounds, norm_values)) in enumerate(clients_data.items()):
        # 确保数据长度不超过最大轮次
        filtered_data = [(r, v) for r, v in zip(rounds, norm_values) if r <= max_rounds]
        if filtered_data:
            filtered_rounds, filtered_norms = zip(*filtered_data)
        else:
            filtered_rounds, filtered_norms = [], []
        
        # 绘制折线，不带标记
        plt.plot(filtered_rounds, filtered_norms, 
                 label=client_name,
                 linestyle=linestyles[i % len(linestyles)],
                 color=colors[i % len(colors)],
                 linewidth=linewidth)
    
    # 设置图表属性
    plt.title(title, fontsize=18, family='Times New Roman')
    plt.xlabel(xlabel, fontsize=16, family='Times New Roman')
    plt.ylabel(ylabel, fontsize=16, family='Times New Roman')
    
    # 设置x轴刻度，步长为rounds_step
    x_ticks = list(range(0, max_rounds + 1, rounds_step))
    plt.xticks(x_ticks, fontsize=14, family='Times New Roman')
    
    # 添加 x 轴子刻度并显示数值
    minor_x_ticks = list(range(0, max_rounds + 1, 5))  # 子刻度增幅为5
    # 过滤掉与主刻度重叠的子刻度
    minor_x_ticks = [x for x in minor_x_ticks if x not in x_ticks]
    plt.gca().set_xticks(minor_x_ticks, minor=True)
    plt.gca().set_xticklabels([str(x) for x in minor_x_ticks], minor=True, fontsize=12, family='Times New Roman')
    plt.gca().tick_params(axis='x', which='minor', length=4, color='gray', width=1.0)
    
    # 设置x轴范围
    plt.xlim(0, max_rounds)
    
    # 设置y轴范围和刻度
    if ylim:
        plt.ylim(ylim)
        # 自动计算y轴主刻度间隔
        y_range = ylim[1] - ylim[0]
        if y_range <= 5:
            y_step = 0.5
        elif y_range <= 10:
            y_step = 1.0
        elif y_range <= 50:
            y_step = 5.0
        else:
            y_step = 10.0
        
        y_ticks = np.arange(ylim[0], ylim[1] + 0.01, y_step)
        plt.yticks(y_ticks, fontsize=14, family='Times New Roman')
        
        # 添加 y 轴子刻度
        minor_y_step = y_step / 5
        minor_y_ticks = np.arange(ylim[0], ylim[1] + 0.01, minor_y_step)
        # 过滤掉与主刻度重叠的子刻度
        minor_y_ticks = [y for y in minor_y_ticks if not any(abs(y - main_y) < 1e-6 for main_y in y_ticks)]
        plt.gca().set_yticks(minor_y_ticks, minor=True)
        plt.gca().tick_params(axis='y', which='minor', length=4, color='gray', width=1.0)
    
    # 添加网格线（横向和纵向）
    plt.grid(True, linestyle='--', alpha=0.7)
    # 添加次要网格线
    plt.grid(True, which='minor', linestyle=':', alpha=0.3)
    
    # 添加图例，放在右上角
    legend = plt.legend(fontsize=12, loc='upper right', prop={'family': 'Times New Roman'})
    legend.get_frame().set_linewidth(1.0)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存至: {save_path}")
    
    # 显示图表
    plt.show()


def plot_from_directory(
    data_dir: str,
    output: str = "update_norms_comparison.png",
    title: str = "Model Update Norms Comparison",
    ymin: Optional[float] = None,
    ymax: Optional[float] = None,
    rounds_step: int = 20,
    max_rounds: int = 100,
    linewidth: float = 2.0,
    custom_names: Optional[Dict[str, str]] = None
) -> None:
    """
    从指定目录读取pkl文件并绘制比较图。
    
    参数:
        data_dir: 包含pkl文件的目录路径
        output: 输出图像的文件名
        title: 图表标题
        ymin: y轴最小值，如果为None则自动确定
        ymax: y轴最大值，如果为None则自动确定
        rounds_step: 轮次标签的步长
        max_rounds: 最大轮次数
        linewidth: 线宽
        custom_names: 客户端名称映射字典，格式为 {原客户端名: 显示名称}
    """
    # 获取目录中的所有pkl文件
    if not os.path.exists(data_dir):
        print(f"目录 {data_dir} 不存在")
        return
        
    pkl_files = [f for f in os.listdir(data_dir) if f.endswith('.pkl') and 'update_norms' in f]
    
    if not pkl_files:
        print(f"在目录 {data_dir} 中未找到update_norms的pkl文件")
        return
    
    # 打印找到的文件和自定义名称映射，用于调试
    print("找到的pkl文件:")
    for file in pkl_files:
        print(f"  - {file}")
    
    if custom_names:
        print("\n自定义客户端名称映射:")
        for key, value in custom_names.items():
            print(f"  - {key} -> {value}")
    
    # 读取每个客户端的数据
    clients_data = {}
    all_norm_values = []  # 用于自动确定y轴范围
    
    for file_name in pkl_files:
        file_path = os.path.join(data_dir, file_name)
        
        # 从文件名提取客户端ID
        client_id = file_name.split('_')[1] if '_' in file_name else file_name.split('.')[0]
        display_name = f"Client {client_id}"
        
        # 如果存在自定义名称映射，则使用映射后的名称
        if custom_names and client_id in custom_names:
            display_name = custom_names[client_id]
        
        # 加载update_norms数据
        update_norms_list = load_update_norms_from_pkl(file_path)
        
        # 提取轮次和update_norm数据
        rounds, norm_values = extract_update_norms(update_norms_list)
        
        if rounds and norm_values:
            # 收集所有norm值用于自动确定y轴范围
            all_norm_values.extend(norm_values)
            
            clients_data[display_name] = (rounds, norm_values)
            print(f"成功加载客户端 {client_id} 的数据 (显示为 {display_name})，共 {len(rounds)} 个数据点")
    
    if not clients_data:
        print("未能从pkl文件中提取到有效的update_norm数据")
        return
    
    # 如果未指定y轴范围，自动确定
    if ymin is None or ymax is None:
        if all_norm_values:
            if ymin is None:
                ymin = max(0, min(all_norm_values) * 0.9)  # 留出10%的下边距
            if ymax is None:
                ymax = max(all_norm_values) * 1.1  # 留出10%的上边距
    
    # 绘制比较图
    save_path = os.path.join(data_dir, output)
    plot_update_norms(
        clients_data=clients_data,
        title=title,
        save_path=save_path,
        ylim=(ymin, ymax) if ymin is not None and ymax is not None else None,
        rounds_step=rounds_step,
        max_rounds=max_rounds,
        linewidth=linewidth
    )


def main():
    """主函数，处理命令行参数并执行绘图"""
    parser = argparse.ArgumentParser(description='绘制客户端模型更新范数(update_norm)比较折线图')
    parser.add_argument('--data_dir', type=str, default="C:\\Users\\<USER>\\Desktop\\result\\CICDIS17\\draw",
                        help='包含pkl文件的目录路径')
    parser.add_argument('--output', type=str, default="update_norms_comparison.png",
                        help='输出图表的文件名')
    parser.add_argument('--title', type=str, default="",
                        help='图表标题')
    parser.add_argument('--ymin', type=float, default=2.,
                        help='y轴最小值')
    parser.add_argument('--ymax', type=float, default=5.,
                        help='y轴最大值')
    parser.add_argument('--rounds_step', type=int, default=20,
                        help='轮次标签的步长')
    parser.add_argument('--max_rounds', type=int, default=100,
                        help='最大轮次数')
    parser.add_argument('--linewidth', type=float, default=2.0,
                        help='线宽')
    
    args = parser.parse_args()
    
    # 自定义客户端名称映射
    custom_names = {
        "client_5_update_norms": "Client 1",
        "client_6_update_norms": "Client 2",
        "client_7_update_norms": "Client 3",
        "client_8_update_norms": "Client 4",
        "client_9_update_norms": "Client 5",
        "client_10_update_norms": "Client 6"
    }
    
    # 从文件读取数据并绘图
    plot_from_directory(
        data_dir=args.data_dir,
        output=args.output,
        title=args.title,
        ymin=args.ymin,
        ymax=args.ymax,
        rounds_step=args.rounds_step,
        max_rounds=args.max_rounds,
        linewidth=args.linewidth,
        custom_names=custom_names
    )


if __name__ == "__main__":
    main() 