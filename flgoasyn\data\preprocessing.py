"""
车联网入侵检测数据预处理工具。
提供特征工程、数据清洗和特征选择等功能。
"""
from typing import List, Dict, Tuple, Any, Optional, Union

import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.impute import SimpleImputer
from sklearn.feature_selection import VarianceThreshold, SelectKBest, f_classif


def normalize_data(data: np.ndarray) -> np.ndarray:
    """
    对数据进行归一化处理，将特征缩放到[0,1]范围内。
    
    Args:
        data: 输入特征数据
        
    Returns:
        归一化后的特征数据
    """
    scaler = MinMaxScaler()
    return scaler.fit_transform(data)


def standardize_data(data: np.ndarray) -> np.ndarray:
    """
    对数据进行标准化处理，使特征均值为0，标准差为1。
    
    Args:
        data: 输入特征数据
        
    Returns:
        标准化后的特征数据
    """
    scaler = StandardScaler()
    return scaler.fit_transform(data)


def robust_scale_data(data: np.ndarray) -> np.ndarray:
    """
    对数据进行鲁棒缩放，使用中位数和四分位距而非均值和方差。
    适合存在异常值的数据。
    
    Args:
        data: 输入特征数据
        
    Returns:
        鲁棒缩放后的特征数据
    """
    scaler = RobustScaler()
    return scaler.fit_transform(data)


def handle_missing_values(
    data: np.ndarray,
    strategy: str = 'mean'
) -> np.ndarray:
    """
    处理数据中的缺失值。
    
    Args:
        data: 输入特征数据
        strategy: 填充策略，可选'mean'、'median'、'most_frequent'或'constant'
        
    Returns:
        填充缺失值后的特征数据
    """
    imputer = SimpleImputer(strategy=strategy)
    return imputer.fit_transform(data)


def remove_low_variance_features(
    data: np.ndarray,
    threshold: float = 0.01
) -> np.ndarray:
    """
    移除方差低于阈值的特征。
    
    Args:
        data: 输入特征数据
        threshold: 方差阈值
        
    Returns:
        移除低方差特征后的数据
    """
    selector = VarianceThreshold(threshold=threshold)
    return selector.fit_transform(data)


def select_k_best_features(
    data: np.ndarray,
    labels: np.ndarray,
    k: int
) -> np.ndarray:
    """
    选择与标签最相关的K个特征。
    
    Args:
        data: 输入特征数据
        labels: 标签数据
        k: 要选择的特征数量
        
    Returns:
        选择后的特征数据
    """
    selector = SelectKBest(f_classif, k=k)
    return selector.fit_transform(data, labels)


def encode_categorical(
    data: pd.DataFrame,
    categorical_columns: List[str]
) -> pd.DataFrame:
    """
    对分类特征进行独热编码。
    
    Args:
        data: 输入数据框
        categorical_columns: 需要编码的分类特征列名列表
        
    Returns:
        编码后的数据框
    """
    return pd.get_dummies(data, columns=categorical_columns, drop_first=True)


def detect_outliers(
    data: np.ndarray,
    method: str = 'iqr',
    threshold: float = 1.5
) -> np.ndarray:
    """
    检测数据中的异常值，返回异常值的布尔掩码。
    
    Args:
        data: 输入特征数据
        method: 检测方法，可选'iqr'（四分位距）或'zscore'（Z分数）
        threshold: 阈值，对于'iqr'是IQR的倍数，对于'zscore'是标准差的倍数
        
    Returns:
        布尔掩码数组，True表示异常值
    """
    if method == 'iqr':
        q1 = np.percentile(data, 25, axis=0)
        q3 = np.percentile(data, 75, axis=0)
        iqr = q3 - q1
        lower_bound = q1 - threshold * iqr
        upper_bound = q3 + threshold * iqr
        return (data < lower_bound) | (data > upper_bound)
    elif method == 'zscore':
        mean = np.mean(data, axis=0)
        std = np.std(data, axis=0)
        z_scores = np.abs((data - mean) / std)
        return z_scores > threshold
    else:
        raise ValueError(f"不支持的检测方法: {method}")


def create_time_features(
    df: pd.DataFrame,
    timestamp_col: str
) -> pd.DataFrame:
    """
    从时间戳列创建时间特征。
    
    Args:
        df: 输入数据框
        timestamp_col: 时间戳列名
        
    Returns:
        添加时间特征后的数据框
    """
    # 确保timestamp_col是datetime类型
    df[timestamp_col] = pd.to_datetime(df[timestamp_col])
    
    # 创建时间特征
    df['hour'] = df[timestamp_col].dt.hour
    df['day'] = df[timestamp_col].dt.day
    df['weekday'] = df[timestamp_col].dt.weekday
    df['month'] = df[timestamp_col].dt.month
    
    # 创建循环特征
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['weekday_sin'] = np.sin(2 * np.pi * df['weekday'] / 7)
    df['weekday_cos'] = np.cos(2 * np.pi * df['weekday'] / 7)
    
    return df 