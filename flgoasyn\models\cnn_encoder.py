"""
CNN编码器模块，用于特征提取。
"""
from typing import Tuple, Optional

import torch
import torch.nn as nn
import torch.nn.functional as F


class CNNEncoder(nn.Module):
    """CNN编码器，用于对输入序列进行特征编码。
    
    对于车联网环境中的时序数据，使用一维卷积提取时序特征并转换为向量表示，
    为后续的Transformer模块提供输入。
    
    Attributes:
        input_size (int): 输入特征维度
        hidden_size (int): 卷积核数量（输出通道数）
        num_layers (int): 卷积层数
        kernel_size (int): 卷积核大小
        dropout (float): Dropout概率
        conv_layers (nn.ModuleList): 卷积层列表
        output_size (int): 输出特征维度
    """
    
    def __init__(
        self, 
        input_size: int, 
        hidden_size: int, 
        num_layers: int = 3, 
        kernel_size: int = 5,
        dropout: float = 0.1
    ) -> None:
        """初始化CNN编码器。
        
        Args:
            input_size: 输入特征维度
            hidden_size: 卷积核数量（输出通道数）
            num_layers: 卷积层数
            kernel_size: 卷积核大小
            dropout: Dropout概率
        """
        super(CNNEncoder, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.kernel_size = kernel_size
        self.dropout = dropout
        
        # 创建多层卷积网络
        self.conv_layers = nn.ModuleList()
        
        # 第一层卷积，从输入特征维度转换到隐藏维度
        self.conv_layers.append(nn.Conv1d(
            in_channels=input_size,
            out_channels=hidden_size,
            kernel_size=kernel_size,
            padding=(kernel_size - 1) // 2  # 保持序列长度不变
        ))
        
        # 添加激活函数和Dropout
        self.activation = nn.ReLU()
        self.dropout_layer = nn.Dropout(dropout)
        
        # 添加后续卷积层
        for _ in range(1, num_layers):
            self.conv_layers.append(nn.Conv1d(
                in_channels=hidden_size,
                out_channels=hidden_size,
                kernel_size=kernel_size,
                padding=(kernel_size - 1) // 2  # 保持序列长度不变
            ))
        
        # 输出特征维度与隐藏层维度相同
        self.output_size = hidden_size
        
        # 批归一化层，提高训练稳定性
        self.batch_norms = nn.ModuleList([
            nn.BatchNorm1d(hidden_size) for _ in range(num_layers)
        ])
    
    def forward(
        self, 
        x: torch.Tensor
    ) -> torch.Tensor:
        """前向传播。
        
        Args:
            x: 输入数据，形状为 [batch_size, seq_len, input_size]
            
        Returns:
            torch.Tensor: 输出特征，形状为 [batch_size, seq_len, output_size]
        """
        batch_size, seq_len, _ = x.size()
        
        # 转置输入以适应卷积操作 [batch_size, input_size, seq_len]
        x = x.transpose(1, 2)
        
        # 应用卷积层
        for i, conv_layer in enumerate(self.conv_layers):
            x = conv_layer(x)
            # BatchNorm1d 在训练模式下需要每个通道至少有两个值；
            # 当 batch_size * seq_len == 1 时跳过批归一化以避免数值错误。
            if self.training and x.size(0) * x.size(2) <= 1:
                # 直接使用输入（或可考虑 LayerNorm/InstanceNorm）
                pass
            else:
                x = self.batch_norms[i](x)
            x = self.activation(x)
            x = self.dropout_layer(x)
        
        # 转置回原始形状 [batch_size, seq_len, hidden_size]
        x = x.transpose(1, 2)
        
        return x 