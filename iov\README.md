# 车联网联邦学习入侵检测框架

本项目实现了一个基于FLGO联邦学习库的车联网入侵检测框架。该框架支持两种联邦学习算法：FedBuff异步聚合和FedAvg同步聚合。框架模拟了车辆和路边单元（边缘节点）之间的交互，使用混合专家模型进行入侵检测。

## 项目结构

```
iov/
├── data/                   # 数据处理模块
│   ├── flgo_data_loader.py # 基于FLGO的数据加载和划分
│   └── generate_sample_data.py # 生成示例数据
├── models/                 # 模型定义模块
│   └── models.py           # 模型架构定义
├── federated/              # 联邦学习模块
│   ├── flgo_fedbuff.py     # 基于FLGO的FedBuff实现
│   └── flgo_fedavg.py      # 基于FLGO的FedAvg实现
├── utils/                  # 工具函数模块
│   └── utils.py            # 辅助函数
├── logs/                   # 日志目录
├── output/                 # 输出目录
├── main.py                 # 主程序入口
├── simulation_flgo.py      # 联邦学习模拟实现（支持FedBuff和FedAvg）
├── run.py                  # 运行脚本
├── requirements.txt        # 项目依赖
└── README.md               # 项目说明
```

## 系统架构

本框架包含两个主要组件：

1. **车辆节点**：每个车辆训练本地入侵检测模型，包括：
   - 本地异质特征提取器（LSTM结构）
   - 门控网络（多层感知机）
   - 预测分类器（全连接网络）

2. **边缘节点（路边单元）**：管理范围内的车辆，并进行全局模型聚合：
   - 使用FedBuff/FedAvg算法进行模型聚合
   - 维护全局同质特征提取器（支持多种架构）
   - 与其他边缘节点通信，进行全局模型聚合

## 模型架构

### 混合专家模型

每个车辆使用混合专家模型进行入侵检测：

1. **本地专家（E1）**：本地异质特征提取器（LSTM）
2. **全局专家（E2）**：全局同质特征提取器（支持两种架构）：
   - **纯Transformer架构**：使用多头注意力机制的Transformer编码器
   - **CNN+Transformer混合架构**：先使用CNN提取多尺度局部特征，再使用Transformer捕获全局依赖关系
3. **门控网络**：决定两个专家的权重
4. **预测分类器**：基于混合特征进行分类

### 全局特征提取器架构

#### 纯Transformer架构
使用标准Transformer编码器，包含：
- 多头自注意力机制
- 前馈神经网络
- 残差连接和层归一化

#### CNN+Transformer混合架构
结合CNN和Transformer的优势：
1. **CNN部分**：
   - 使用多尺度卷积核（3×1、5×1、7×1等）提取局部特征
   - 采用批归一化和残差连接增强特征表示
   
2. **Transformer部分**：
   - 在CNN提取的特征基础上添加位置编码
   - 使用多头自注意力机制捕获全局依赖关系

这种混合架构能够同时利用CNN在提取局部统计特征方面的优势和Transformer在建模长距离依赖方面的能力，特别适合网络流量数据这类时间序列数据。

### 联邦学习流程

1. 每个车辆在本地训练模型
2. 车辆将模型上传到边缘节点
   - FedBuff：异步上传，使用缓冲区进行聚合
   - FedAvg：同步上传，等待所有车辆完成训练
3. 边缘节点聚合接收到的模型
4. 边缘节点之间直接进行全局聚合
   - FedBuff：根据配置的间隔（默认每5轮）进行全局聚合
   - FedAvg：每轮都进行全局聚合
5. 聚合后的全局模型分发给所有边缘节点

## FLGO库集成

本项目使用FLGO（Federated Learning Go）库进行联邦学习，它提供了以下优势：

1. **高效的数据划分**：使用FLGO内置的数据分区工具，如Dirichlet、Shards等，实现更高效的非独立同分布数据划分
2. **优化的算法实现**：使用FLGO的联邦学习算法实现，提供更稳定和高效的模型聚合
3. **多种联邦学习算法支持**：支持FedBuff异步聚合和FedAvg同步聚合

## 安装和使用

### 环境要求

- Python 3.7+
- PyTorch 1.8+
- FLGO
- 其他依赖见`requirements.txt`

### 安装依赖

```bash
pip install -r requirements.txt
```

### 数据准备

可以使用内置的示例数据生成器生成数据：

```bash
python run.py --generate_data
```

或者使用CIC-IDS2017数据集：

```bash
mkdir -p data
# 下载并解压CIC-IDS2017数据集
# 将数据集放在data/CICIDS2017.csv
```

### 运行模拟

使用FedBuff异步聚合 + 纯Transformer架构：
```bash
python run.py --algorithm fedbuff --model_type transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50 --edge_aggregation_interval 10
```

使用FedBuff异步聚合 + CNN+Transformer混合架构：
```bash
python run.py --algorithm fedbuff --model_type cnn_transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50 --edge_aggregation_interval 10
```

使用FedAvg同步聚合 + 纯Transformer架构：
```bash
python run.py --algorithm fedavg --model_type transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50
```

使用FedAvg同步聚合 + CNN+Transformer混合架构：
```bash
python run.py --algorithm fedavg --model_type cnn_transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50
```

### 主要参数

- `--algorithm`：联邦学习算法选择（fedbuff/fedavg）
- `--model_type`：全局特征提取器架构类型（transformer/cnn_transformer）
- `--data_path`：数据集路径
- `--num_vehicles`：车辆数量
- `--num_edges`：边缘服务器数量
- `--buffer_size`：FedBuff缓冲区大小（仅FedBuff算法）
- `--edge_aggregation_interval`：边缘服务器全局聚合间隔（轮次，仅对FedBuff生效，FedAvg每轮都聚合）
- `--movement_ratio`：每次移动的车辆比例
- `--movement_interval`：移动间隔（轮次）
- `--num_rounds`：全局训练轮数
- `--local_epochs`：本地训练轮数
- `--hidden_dim`：隐藏层维度
- `--gpu`：GPU ID，-1表示使用CPU
- `--partition_type`：数据分区类型（dirichlet/shards/iid）

更多参数见`main.py`中的`parse_args`函数。

## 结果分析

训练完成后，结果将保存在`output/`目录下：

- `training_history.png`：训练历史曲线
- `confusion_matrix.png`：混淆矩阵
- `roc_curve.png`：ROC曲线

## 许可证

MIT 