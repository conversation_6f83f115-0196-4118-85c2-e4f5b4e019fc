"""This is a non-official implementation of 'Federated Learning with Buffered Asynchronous Aggregation' (http://arxiv.org/abs/2106.06639). """
from flgo.algorithm.asyncbase import AsyncServer
from flgo.algorithm.fedbase import BasicClient
import flgo.utils.fmodule as fmodule
import copy
import torch
import torch.nn.functional as F
import math

class Server(AsyncServer):
    def initialize(self):
        self.init_algo_para({'buffer_ratio': 0.4, 'eta': 1.0})
        self.buffer = []

    def package_handler(self, received_packages:dict):
        if self.is_package_empty(received_packages): return False
        received_updates = received_packages['model']
        received_client_taus = [u._round for u in received_updates]
        for cdelta, ctau in zip(received_updates, received_client_taus):
            self.buffer.append((cdelta, ctau))
        if len(self.buffer) >= int(self.buffer_ratio * self.num_clients):
            self.buffer= self.buffer[:int(self.buffer_ratio * self.num_clients)]
            # print("len(self.buffer)", int(self.buffer_ratio * self.num_clients))
            # aggregate and clear updates in buffer
            taus_bf = [b[1] for b in self.buffer]
            updates_bf = [b[0] for b in self.buffer]
            weights_bf = [(1 + self.current_round - ctau) ** (-0.5) for ctau in taus_bf]
            model_delta = fmodule._model_average(updates_bf, weights_bf) / len(self.buffer)
            self.model = self.model + self.eta * model_delta
            print('=========================self.buffer', len(self.buffer))
            # clear buffer
            self.buffer = []
            return True
        return False

    def save_checkpoint(self):
        cpt = super().save_checkpoint()
        cpt.update({
            'buffer': self.buffer,
        })
        return cpt

    def load_checkpoint(self, cpt):
        super().load_checkpoint(cpt)
        self.buffer = cpt.get('buffer', set())

class Client(BasicClient):
    def reply(self, svr_pkg):
        model = self.unpack(svr_pkg)
        global_model = copy.deepcopy(model)
        self.train(model)
        update = model-global_model
        update._round = model._round
        cpkg = self.pack(update)
        return cpkg
    

    @fmodule.with_multi_gpus
    def train(self, model):
        r"""
        Standard local training procedure. Train the transmitted model with
        local training dataset.

        Args:
            model (FModule): the global model
        """
        model.train()
        optimizer = self.calculator.get_optimizer(model, lr=self.learning_rate, weight_decay=self.weight_decay,
                                                  momentum=self.momentum)
        
        # train_loader = self.calculator.get_dataloader(
        #     self.train_data, 
        #     batch_size=self.batch_size,
        #     shuffle=False,
        #     num_workers=self.loader_num_workers,
        #     pin_memory=self.option['pin_memory'], 
        #     drop_last=self.option.get('drop_last', False)
        # )
        
        # for _ in range(self.num_epochs):
        #     for batch_data in train_loader:
        #             # 移动数据到设备
        #         x, y = batch_data
        #         x, y = x.to(self.device), y.to(self.device)

        #         model.zero_grad()
        #         outputs = model(x)
        #         loss = F.cross_entropy(outputs, y)
                        
        #             # 反向传播和优化
        #         loss.backward()
        #         optimizer.step()
        # print("self.num_steps", self.num_steps)
        # print('new============', self.num_epochs * math.ceil(self.datavol / self.batch_size),   math.ceil(self.datavol / self.batch_size))
        for iter in range(self.num_epochs):
            # get a batch of data
            batch_data = self.get_batch_data()
            model.zero_grad()
            # calculate the loss of the model on batched dataset through task-specified calculator
            loss = self.calculator.compute_loss(model, batch_data)['loss']
            loss.backward()
            if self.clip_grad>0:torch.nn.utils.clip_grad_norm_(parameters=model.parameters(), max_norm=self.clip_grad)
            optimizer.step()
        return