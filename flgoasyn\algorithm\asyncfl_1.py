"""
车联网场景下结合个性化本地RDP和全局RDP修正的FedAsync异步联邦学习算法实现。
基于异步联邦学习和差分隐私保护，实现安全、高效的入侵检测。

本算法实现了以下关键特性：
1. 客户端侧个性化本地差分隐私SGD
2. 服务器端基于staleness的自适应学习率调整
3. 全局RDP修正和隐私预算管理
4. 通信压缩支持
"""
import copy
import time
import logging
import math
from typing import Dict, List, Any, Optional, Union, Tuple

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import os
# flgo库导入
from flgo.algorithm.asyncbase import AsyncServer
from flgo.algorithm.fedbase import BasicClient
from flgo.utils.fmodule import FModule
from flgo.utils import fmodule
# 添加module2fmodule导入
# from flgo import module2fmodule, convert_model
import collections
# 导入自定义工具
from flgoasyn.utils.compression import CompressionManager

from opacus.accountants.rdp import RDPAccountant
from flgoasyn.utils.rdp_analysis import calibrating_sampled_gaussian
import pickle


# 配置日志
logger = logging.getLogger(__name__)

class Server(AsyncServer):
    """异步联邦学习算法服务器实现。
    
    继承自flgo的AsyncServer，添加了差分隐私保护和自适应学习率调整机制。
    """
    def initialize(self) -> None:
        """初始化AsyncFL服务器参数和状态。
        
        在父类初始化后被调用，设置算法特定的参数和状态。
        """
        # 从self.option获取用户传入的参数
        algo_para = self.option.get('algo_para', [])
        
        # 从main.py传入的客户端采样比例
        self.proportion = self.option.get('proportion', 0.5)
        logger.info(f"服务器: 客户端采样比例 = {self.proportion}")
        
        # 以字典形式定义算法参数，使用用户传入的参数，并设置默认值
        # 确保所有参数都为数值类型
        self.init_algo_para({
            # 缓冲区参数
            'buffer_size': 5 if len(algo_para) <= 0 else int(algo_para[0]), # 缓冲区大小，控制聚合频率
            'sample_rate': 0.5 if len(algo_para) <= 1 else float(algo_para[1]), # 客户端采样率
            
            # 差分隐私参数
            'use_dp': 0 if len(algo_para) <= 2 else int(algo_para[2]), # 是否使用差分隐私: 0=False, 1=True
            'epsilon_total': 100 if len(algo_para) <= 3 else float(algo_para[3]), # 总隐私预算
            'delta': 1e-5 if len(algo_para) <= 4 else float(algo_para[4]), # 隐私参数δ
            'clip_norm': 1.0 if len(algo_para) <= 5 else float(algo_para[5]), # 梯度裁剪范数
            'noise_scale': 1.0 if len(algo_para) <= 6 else float(algo_para[6]), # 噪声尺度  初始最大噪声值
            
            # 全局学习率参数
            'eta': 1.0 if len(algo_para) <= 7 else float(algo_para[7]), # 全局学习率
            
            # staleness感知学习率参数
            'lr_scheme': 2 if len(algo_para) <= 8 else int(algo_para[8]), # 学习率方案(0: 固定, 1: Staleness指数衰减, 2: 指数衰减学习率)
            
            # 通信压缩参数
            'use_compression': 0 if len(algo_para) <= 9 else int(algo_para[9]), # 是否使用通信压缩: 0=False, 1=True
            'compression_method': 0 if len(algo_para) <= 10 else int(algo_para[10]), # 压缩方法: 0='qsgd'
            'compression_bits': 4 if len(algo_para) <= 11 else int(algo_para[11]), # 压缩位数
            
            # 历史模型管理参数
            'history_size': 20 if len(algo_para) <= 12 else int(algo_para[12]), # 最多保存的历史版本数
            'check_interval': 5 if len(algo_para) <= 13 else int(algo_para[13]), # 完整模型检查点间隔
            
            # 敏感度计算方式
            'sensitivity_method': 0 if len(algo_para) <= 14 else int(algo_para[14]), # 0='max', 1='median'
            
            # 预算分配策略参数
            'budget_allocation_strategy': 2 if len(algo_para) <= 15 else int(algo_para[15]), # 预算分配策略: 0='fixed', 1='uniform', 2='rdp_optimal'
            'max_budget_per_round': 50 if len(algo_para) <= 16 else float(algo_para[16]), # 每轮最大预算
            'expected_total_rounds': 100 if len(algo_para) <= 17 else int(algo_para[17]), # 预期总轮数
            'noise_decay_rate': 0.0 if len(algo_para) <= 18 else float(algo_para[18]), # 噪声衰减率
            'hyperbolic_decay_k': 1.0 if len(algo_para) <= 19 else float(algo_para[19]), # 双曲衰减系数
            
            # 个性化差分隐私参数
            'gamma': 0.5 if len(algo_para) <= 20 else float(algo_para[20]), # 噪声自适应因子权重
            'noise_decay_mode': 0 if len(algo_para) <= 21 else int(algo_para[21]), # 噪声衰减模式: 0='exponential', 1='hyperbolic'
            'noise_base_mode': 1 if len(algo_para) <= 22 else int(algo_para[22]), # 噪声基准模式: 0='fixed', 1='dynamic'
            'initial_eplson': 5.0 if len(algo_para) <= 23 else float(algo_para[23]), # 固定模式下的初始最大噪声值
            'max_budget_local_round': 100 if len(algo_para) <= 24 else int(algo_para[24]), # 每轮最大本地预算
            
            # 总客户端数量
            'num_clients': 20 if len(algo_para) <= 25 else int(algo_para[25]), # 总客户端数量
        })
        
        # 使用字符串值映射整数参数
        self._sensitivity_method_map = {0: 'max', 1: 'median'}
        self._noise_decay_mode_map = {0: 'exponential', 1: 'hyperbolic'}
        self._noise_base_mode_map = {0: 'fixed', 1: 'dynamic'}
        
        # 将整数参数映射为字符串值
        self.sensitivity_method = self._sensitivity_method_map.get(self.sensitivity_method, 'max')
        self.noise_decay_mode = self._noise_decay_mode_map.get(self.noise_decay_mode, 'exponential')
        self.noise_base_mode = self._noise_base_mode_map.get(self.noise_base_mode, 'dynamic')
        
        # 将布尔值参数转换为布尔类型
        self.use_dp = bool(self.use_dp)
        self.use_compression = bool(self.use_compression)
        
        # 初始化更新缓冲区
        self.buffer: List[int] = []

        self.accountant = None # RDPAccountant()

        # 初始化当前最大RDP消耗
        self.current_max_rdp_cost = 0.0
        
        # print("self.use_dp", self.use_dp)

        # 初始化差分隐私管理器
        if self.use_dp:
            # 获取客户端总数以便计算采样率
            
            self.dp_config = {
                'epsilon_total': self.epsilon_total,
                'delta': self.delta,
                'clip_norm': self.clip_norm,
                'noise_scale': self.noise_scale,
                'gamma': self.gamma,
                'noise_decay_mode': self.noise_decay_mode,
                'noise_base_mode': self.noise_base_mode,
                'initial_eplson': self.initial_eplson,
                'noise_decay_rate': self.noise_decay_rate,
                'hyperbolic_decay_k': self.hyperbolic_decay_k,
                'batch_size': self.option['batch_size']
            }
            logger.info(f"服务器初始化差分隐私管理器: epsilon={self.epsilon_total}, delta={self.delta}, "
                       f"num_clients={self.num_clients}, noise_scale={self.noise_scale}, gamma={self.gamma}, "
                       f"noise_decay_mode={self.noise_decay_mode}, noise_base_mode={self.noise_base_mode}, "
                       f"initial_eplson={self.initial_eplson}, noise_decay_rate={self.noise_decay_rate}, "
                       f"hyperbolic_decay_k={self.hyperbolic_decay_k}")
        
        # 初始化RDP核算器状态（现在由DPManager管理）
        self.epsilon_cumulative_total = []# 累积的总RDP消耗
        self.sigma_cumulative_total = []  # 累积的客户端sigma值
        self.client_clip_values = []      # 存储客户端上传的clip值
        self.epsilon_max = self.epsilon_total  # 总隐私预算
        self.epsilon_rem = 0. # 剩余隐私预算
        
        # 初始化全局更新方向估计（用于客户端计算梯度方向相似度）
        self.global_update_direction = None
        
        # 记录每个客户端的最后一次更新
        self.client_last_update = {}
        self.batch_size = self.option['batch_size']
        print('self.batch_size',self.batch_size)

        # 添加客户端数据量跟踪
        self.client_data_sizes = {}
        self.min_client_data_size = float('inf')  # 初始化为无穷大，以便找到最小值
        self.estimated_data_size = self.option.get('estimated_data_size', 1000)  # 默认估计值
        
        self.all_noise_multiplier = None
        self.sample_rate = None
        
        # 初始化压缩器（如果启用）
        self.compression_manager = None
        if self.use_compression:
            # 只使用QSGD方法
            if self.compression_method == 0:  # 0代表'qsgd'
                logger.info(f"启用通信压缩，方法=QSGD，位数={self.compression_bits}")
                # 初始化压缩管理器
                self.compression_manager = CompressionManager(
                    compression_method="qsgd",
                    compression_params={"bits": self.compression_bits}
                )
            else:
                logger.warning(f"不支持的压缩方法ID: {self.compression_method}，仅支持QSGD方法(ID=0)")
                self.use_compression = False
                
        # 初始化模型历史记录
        self.model_history = {}  # 存储历史模型或差分，键为轮次
        self.last_checkpoint_round = 0  # 上一个完整模型检查点轮次
        
        logger.info(f"AsyncFL服务器初始化完成, buffer_size={self.buffer_size}, use_dp={self.use_dp}, "
                   f"lr_scheme={self.lr_scheme}, epsilon_total={self.epsilon_total}, "
                   f"eta={self.eta}, budget_allocation_strategy={self.budget_allocation_strategy}")

    def init_compressor(self) -> None:
        """初始化通信压缩器。"""
        # 使用compression.py中的实现，这个方法保留但不再使用
        pass

    def package_handler(self, received_packages: dict) -> bool:
        """处理从客户端接收到的更新包，实现异步聚合和差分隐私保护。
        
        Args:
            received_packages: 从客户端接收到的包，包含模型更新等信息
            
        Returns:
            bool: 表示是否执行了模型更新
        """

        if self.is_package_empty(received_packages):
            return False

        # 获取接收到的模型更新和元数据
        received_updates = received_packages.get('model', [])

        received_client_ids = received_packages.get('__cid', [])
        received_client_taus = [u._round for u in received_updates]
        received_rdp_costs = received_packages.get('rdp_cost', [0.0] * len(received_updates))
        received_local_steps = received_packages.get('local_steps', [1] * len(received_updates))
        received_data_sizes = received_packages.get('data_size', [0] * len(received_updates))

        # 获取客户端上传的clip和sigma值
        received_clip_values = received_packages.get('clip', [self.clip_norm] * len(received_updates))
        received_sigma_values = received_packages.get('sigma', [1.0] * len(received_updates))

        # print('received_rdp_costs================================',received_rdp_costs, received_packages.keys())
        
       

        self.epsilon_cumulative_total = []
        self.sigma_cumulative_total = []
        self.client_clip_values = []
        self.epsilon_rem = 0.
        
        # 添加到缓冲区
        for client_update, client_id, ctau, rdp_cost, local_steps, data_size, clip_value, sigma_value in zip(
                received_updates, received_client_ids, received_client_taus, 
                received_rdp_costs, received_local_steps, received_data_sizes,
                received_clip_values, received_sigma_values):
            # 记录客户端最后一次更新
            self.client_last_update[client_id] = {
                'update': client_update,
                'rdp_cost': rdp_cost,
                'local_steps': local_steps,
                'data_size': data_size,
                'clip': clip_value,
                'sigma': sigma_value
            }
            
            # 添加到缓冲区
            self.buffer.append((client_update, ctau))

            if self.use_dp:  
                self.epsilon_cumulative_total.append(rdp_cost)
                self.sigma_cumulative_total.append(sigma_value)
                self.client_clip_values.append(clip_value)
                logger.warning(f"接收客户端{client_id}的更新, 轮次={self.current_round}, RDP成本={rdp_cost:.4f}, "
                              f"clip值={clip_value:.4f}, sigma值={sigma_value:.4f}")
                


        # 从received_rdp_costs中取出最大值作为本地消耗
        if self.use_dp: 
            if received_rdp_costs and len(received_rdp_costs) > 0:
                max_rdp_cost = max(received_rdp_costs)
                logger.warning(f"服务器: 从客户端接收到的最大RDP消耗为 {max_rdp_cost:.6f}")
                # 更新当前最大RDP消耗
                self.current_max_rdp_cost = max_rdp_cost

        # 缓冲区未满，继续等待
        if len(self.buffer) < self.buffer_size:
            logger.warning(f"缓冲区未满，继续等待，当前缓冲区大小={len(self.buffer)}")
            return False
        
        # self.buffer = self.buffer[:self.buffer_size]
        # self.epsilon_cumulative_total = self.epsilon_cumulative_total[:self.buffer_size]
        # self.sigma_cumulative_total = self.sigma_cumulative_total[:self.buffer_size]
        # self.client_clip_values = self.client_clip_values[:self.buffer_size]

        # 创建当前轮次的聚合集合
        taus_bf = [b[1] for b in self.buffer]
        updates_bf = [b[0] for b in self.buffer]

        # 根据选择的学习率方案计算学习率
        if self.lr_scheme == 0:
            # 固定学习率
            weights_bf = [(1 + 1) ** (-0.5) for _ in taus_bf]
        elif self.lr_scheme == 1:
                # 计算每个客户端的模型差异
                model_diff = [self.model - self.get_client_model(self.current_round - ctau) for ctau in taus_bf]
                # 使用fmodule._model_norm计算模型的范数，而不是直接使用torch.norm
                weights_bf = [(1 + (fmodule._model_norm(cm).item() / (fmodule._model_norm(cdelta).item()))) ** (-0.5) for
                            cm, cdelta in zip(model_diff, updates_bf)]
        elif self.lr_scheme == 2:
                # 指数衰减学习率
                weights_bf = [(1 + self.current_round - ctau) ** (-0.5) for ctau in taus_bf]
        else:
            # 默认使用固定学习率
            weights_bf = [2 ** (-self.current_round - ctau) for _ in taus_bf]
            logger.warning(f"未知的学习率方案: {self.lr_scheme}，使用默认固定学习率方案")

        # 执行聚合操作
        current_model = copy.deepcopy(self.model)
        updates_bf_clipped = updates_bf

        model_delta = fmodule._model_average(updates_bf_clipped, weights_bf) / len(self.buffer)
        agg_mean = self.eta * model_delta

        clip = 1.
        server_noise = None
        if self.use_dp:
            client_sampling_rate = len(self.buffer) / self.num_clients if self.num_clients > 0 else 1.0
            
            # 使用客户端上传的clip值的中位数
            if len(self.client_clip_values) > 0:
                clip = np.average(self.client_clip_values)
                logger.warning(f"使用客户端上传的clip值中位数: {clip:.6f}")
            
            # # 考虑当前最大RDP消耗，调整目标epsilon
            # target_epsilon = self.max_budget_local_round
            # if self.current_max_rdp_cost > 0:
            #     # 使用当前最大RDP消耗作为参考，可以适当调整目标epsilon
            #     target_epsilon = min(self.max_budget_local_round, self.current_max_rdp_cost * 1.2)  # 允许20%的增长空间
            #     logger.warning(f"基于当前最大RDP消耗 {self.current_max_rdp_cost:.6f} 调整目标epsilon为 {target_epsilon:.6f}")
            
            sensitivity = 2 * clip / len(self.buffer)
            noise_multiplier = self.find_optimal_noise_multiplier_a(
                epochs=1,
                target_epsilon=self.max_budget_local_round,
                target_delta=1e-4,
                sample_rate=client_sampling_rate,
                dataset_size=1,
                batch_size=1
            )
            
            # 使用noise_multiplier直接计算标准差，符合标准DP-SGD实现
            sigma = (sensitivity * noise_multiplier) ** 2  # / np.sqrt(len(self.buffer))

            logger.warning(f"使用敏感度参数S={sensitivity:.6f}, 噪声乘数={noise_multiplier:.6f}，噪声标准差={sigma:.6f}")

            extra_noise = np.sqrt(sigma- sum(self.sigma_cumulative_total) / (len(self.buffer)**2))

            if extra_noise > 0. and sum(self.epsilon_cumulative_total) > 0.:
                server_noise = extra_noise
                noised_update = copy.deepcopy(agg_mean)
                server_noise = server_noise * self.eta
                # 为每个参数添加噪声
                for name, param in noised_update.named_parameters():
                    noise = torch.randn_like(param.data) * server_noise
                    param.data.add_(noise)
                
                # 使用添加了噪声的更新
                agg_mean = noised_update
                logger.warning(f"服务器添加噪声: 噪声标准差={server_noise:.6f}")
        
        # 保存当前模型的副本，用于历史记录
        prev_model = copy.deepcopy(self.model)
        
        # 更新全局模型
        self.model = current_model + agg_mean
        
        # 保留缓冲区中未处理的客户端更新，以便下一轮聚合
        self.buffer = []
        
        # 更新历史模型记录
        self._update_model_history(prev_model)
        
        # 更新全局更新方向估计（用于下一轮客户端计算梯度方向相似度）
        self.global_update_direction = self.get_client_model(self.current_round)  
        
        processed_count = len(self.buffer)
        logger.info(
            f"模型更新完成，当前轮次={self.current_round}, 参与客户端数量={processed_count}")
        
        return True
    
    def find_optimal_noise_multiplier_a(
        self,
        epochs: int,
        # max_grad_norm: float,
        target_epsilon: float,
        target_delta: float,
        sample_rate: float,
        dataset_size: int,
        batch_size: int,
        alphas: list = None,
        sigma_min: float = 0.01,
        sigma_max: float = 10.0,
        tolerance: float = 0.01,
        max_iter: int = 100
    ) -> float:
        """
        计算达到目标隐私预算(ε,δ)所需的最小噪声乘数
        
        参数:
            epochs: 训练轮次
            # max_grad_norm: 梯度裁剪阈值
            target_epsilon: 目标隐私预算ε
            target_delta: 目标δ值
            sample_rate: 采样率 (batch_size / dataset_size)
            dataset_size: 数据集大小
            batch_size: 批次大小
            alphas: RDP阶数列表，默认[1.5, 2, 4, 8, 16, 32, 64]
            sigma_min: 噪声乘数搜索下限
            sigma_max: 噪声乘数搜索上限
            tolerance: 精度容差
            max_iter: 最大迭代次数
            
        返回:
            满足隐私预算的最小噪声乘数
        """
        # 初始化RDP会计器
        accountant = RDPAccountant()
        
        # 设置RDP阶数(如果未提供)
        if alphas is None:
            alphas = [1.5, 2, 4, 8, 16, 32, 64]
        
        # 计算总步数
        steps_per_epoch = dataset_size // batch_size
        total_steps = int(epochs * steps_per_epoch)
        
        # 二分搜索寻找最优噪声乘数
        low = sigma_min
        high = sigma_max
        best_sigma = None
        
        from tqdm import tqdm
        # 使用tqdm显示进度
        with tqdm(total=max_iter, desc="寻找最优噪声乘数") as pbar:
            for i in range(max_iter):
                # 计算中点
                sigma = (low + high) / 2.0
                
                # 创建新的会计器
                accountant = RDPAccountant()
                
                # 模拟训练过程
                for step in range(total_steps):
                    accountant.step(
                        noise_multiplier=sigma,
                        sample_rate=sample_rate
                    )
                
                # 计算当前隐私消耗
                epsilon,_ = accountant.get_privacy_spent(delta=target_delta)
                
                # 检查是否满足目标
                if epsilon <= target_epsilon:
                    best_sigma = sigma
                    # 噪声过大，尝试缩小
                    high = sigma - tolerance
                else:
                    # 噪声不足，尝试增大
                    low = sigma + tolerance
                
                # 检查收敛
                if high - low < tolerance:
                    break
                    
                # 更新进度条
                pbar.set_postfix({
                    'epsilon': epsilon, 
                    'sigma': sigma,
                    'range': f"[{low:.4f}, {high:.4f}]"
                })
                pbar.update(1)
        
        # 如果没有找到满足条件的噪声值，选择上限值
        if best_sigma is None:
            best_sigma = sigma_max
            print(f"警告: 在范围内未找到满足ε<={target_epsilon}的噪声值。使用最大值: {best_sigma}")
        
        return best_sigma

    def find_self_consistent_alpha(
        self,
        sample_rate: float,
        eps_budget: float,
        delta: float,
        alpha_min: float = 1.1,
        alpha_max: float = 100.0,
        num_candidates: int = 100
    ):
        """
        在 [alpha_min, alpha_max] 范围内以线性扫描方式，
        找到一个 alpha，使得 best_alpha ≈ alpha。
        """
        best_pair = None  # (alpha_input, best_alpha, |diff|)
        min_diff = float("inf")

        # 构造候选 alpha 列表  list(np.linspace(1.1, 18, 20)) + list(np.linspace(18, 100, 10))  # [1 + x / 10.0 for x in range(1, 100)] + list(range(12, 64))#
        alphas = np.linspace(alpha_min, alpha_max, num_candidates)
        for alpha in alphas:
            # 由 eps_budget 反解 noise_multiplier
            # noise_multiplier = sqrt(alpha / (2 * eps_budget))
            sigma = math.sqrt(alpha / (2 * eps_budget))

            acct = RDPAccountant()
            acct.step(noise_multiplier=sigma, sample_rate=sample_rate)
            eps, best_alpha = acct.get_privacy_spent(delta=delta)

            diff = abs(best_alpha - alpha)
            if diff < min_diff:
                min_diff = diff
                best_pair = (alpha, best_alpha, diff, eps, sigma)

        return best_pair[1]


    def _update_model_history(self, prev_model) -> None:
        """更新模型历史记录。
        
        Args:
            prev_model: 更新前的模型
        """
        current_round = self.current_round
        
        # 保存完整模型
        self.model_history[current_round] = copy.deepcopy(self.model)
        
        # 周期性保存完整检查点
        if current_round - self.last_checkpoint_round >= self.check_interval:
            self.model_history[f"checkpoint_{current_round}"] = copy.deepcopy(self.model)
            self.last_checkpoint_round = current_round
        
        # 清理旧的历史记录
        self._cleanup_model_history()
    
    def _cleanup_model_history(self) -> None:
        """清理过时的模型历史记录，以控制内存使用。"""
        
        # 获取所有轮次（不包括检查点）
        rounds = [r for r in self.model_history.keys() if isinstance(r, int)]
        
        # 如果历史记录数量超过限制，删除最早的记录
        if len(rounds) > self.history_size:
            # 排序找出最早的记录
            rounds.sort()
            # 需要删除的轮次
            to_remove = rounds[:len(rounds) - self.history_size]
            
            # 删除这些轮次的记录
            for r in to_remove:
                if r in self.model_history:
                    del self.model_history[r]
                    logger.debug(f"从历史记录中删除轮次{r}的模型")

    def get_client_model(self, round_number: int) -> FModule:
        """获取指定轮次的全局模型。
        
        直接返回历史记录中指定轮次的模型，因为self.model本身就是全模型。
        
        Args:
            round_number: 请求的轮次号
            
        Returns:
            指定轮次的全局模型
        """
        # 如果请求当前轮次的模型，直接返回当前模型
        if round_number >= self.current_round:
            return self.model
            
        # 如果请求的轮次在历史记录中，直接返回
        if round_number in self.model_history:
            return self.model_history[round_number]
        
        # 如果找不到精确匹配，查找最接近的轮次
        available_rounds = sorted([r for r in self.model_history.keys() if isinstance(r, int)])
        
        if not available_rounds:
            # 没有历史记录，返回当前模型
            return self.model
        
        # 找到不大于请求轮次的最大轮次
        closest_round = None
        for r in available_rounds:
            if r <= round_number:
                closest_round = r
            elif r > round_number:
                break
                
        if closest_round is None:
            # 所有历史轮次都大于请求的轮次，返回当前模型
            return self.model
            
        # 返回最接近的轮次模型
        return self.model_history[closest_round]
    

  

    def save_checkpoint(self) -> dict:
        """保存检查点，包含算法特定的状态。
        
        Returns:
            dict: 包含算法状态的检查点字典
        """
        cpt = super().save_checkpoint()
        cpt.update({
            'buffer': self.buffer,
            'epsilon_cumulative_total': self.epsilon_cumulative_total,
            'epsilon_rem': self.epsilon_rem,
            'client_last_update': self.client_last_update,
            'global_update_direction': self.global_update_direction,
            'compression_manager': self.compression_manager if hasattr(self, 'compression_manager') else None,
            'model_history': self.model_history,
            'last_checkpoint_round': self.last_checkpoint_round,
            'current_max_rdp_cost': self.current_max_rdp_cost
        })
        return cpt

    def load_checkpoint(self, cpt: dict) -> None:
        """加载检查点，恢复算法特定的状态。
        
        Args:
            cpt: 包含算法状态的检查点字典
        """
        super().load_checkpoint(cpt)
        self.buffer = cpt.get('buffer', [])
        self.epsilon_cumulative_total = cpt.get('epsilon_cumulative_total', 0.0)
        self.epsilon_rem = cpt.get('epsilon_rem', self.epsilon_total)
        self.client_last_update = cpt.get('client_last_update', {})
        self.global_update_direction = cpt.get('global_update_direction', None)
        self.compression_manager = cpt.get('compression_manager', None)
        self.model_history = cpt.get('model_history', {})
        self.last_checkpoint_round = cpt.get('last_checkpoint_round', 0)
        self.current_max_rdp_cost = cpt.get('current_max_rdp_cost', 0.0)
        

    def pack(self, client_id, mtype=0, *args, **kwargs):
        """打包消息，应用压缩（如果启用）。
        
        Args:
            client_id: 客户端ID
            mtype: 消息类型
            *args: 额外的位置参数
            **kwargs: 额外的关键字参数
            
        Returns:
            dict: 打包后的消息
        """
        # 应用压缩（如果启用）
        if self.use_compression and self.compression_manager is not None:
            # 压缩模型
            model_dict = {"model": self.model}
            compressed_dict = self.compression_manager.compress(model_dict)
            # 记录压缩统计信息
            stats = self.compression_manager.get_compression_statistics()
            logger.warning(f"压缩统计: 原始大小={stats['original_size_mb']:.4f}MB, "
                       f"压缩后={stats['compressed_size_mb']:.4f}MB, "
                       f"压缩比={stats['compression_ratio']:.2f}x")
            # 更新包中的模型
            self.model = compressed_dict["model"]

        # print('这是一个测试')

        return {
            'model': copy.deepcopy(self.model),
            'global_update_direction': self.global_update_direction
        }
    
    
    

    def unpack(self, packages: Any) -> Any:
        """解包消息，应用解压缩（如果启用）。
        
        Args:
            packages: 要解包的消息
            
        Returns:
            解包后的消息
        """
        if len(packages) == 0: return collections.defaultdict(list)
        res = {pname: [] for pname in packages[0]}
        for cpkg in packages:
            # 应用解压缩（如果启用）
            if self.use_compression and self.compression_manager is not None:
                # 检查并解压模型数据
                if 'model' in cpkg or isinstance(cpkg['model'], dict):
                    model_dict = {"model": cpkg['model']}
                    decompressed_dict = self.compression_manager.decompress(model_dict)
                    cpkg['model'] = decompressed_dict["model"]
                    
            # 收集解压后的数据
            for pname, pval in cpkg.items():
                res[pname].append(pval)

        return res

    def _save_checkpoint(self):
        """保存检查点，使用更短的文件名格式。
        
        覆盖父类的方法，解决文件名过长的问题。
        """
        checkpoint = self.option.get('save_checkpoint', '')
        if checkpoint != '':
            cpt = self.save_checkpoint()
            
            # 创建检查点目录
            checkpoint_dir = os.path.join(self.option['task'], 'checkpoint')
            if not os.path.exists(checkpoint_dir): 
                os.makedirs(checkpoint_dir, exist_ok=True)
            
            # 使用更短的文件名格式
            algorithm = self.option.get('algorithm', 'asyncfl')
            timestamp = int(time.time())
            short_cpt_path = os.path.join(
                checkpoint_dir, 
                f"{algorithm}_r{self.current_round}_t{timestamp}.pt"
            )
            
            # 保存到简短路径
            try:
                torch.save(cpt, short_cpt_path)
                logger.warning(f"检查点已保存到: {short_cpt_path}")
            except Exception as e:
                logger.error(f"保存检查点失败: {str(e)}")


class Client(BasicClient):
    """异步联邦学习算法客户端实现。
    
    继承自flgo的BasicClient，实现了差分隐私SGD功能。
    """
    
    def initialize(self) -> None:
        """初始化客户端参数和状态。"""
        super().initialize()
        
        # 初始化name属性（可以被外部覆盖）
        if not hasattr(self, 'name'):
            self.name = f"client_{self.id}" if hasattr(self, "id") else "unknown_client"
            
        # 初始化datavol属性（可以被外部覆盖）
        if not hasattr(self, 'datavol'):
            self.datavol = len(self.train_data) if hasattr(self, "train_data") and self.train_data else 0
            
        # 添加最小数据量跟踪
        self.min_data_size = len(self.train_data) if hasattr(self, "train_data") and self.train_data else 0
        logger.warning(f"客户端 {self.name}: 数据量 = {self.min_data_size}")
            
        # 初始化model_size属性
        self.model_size = 0  # 默认值为0

        # 初始化num_steps属性（用于控制本地训练步数）
        self.num_steps = self.option.get('num_steps', -1)
        self.num_rounds = self.option.get('num_rounds', 10)
        
        # 从main.py传入的客户端采样比例
        self.proportion = self.option.get('proportion', 0.5)
        # logger.warning(f"客户端 {self.name}: 采样比例 = {self.proportion}")
        
        # 从self.option获取用户传入的参数
        algo_para = self.option.get('algo_para', [])
        
        # 直接为参数赋值，不使用init_algo_para方法
        # 差分隐私参数
        self.buffer = algo_para[0] if len(algo_para) > 0 else 5
        self.use_dp = bool(0 if len(algo_para) <= 2 else int(algo_para[2])) # 是否使用差分隐私: 0=False, 1=True
        self.epsilon_total = 5.0 if len(algo_para) <= 3 else float(algo_para[3]) # 总隐私预算
        self.delta = 1e-5 if len(algo_para) <= 4 else float(algo_para[4]) # 隐私参数δ
        self.clip_norm = 1.0 if len(algo_para) <= 5 else float(algo_para[5]) # 梯度裁剪范数
        self.noise_scale = 1.0 if len(algo_para) <= 6 else float(algo_para[6]) # 噪声尺度
        
        # 通信压缩参数
        self.use_compression = bool(0 if len(algo_para) <= 9 else int(algo_para[9])) # 是否使用通信压缩: 0=False, 1=True
        self.compression_method = 0 if len(algo_para) <= 10 else int(algo_para[10]) # 压缩方法: 0='qsgd'
        self.compression_bits = 4 if len(algo_para) <= 11 else int(algo_para[11]) # 压缩位数
        
        # 敏感度计算方式
        self.sensitivity_method = 0 if len(algo_para) <= 14 else int(algo_para[14]) # 0='max', 1='median'
        
        # 噪声衰减率参数
        self.noise_decay_rate = 0.01 if len(algo_para) <= 18 else float(algo_para[18]) # 噪声衰减率
        self.hyperbolic_decay_k = 0.05 if len(algo_para) <= 19 else float(algo_para[19]) # 双曲衰减系数
        
        # 个性化差分隐私参数
        self.gamma = 0.5 if len(algo_para) <= 20 else float(algo_para[20]) # 噪声自适应因子权重
        self.noise_decay_mode = 0 if len(algo_para) <= 21 else int(algo_para[21]) # 噪声衰减模式: 0='exponential', 1='hyperbolic'
        self.noise_base_mode = 1 if len(algo_para) <= 22 else int(algo_para[22]) # 噪声基准模式: 0='fixed', 1='dynamic'
        self.initial_eplson = 5.0 if len(algo_para) <= 23 else float(algo_para[23]) # 固定模式下的初始最大噪声值
        self.max_budget_local_round = 100 if len(algo_para) <= 24 else int(algo_para[24]) # 每轮最大本地预算
        
        # 总客户端数量，用于计算采样率
        self.num_clients = 20 if len(algo_para) <= 25 else int(algo_para[25]) # 总客户端数量
        
        # 使用字符串值映射整数参数
        self._sensitivity_method_map = {0: 'max', 1: 'median'}
        self._noise_decay_mode_map = {0: 'exponential', 1: 'hyperbolic'}
        self._noise_base_mode_map = {0: 'fixed', 1: 'dynamic'}
        
        # 将整数参数映射为字符串值
        self.sensitivity_method = self._sensitivity_method_map.get(self.sensitivity_method, 'max')
        self.noise_decay_mode = self._noise_decay_mode_map.get(self.noise_decay_mode, 'exponential')
        self.noise_base_mode = self._noise_base_mode_map.get(self.noise_base_mode, 'dynamic')
        
        # 将布尔值参数转换为布尔类型
        self.use_dp = bool(self.use_dp)
        self.use_compression = bool(self.use_compression)

        self.rdp_cost = 0.
        self.current_steps = 0

        self.eplson = None
        self.sample_rate = self.batch_size / len(self.train_data) if hasattr(self, "train_data") and self.train_data else 0.1

        self.sigma = 1
        
        # 初始化loss_ref为None，将在第一次训练时计算
        self.loss_ref = None
        self.is_first_train = True
        self.last_loss = None
        
        # 初始化差分隐私管理器
        if self.use_dp:
            self.dp_config = {
                'epsilon_total': self.epsilon_total,
                'delta': self.delta,
                'clip_norm': self.clip_norm,
                'noise_scale': self.noise_scale,
                'gamma': self.gamma,
                'noise_decay_mode': self.noise_decay_mode,
                'noise_base_mode': self.noise_base_mode,
                'initial_eplson': self.initial_eplson,
                'noise_decay_rate': self.noise_decay_rate,
                'hyperbolic_decay_k': self.hyperbolic_decay_k,
                'batch_size': self.batch_size
            }
            logger.warning(f"客户端 {self.name} 初始化差分隐私管理器: epsilon={self.epsilon_total}, "
                       f"delta={self.delta}, noise_scale={self.noise_scale}, gamma={self.gamma}, "
                       f"noise_base_mode={self.noise_base_mode}, initial_eplson={self.initial_eplson}, "
                       f"noise_decay_mode={self.noise_decay_mode}, noise_decay_rate={self.noise_decay_rate}, "
                       f"hyperbolic_decay_k={self.hyperbolic_decay_k}, num_clients={self.num_clients}")
 
        
        # 初始化压缩器（如果启用）
        self.compression_manager = None
        if self.use_compression:
            # 只使用QSGD方法
            if self.compression_method == 0:  # 0代表'qsgd'
                logger.warning(f"客户端 {self.name}: 启用通信压缩，方法=QSGD，位数={self.compression_bits}")
                # 初始化压缩管理器
                self.compression_manager = CompressionManager(
                    compression_method="qsgd",
                    compression_params={"bits": self.compression_bits}
                )
            else:
                logger.warning(f"客户端 {self.name}: 不支持的压缩方法ID: {self.compression_method}，仅支持QSGD方法(ID=0)")
                self.use_compression = False
                
        # 管理异步状态
        self.round = 0 # 记录客户端当前训练轮次
        self.data_loader = None  # BasicClient已有此属性
        self.expect_noise_multiplier = 1.0
        self.loss_ref = None
        self.model_history = {}
        self.last_checkpoint_round = 0  # 上一个完整模型检查点轮次

        # 设置默认的clip值
        self.clip = self.clip_norm

    def _load_grad_norms(self) -> list:
        """加载历史梯度范数。
            
        Returns:
            list: 历史梯度范数列表
        """
        if os.path.exists(self.grad_norms_file):
            try:
                with open(self.grad_norms_file, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.error(f"客户端 {self.name}: 加载梯度范数文件失败: {str(e)}")
        return []
    
    def _save_grad_norms(self, grad_norms: list) -> None:
        """保存梯度范数到文件。
        
        Args:
            grad_norms: 当前批次的梯度范数列表
        """
        # 添加新的梯度范数到历史记录
        self.all_grad_norms.extend(grad_norms)
        
        # 保存到文件
        try:
            with open(self.grad_norms_file, 'wb') as f:
                pickle.dump(self.all_grad_norms, f)
            logger.debug(f"客户端 {self.name}: 保存梯度范数到文件，共{len(self.all_grad_norms)}个")
        except Exception as e:
            logger.error(f"客户端 {self.name}: 保存梯度范数文件失败: {str(e)}")
    
    def _load_global_median_clip(self) -> float:
        """加载全局中位数clip值。
        
        Returns:
            float: 全局中位数clip值，如果文件不存在则返回None
        """
        if os.path.exists(self.global_median_file):
            try:
                with open(self.global_median_file, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.error(f"客户端 {self.name}: 加载全局中位数clip文件失败: {str(e)}")
        return None
    
    def _update_global_median_clip(self) -> float:
        """更新全局中位数clip值。
        
        计算所有客户端的梯度范数中位数，并保存到全局文件中。
        
        Returns:
            float: 计算得到的全局中位数，如果没有足够数据则返回None
        """
        # 收集所有客户端的梯度范数文件
        all_norms = []
        for filename in os.listdir(self.grad_norms_dir):
            if filename.endswith('_grad_norms.pkl'):
                try:
                    with open(os.path.join(self.grad_norms_dir, filename), 'rb') as f:
                        client_norms = pickle.load(f)
                        all_norms.extend(client_norms)
                except Exception as e:
                    logger.error(f"客户端 {self.name}: 读取梯度范数文件 {filename} 失败: {str(e)}")
        
        # 如果有足够的数据，计算中位数
        if len(all_norms) > 0:
            global_median = float(np.median(all_norms))
            
            # 保存全局中位数
            try:
                with open(self.global_median_file, 'wb') as f:
                    pickle.dump(global_median, f)
                logger.warning(f"客户端 {self.name}: 更新全局中位数clip值: {global_median:.6f}，基于{len(all_norms)}个样本")
                return global_median
            except Exception as e:
                logger.error(f"客户端 {self.name}: 保存全局中位数clip文件失败: {str(e)}")
        
        return None
    
    def _get_all_clients_median(self) -> float:
        """获取所有客户端和所有轮次的梯度范数中位数。
        
        这个方法会读取所有客户端的梯度范数文件，计算全局中位数，
        并将结果存储在一个单独的文件中供所有客户端使用。
        
        Returns:
            float: 所有客户端和所有轮次的梯度范数中位数
        """
        # 全局中位数文件路径
        global_stats_file = os.path.join(self.grad_norms_dir, 'global_all_clients_median.pkl')
        
        # 尝试读取已有的全局统计信息
        if os.path.exists(global_stats_file):
            try:
                with open(global_stats_file, 'rb') as f:
                    global_stats = pickle.load(f)
                    logger.info(f"客户端 {self.name}: 加载全局统计信息，中位数={global_stats['median']:.6f}，基于{global_stats['count']}个样本")
                    return global_stats['median']
            except Exception as e:
                logger.error(f"客户端 {self.name}: 读取全局统计文件失败: {str(e)}")
        
        # 如果文件不存在或读取失败，重新计算
        all_norms = []
        client_counts = {}
        
        # 收集所有客户端的梯度范数
        for filename in os.listdir(self.grad_norms_dir):
            if filename.endswith('_grad_norms.pkl'):
                client_id = filename.split('_')[1]  # 从文件名提取客户端ID
                try:
                    with open(os.path.join(self.grad_norms_dir, filename), 'rb') as f:
                        client_norms = pickle.load(f)
                        all_norms.extend(client_norms)
                        client_counts[client_id] = len(client_norms)
                except Exception as e:
                    logger.error(f"客户端 {self.name}: 读取梯度范数文件 {filename} 失败: {str(e)}")
        
        # 如果有足够的数据，计算中位数
        if len(all_norms) > 0:
            global_median = float(np.median(all_norms))
            
            # 保存全局统计信息
            global_stats = {
                'median': global_median,
                'count': len(all_norms),
                'client_counts': client_counts,
                'timestamp': time.time()
            }
            
            try:
                with open(global_stats_file, 'wb') as f:
                    pickle.dump(global_stats, f)
                logger.warning(f"客户端 {self.name}: 更新全局统计信息，中位数={global_median:.6f}，基于{len(all_norms)}个样本")
                return global_median
            except Exception as e:
                logger.error(f"客户端 {self.name}: 保存全局统计文件失败: {str(e)}")
                return global_median  # 即使保存失败也返回计算结果
        
        # 如果没有数据，返回默认值
        return self.clip_norm
    
    @fmodule.with_multi_gpus
    def train(self, model) -> Any:
        """使用标准SGD训练模型，不进行梯度裁剪和噪声添加。
        
        Args:
            model: 待训练的模型
            
        Returns:
            Any: 训练后的模型
        """
        
        model.train()

        optimizer = self.calculator.get_optimizer(
                model, 
                lr=self.learning_rate, 
                weight_decay=self.weight_decay,
                momentum=self.momentum
                )   
        
        # 初始化累积损失和计数器，用于计算平均损失
        total_loss = 0.0
        batch_count = 0
        
        # 无论是否使用DP，都使用相同的训练流程，裁剪和加噪将在reply中处理
        for step in range(self.num_steps): #num_steps
            # 获取一批数据
            batch_data = self.get_batch_data() 
            
            # 前向传播
            optimizer.zero_grad()
            
            loss = self.calculator.compute_loss(model, batch_data)['loss']
            
            # 累积损失
            total_loss += loss.mean().item()
            batch_count += 1

            # 反向传播和优化
            loss.backward()
            
            # 直接更新参数，不裁剪也不加噪
            optimizer.step()
                        
            # 更新本地步数
            self.current_steps += 1

            # 删除冗余代码，因为我们已经在epoch结束后计算了平均损失并更新了self.last_loss
            # if step == self.num_steps - 1:
            #     self.last_loss = loss.mean().item()
            #     print(model._round - 1, 'self.last_loss',self.last_loss)
            
            # 保存最后一步的损失值，用于reply中计算epsilon

            # if step == 0:
            #     self.loss_ref = loss.mean().item()
            
        
        # 计算本次训练的平均损失
        if batch_count > 0:
            avg_loss = total_loss / batch_count
        else:
            avg_loss = 0.1  # 默认值，避免除零错误
        
        # 如果是第一次训练，将平均损失作为参考损失
        if self.is_first_train:
            self.loss_ref = avg_loss #max(avg_loss, 0.0001)  # 确保不为0
            self.is_first_train = False
            logger.warning(f"客户端 {self.name}: 初始化参考损失值 loss_ref = {self.loss_ref:.6f}")
        
        # 更新最后一次的平均损失
        self.last_loss = avg_loss
        print(f"客户端 {self.name}: 轮次 {model._round - 1 if hasattr(model, '_round') else self.round}, "
              f"平均损失 = {self.last_loss:.6f}, 参考损失 = {self.loss_ref:.6f}")
        
        self.round += 1
            
        return model

    def reply(self, svr_pkg: Any) -> dict:
        """回复服务器，包含额外的元数据。
        
        Args:
            svr_pkg: 服务器发送的包
            
        Returns:
            dict: 客户端回复的包
        """
        # 解包获取模型和全局更新方向
        unpacked = self.unpack(svr_pkg)
        # print('unpacked', unpacked ,type(unpacked))
        model = unpacked['model']
        
        # 从服务器包中提取全局更新方向(如果存在)
        # global_update_direction = None
        
        if 'global_update_direction' in unpacked:
            global_update_direction = unpacked['global_update_direction']
            logger.info(f"客户端 {self.name}: 收到上一个全局model")
        
            
        # 保存全局模型副本
        global_model = copy.deepcopy(model)
        
        # 训练模型
        trained_model = self.train(model)

        
        # 计算模型更新
        update = trained_model - global_model

        # 计算更新的范数，使用DP计算并存储
        with torch.no_grad():
            update_norm = fmodule._model_norm(update)


            if not self.use_dp:

                update_norm_value = update_norm.item() if isinstance(update_norm, torch.Tensor) else update_norm
                
                # logger.warning(f"客户端 {self.name}: 原始模型更新范数={update_norm_value:.6f}")
                
                # 创建模型更新范数存储目录
                update_norms_dir = os.path.join(self.option['task'], 'update_norms')
                os.makedirs(update_norms_dir, exist_ok=True)
                
                # 模型更新范数文件路径
                update_norms_file = os.path.join(update_norms_dir, f'client_{self.id}_update_norms.pkl')
                global_median_file = os.path.join(update_norms_dir, 'global_median_clip.pkl')
                
                # 加载历史模型更新范数
                update_norms = []
                if os.path.exists(update_norms_file):
                    try:
                        with open(update_norms_file, 'rb') as f:
                            update_norms = pickle.load(f)
                    except Exception as e:
                        logger.error(f"客户端 {self.name}: 加载模型更新范数文件失败: {str(e)}")
                
                # 添加新的模型更新范数
                update_norms.append(update_norm_value)
                
                # 保存到文件
                try:
                    with open(update_norms_file, 'wb') as f:
                        pickle.dump(update_norms, f)
                    logger.debug(f"客户端 {self.name}: 保存模型更新范数到文件，共{len(update_norms)}个")
                except Exception as e:
                    logger.error(f"客户端 {self.name}: 保存模型更新范数文件失败: {str(e)}")
                
                # 每10轮更新一次全局中位数
                if self.round % 1 == 0:
                    # 收集所有客户端的模型更新范数
                    all_norms = []
                    for filename in os.listdir(update_norms_dir):
                        if filename.endswith('_update_norms.pkl'):
                            try:
                                with open(os.path.join(update_norms_dir, filename), 'rb') as f:
                                    client_norms = pickle.load(f)
                                    all_norms.extend(client_norms)
                            except Exception as e:
                                logger.error(f"客户端 {self.name}: 读取模型更新范数文件 {filename} 失败: {str(e)}")
                    
                    # 如果有足够的数据，计算中位数
                    if len(all_norms) > 0:
                        global_median = float(np.average(all_norms))
                        
                        # 保存全局中位数
                        try:
                            with open(global_median_file, 'wb') as f:
                                pickle.dump(global_median, f)
                            logger.warning(f"客户端 {self.name}: 更新全局中位数clip值: {global_median:.6f}，基于{len(all_norms)}个样本")
                        except Exception as e:
                            logger.error(f"客户端 {self.name}: 保存全局中位数clip文件失败: {str(e)}")

        # 如果启用差分隐私，在此处对模型更新添加噪声
        if self.use_dp:
            # 尝试加载全局中位数作为clip值
            try:
                with open(global_median_file, 'rb') as f:
                    self.clip = pickle.load(f)/2
                logger.warning(f"客户端 {self.name}: 加载全局模型更新范数中位数作为clip值: {self.clip:.6f}")
            except Exception as e:
                logger.error(f"客户端 {self.name}: 加载全局中位数clip文件失败: {str(e)}")
                # 如果加载失败，使用默认值
                self.clip = self.clip_norm
                logger.warning(f"客户端 {self.name}: 使用默认clip值: {self.clip:.6f}")
                
            # 计算梯度相似度
            grad_similarity = None
            if 'global_update_direction' in unpacked:
                global_update_direction = unpacked['global_update_direction']
            
              #  if local_update_direction is not None:
                   # grad_similarity = fmodule._model_cossim(local_update_direction, global_update_direction)
              #      logger.warning(f"梯度相似度: {grad_similarity:.4f}")
            print(f"客户端 {self.name}: 轮次 {model._round - 1 if hasattr(model, '_round') else self.round}, "
                  f"使用平均损失 = {self.last_loss:.6f}, 参考损失 = {self.loss_ref:.6f}")
            # 计算epsilon和sigma
            self.eplson = self.adaptive_scheduler(
                round=(model._round - 1) if hasattr(model, '_round') else self.round,
                loss_value=getattr(self, 'last_loss', None), #self.last_loss
                grad_similarity=grad_similarity
            )

            if self.eplson > self.max_budget_per_round:
                self.eplson = self.max_budget_per_round
                logger.warning(f"客户端 {self.name}: 已经超过本地最大阈值隐私预算: {self.max_budget_local_round:.6f}")
                
            # 使用服务器传来的总客户端数量计算采样率
            if hasattr(self, 'num_clients') and self.num_clients > 0:
                client_sampling_rate = self.proportion#self.buffer / self.num_clients  # 每个客户端被选中的概率
            # else:
            #     # 如果没有num_clients参数，使用批次大小和数据集大小计算采样率
            #     client_sampling_rate = self.batch_size / len(self.train_data) if hasattr(self, 'train_data') and len(self.train_data) > 0 else 0.1
            
            logger.warning(f"客户端 {self.name}: 使用采样率={client_sampling_rate:.6f}计算噪声乘数")
            
            self.sigma = self.find_optimal_noise_multiplier(
                epochs=1,
                target_epsilon=self.eplson,
                target_delta=1e-4,
                sample_rate=client_sampling_rate,
                dataset_size=1,
                batch_size=1
            )

            self.rdp_cost = self.eplson
            logger.warning(f"客户端{self.name}计算噪声乘数={self.sigma:.4f}, 预算={self.eplson:.4f}")
            
            # 裁剪更新
            clip_factor = 1.0
            if update_norm_value > self.clip:
                clip_factor = self.clip / update_norm_value
                update = fmodule._model_scale(update, clip_factor)
                logger.warning(f"客户端 {self.name}: 裁剪模型更新，裁剪因子={clip_factor:.6f}")

            # # 重新计算裁剪后的范数
            # clipped_norm = fmodule._model_norm(update).item()
            # logger.warning(f"客户端 {self.name}: 裁剪后模型更新范数={clipped_norm:.6f}")
            
            sensi = 2 * self.clip   / len(self.train_data)
            # 添加高斯噪声
            noise_std = self.sigma * sensi #这里应该是敏感度
            noised_update = copy.deepcopy(update)
            
            # 为每个参数添加噪声
            for name, param in noised_update.named_parameters():
                noise = torch.randn_like(param.data) * noise_std
                param.data.add_(noise)
            
            logger.warning(f"客户端 {self.name}: 对模型更新添加噪声，噪声标准差={noise_std:.6f}")
            
            # 使用添加了噪声的更新
            update = noised_update

        # 计算最终更新的范数
        with torch.no_grad():
            update_norm = fmodule._model_norm(update)    

        # 记录使用模型差异范数计算N_k^{eff}
        logger.info(f"客户端 {self.name}: 最终模型差异范数={update_norm.item():.6f}")

        # 保存轮次信息
        update._round = model._round if hasattr(model, '_round') else self.round
        
        # 保存元数据用于服务器端处理
        update._n_eff = update_norm.item() if isinstance(update_norm, torch.Tensor) else update_norm
        update._rdp_cost = self.rdp_cost
        update._local_steps = self.current_steps
        update._data_size = self.min_data_size
        
        # 添加clip_norm和sigma值，用于服务器端处理
        update._clip = getattr(self, 'clip', self.clip)
        update._sigma = getattr(self, 'sigma', 1.0)


        # print('self.rdp_cost================================',self.rdp_cost)
        
        # 将更新打包，包含额外的元数据
        cpkg = self.pack(update, 
                        rdp_cost=getattr(update, '_rdp_cost', 0.0),
                        local_steps=getattr(update, '_local_steps', 1),
                        data_size=getattr(update, '_data_size', 0),
                        clip=getattr(update, '_clip', self.clip_norm),
                        sigma=getattr(update, '_sigma', 1.0))
        
        return cpkg

    
    def pack(self, update: torch.Tensor, **kwargs) -> dict:
        """打包更新，应用压缩（如果启用）。
        
        Args:
            update: 模型更新
            **kwargs: 额外的打包参数
            
        Returns:
            dict: 打包好的更新
        """
        # 构建打包结果，包含模型以及额外参数
        pack_result = {"model": update}
        for k, v in kwargs.items():
            pack_result[k] = v
            
        # 如果启用压缩，使用CompressionManager处理
        if self.use_compression and self.compression_manager is not None:
            if isinstance(update, torch.Tensor):
                update_dict = {"model": update}
                compressed_dict = self.compression_manager.compress(update_dict)
                # 记录压缩统计信息
                stats = self.compression_manager.get_compression_statistics()
                logger.debug(f"客户端 {self.name} 压缩统计: 原始大小={stats['original_size_mb']:.4f}MB, "
                        f"压缩后={stats['compressed_size_mb']:.4f}MB, "
                        f"压缩比={stats['compression_ratio']:.2f}x")
                pack_result["model"] = compressed_dict["model"]
        
        return pack_result
    
    def get_median_gradient_norm(self, model) -> float:
        """
        计算模型中所有可训练参数梯度的范数的中位数
        返回: 中位数
        """
        grad_norms = []
        for param in model.parameters():
            if param.grad is not None:
                # 计算单个参数的梯度的L2范数
                grad_norm = param.grad.detach().norm(2).item()
                grad_norms.append(grad_norm)
        
        if len(grad_norms) == 0:
            return 0.0
        
        # 计算中位数
        return torch.median(torch.tensor(grad_norms)).item()
    
    
    def find_optimal_noise_multiplier(
        self,
        epochs: int,
        # max_grad_norm: float,
        target_epsilon: float,
        target_delta: float,
        sample_rate: float,
        dataset_size: int,
        batch_size: int,
        alphas: list = None,
        sigma_min: float = 0.01,
        sigma_max: float = 10.0,
        tolerance: float = 0.01,
        max_iter: int = 100
    ) -> float:
        """
        计算达到目标隐私预算(ε,δ)所需的最小噪声乘数
        
        参数:
            epochs: 训练轮次
            # max_grad_norm: 梯度裁剪阈值
            target_epsilon: 目标隐私预算ε
            target_delta: 目标δ值
            sample_rate: 采样率 (batch_size / dataset_size)
            dataset_size: 数据集大小
            batch_size: 批次大小
            alphas: RDP阶数列表，默认[1.5, 2, 4, 8, 16, 32, 64]
            sigma_min: 噪声乘数搜索下限
            sigma_max: 噪声乘数搜索上限
            tolerance: 精度容差
            max_iter: 最大迭代次数
            
        返回:
            满足隐私预算的最小噪声乘数
        """
        # 初始化RDP会计器
        accountant = RDPAccountant()
        
        # 设置RDP阶数(如果未提供)
        if alphas is None:
            alphas = [1.5, 2, 4, 8, 16, 32, 64]
        
        # 计算总步数
        steps_per_epoch = dataset_size // batch_size
        total_steps = epochs * steps_per_epoch
        
        # 二分搜索寻找最优噪声乘数
        low = sigma_min
        high = sigma_max
        best_sigma = None
        
        from tqdm import tqdm
        # 使用tqdm显示进度
        with tqdm(total=max_iter, desc="寻找最优噪声乘数") as pbar:
            for i in range(max_iter):
                # 计算中点
                sigma = (low + high) / 2.0
                
                # 创建新的会计器
                accountant = RDPAccountant()
                
                # 模拟训练过程
                for step in range(total_steps):
                    accountant.step(
                        noise_multiplier=sigma,
                        sample_rate=sample_rate
                    )
                
                # 计算当前隐私消耗
                epsilon,_ = accountant.get_privacy_spent(delta=target_delta)
                
                # 检查是否满足目标
                if epsilon <= target_epsilon:
                    best_sigma = sigma
                    # 噪声过大，尝试缩小
                    high = sigma - tolerance
                else:
                    # 噪声不足，尝试增大
                    low = sigma + tolerance
                
                # 检查收敛
                if high - low < tolerance:
                    break
                    
                # 更新进度条
                pbar.set_postfix({
                    'epsilon': epsilon, 
                    'sigma': sigma,
                    'range': f"[{low:.4f}, {high:.4f}]"
                })
                pbar.update(1)
        
        # 如果没有找到满足条件的噪声值，选择上限值
        if best_sigma is None:
            best_sigma = sigma_max
            print(f"警告: 在范围内未找到满足ε<={target_epsilon}的噪声值。使用最大值: {best_sigma}")
        
        return best_sigma
    

    def unpack(self, svr_pkg: Any) -> Any:
        """解包服务器包，应用解压缩（如果启用）。
        
        Args:
            svr_pkg: 服务器发送的包
            
        Returns:
            解包后的模型
        """
        # 首先获取模型

        # self.global_update_direction = svr_pkg['global_update_direction']
        
        
        unpacked = svr_pkg['model']  #super().unpack(svr_pkg)
        
        # 如果启用压缩，使用CompressionManager处理解压缩
        if self.use_compression and self.compression_manager is not None:
            if isinstance(unpacked, torch.Tensor):
                unpacked_dict = {"model": unpacked}
                decompressed_dict = self.compression_manager.decompress(unpacked_dict)
                svr_pkg['model'] = decompressed_dict["model"]
                # return decompressed_dict["model"]
        return svr_pkg
    
    
    def module_cosine_similarity(m1: nn.Module, m2: nn.Module, eps: float = 1e-8) -> float:
        """
        计算两个 nn.Module 可训练参数拼接后向量的余弦相似度。
        前提：m1 与 m2 结构相同，参数一一对应。
        """
        # 收集参数向量
        vecs1, vecs2 = [], []
        for (n1, p1), (n2, p2) in zip(m1.named_parameters(), m2.named_parameters()):
            assert n1 == n2, f"参数名不匹配：{n1} vs {n2}"
            # 展平参数并拼接
            vecs1.append(p1.detach().view(-1))
            vecs2.append(p2.detach().view(-1))

        # 拼成单个长向量
        v1 = torch.cat(vecs1)
        v2 = torch.cat(vecs2)

        # 计算余弦相似度
        cos_sim = F.cosine_similarity(v1.unsqueeze(0), v2.unsqueeze(0), dim=1, eps=eps)
        return cos_sim.item()
    

    def adaptive_scheduler(self, round,loss_value=None, grad_similarity=None):
        """自适应噪声调度函数，确保参数有默认值，防止调用时缺少参数"""
        
        if grad_similarity is None:
            # 若尚未设置参考损失且传入值可用，则初始化一次
            if self.loss_ref is None and loss_value is not None:
                self.loss_ref = loss_value
            # 使用固定参考损失做除数，不修改 loss_value
            
        # 初始化β_k为1.0（默认无调整）
        beta_k = 1.0  
        # 计算基于损失的组件
        loss_component = 1.0
        if loss_value is not None and self.loss_ref is not None:
            # 归一化损失值，使用本地最大损失值作为参考: L̂_k = L_k / L_{ref}
            norm_loss = loss_value / self.loss_ref
            # 限制在[0, 1]范围内，符合归一化的定义
            # norm_loss = min(max(norm_loss, 0.0), 1.0)
            loss_component = norm_loss
            logger.warning(f"客户端={self.name}, 损失归一化: 原始损失={loss_value:.4f}, 本地最大损失={self.loss_ref:.4f}, 归一化结果={norm_loss:.4f}")
            
        # 计算基于梯度相似度的组件
        similarity_component = 0.0
            # print(f'grad_similarity={grad_similarity}')
        if  grad_similarity is not None:
            # 将相似度从[-1,1]映射到[0,1]
            sim_k = (1 + grad_similarity) / 2
            similarity_component = 1.0 - sim_k  # 相似度越高，噪声越小
            logger.warning(f"梯度相似度组件: 原始相似度={grad_similarity:.4f}, 归一化相似度={sim_k:.4f}, 组件值={similarity_component:.4f}")


        
        # 计算自适应因子β_k = γ·(1-sim_k) + (1-γ)·L̂_k
        # 如果是初始轮次且没有梯度相似度，则更多依赖损失组件
        if grad_similarity is None:
            # 初始轮次中，更多依赖损失组件
            beta_k = loss_component
            logger.info(f"初始轮次中，仅使用损失组件计算自适应因子: β_k={beta_k:.4f}")
        else:
            # 正常计算自适应因子
            beta_k = self.gamma * similarity_component + (1 - self.gamma) * loss_component #self.gamma) 越小越好
            
        # 确保β_k >= 0
        beta_k = max(0.0, beta_k)
        logger.warning(f"自适应因子β_k计算: γ={self.gamma:.4f}, 相似度组件={similarity_component:.4f}, 损失组件={loss_component:.4f}, β_k={beta_k:.4f}")
            
        final_eplson = 1.
        # 根据噪声衰减模式计算最终的噪声标准差
        if self.noise_decay_mode == 'hyperbolic':
            # 双曲衰减模式: σ_k = σ_{initial_base} · sqrt(β_k) / sqrt(1 + k · t_{global})
            decay_factor = ( 1 + self.hyperbolic_decay_k * round)
            final_eplson = self.initial_eplson * (1+ beta_k * decay_factor)
            logger.debug(f"使用双曲衰减模式: 衰减系数k={self.hyperbolic_decay_k:.4f}, "
                        f"当前轮次={round}, 衰减因子={decay_factor:.4f}")
        else:
            # 指数衰减模式（默认）: σ_k = σ_{initial_base} · sqrt(β_k) · e^(-0.5 λ_{noise} t_{global})
            decay_factor = math.exp(self.noise_decay_rate * round)
            final_eplson = self.initial_eplson *  (1 + beta_k * decay_factor)
            logger.debug(f"使用指数衰减模式: 衰减率λ={self.noise_decay_rate:.4f}, "
                        f"当前轮次={round}, 衰减因子={decay_factor:.4f}")

        
        
        
        # final_eplson = 1.
        # if grad_similarity is None:
        #     # 初始轮次中，更多依赖损失组件

        #     final_eplson = self.initial_eplson * (1 + loss_component)
        # else:
        #     # 根据噪声衰减模式计算最终的噪声标准差
        #     if self.noise_decay_mode == 'hyperbolic':
        #         # 双曲衰减模式: σ_k = σ_{initial_base} · sqrt(β_k) / sqrt(1 + k · t_{global})
        #         decay_factor = 1/ ( 1 + self.hyperbolic_decay_k * round)
        #         beta_k = decay_factor * loss_component  + (1 -decay_factor) * similarity_component
        #         final_eplson = self.initial_eplson * (1+ beta_k)
        #         logger.debug(f"使用双曲衰减模式: 衰减系数k={self.hyperbolic_decay_k:.4f}, "
        #                     f"当前轮次={round}, 衰减因子={decay_factor:.4f}")
        #     else:
        #         # 指数衰减模式（默认）: σ_k = σ_{initial_base} · sqrt(β_k) · e^(-0.5 λ_{noise} t_{global})
        #         decay_factor = math.exp(-self.noise_decay_rate * round)
        #         beta_k = decay_factor * loss_component  + (1 -decay_factor) * similarity_component
        #         final_eplson = self.initial_eplson *  (1 + beta_k)
        #         logger.debug(f"使用指数衰减模式: 衰减率λ={self.noise_decay_rate:.4f}, "
        #                     f"当前轮次={round}, 衰减因子={decay_factor:.4f}")
        #     # 正常计算自适应因子
        #     # beta_k = self.gamma * similarity_component + (1 - self.gamma) * loss_component
            
        # # 确保β_k >= 0
        # # beta_k = max(0.0, beta_k)
        # logger.warning(f"自适应因子β_k计算: 相似度组件={similarity_component:.4f}, 损失组件={loss_component:.4f}, β_k={beta_k:.4f}")
            

            
        logger.warning(f"计算自适应噪声: 基础噪声模式={self.noise_base_mode}, 衰减模式={self.noise_decay_mode}, "
                    f"initial_eplson={self.initial_eplson:.4f}, beta_k={beta_k:.4f}, 最终预算eplson={final_eplson:.4f} ")
            
        return final_eplson
    

    
