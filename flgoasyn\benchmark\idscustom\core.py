import os
from flgo.benchmark.toolkits.cv.classification import GeneralCalculator, FromDatasetPipe, FromDatasetGenerator
from .config import train_data
import torch
import pickle
import datetime
import os.path as osp
try:
    from .config import test_data
except:
    test_data = None
try:
    from .config import val_data
except:
    val_data = None

class TaskGenerator(FromDatasetGenerator):
    def __init__(self):
        super(TaskGenerator, self).__init__(benchmark=os.path.split(os.path.dirname(__file__))[-1],
                                            train_data=train_data, val_data=val_data, test_data=test_data)
        
        self.scene = 'horizontal'

class TaskPipe(FromDatasetPipe):
    def __init__(self, task_path):
        super(TaskPipe, self).__init__(task_path, train_data=train_data, val_data=val_data, test_data=test_data)

class IDSCalculator(GeneralCalculator):
    # 默认的metrics文件名
    DEFAULT_METRICS_FILE = "all_metrics.pkl"


        # 标记是否已初始化过metrics文件
    _metrics_initialized = False
    
    def __init__(self, device=None, optimizer=None, **kwargs):
        """
        初始化计算器
        
        Args:
            device: 计算设备
            optimizer: 优化器
            **kwargs: 其他参数，包括optimizer_name等
        """
        # 处理optimizer_name参数
        if 'optimizer_name' in kwargs and optimizer is None:
            optimizer = kwargs['optimizer_name']
            
        super(IDSCalculator, self).__init__(device, optimizer)
        # 初始化metrics文件
        self._initialize_metrics_file()
    
    def _initialize_metrics_file(self):
        """
        初始化metrics文件，如果已存在则删除
        """
        # 确保只在程序首次运行时执行一次
        if IDSCalculator._metrics_initialized:
            return
        
        # 创建保存目录
        save_dir = osp.join(os.getcwd(), 'metrics_results')
        os.makedirs(save_dir, exist_ok=True)
        
        # 构建默认文件路径
        filepath = osp.join(save_dir, self.DEFAULT_METRICS_FILE)
        
        # 如果文件已存在，则删除
        if os.path.exists(filepath):
            try:
                os.remove(filepath)
                print(f"已删除现有的metrics文件: {filepath}")
            except Exception as e:
                print(f"删除metrics文件时出错: {e}")
        
        # 标记为已初始化
        IDSCalculator._metrics_initialized = True
    
    @torch.no_grad()
    def test(self, model, dataset, batch_size=64, num_workers=0, pin_memory=False):
        import torch
        import numpy as np
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report
        
        total_loss = 0
        y_true = []
        y_pred = []

        # 用于收集分类前的特征
        all_features = []

        dataloader = self.get_dataloader(dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers)
        
        with torch.no_grad():
            for batch_id, batch in enumerate(dataloader):
                batch = self.to_device(batch)
                batch_data = batch[0]
                batch_target = batch[1]

                # 使用model.predict获取预测结果和concat_repr特征
                pred, concat_repr = model.predict(batch_data)

                # 直接使用从predict返回的concat_repr特征
                if concat_repr is not None:
                    # 将特征转换为numpy数组并添加到列表中
                    if isinstance(concat_repr, torch.Tensor):
                        features_np = concat_repr.cpu().numpy()
                    else:
                        features_np = concat_repr
                    all_features.append(features_np)

                # 修复：计算损失时，不能直接使用预测的类别索引，需要使用logits
                if hasattr(model, 'compute_loss'):
                    # 如果模型提供了compute_loss方法，直接使用
                    batch_mean_loss = model.compute_loss(pred, batch_target).item()
                else:
                    # 获取logits用于计算损失，而不是直接使用预测的类别索引
                    # 先执行forward获取logits
                    outputs = model(batch_data)
                    if isinstance(outputs, dict) and 'logits' in outputs:
                        logits = outputs['logits']
                    else:
                        logits = outputs
                    # 使用logits计算损失
                    batch_mean_loss = self.criterion(logits, batch_target).item()

                total_loss += batch_mean_loss * len(batch_target)


                                # 将预测结果和真实标签转换为numpy数组
                if isinstance(pred, torch.Tensor):
                    pred = pred.cpu().numpy()
                if isinstance(batch_target, torch.Tensor):
                    batch_target = batch_target.cpu().numpy()
                
                y_true.append(batch_target)
                y_pred.append(pred)
        
        # 合并所有批次的结果
        y_true = np.concatenate(y_true)
        y_pred = np.concatenate(y_pred)

        # 合并所有特征
        if all_features:
            try:
                import numpy as np
                all_features_array = np.concatenate(all_features, axis=0)
                print(f"提取的特征形状: {all_features_array.shape}")
            except Exception as e:
                print(f"合并特征时出错: {e}")
                all_features_array = None
        else:
            all_features_array = None

        # 计算评估指标
        metrics = {
            'loss': total_loss / len(dataset),
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted', zero_division=0),
            'recall': recall_score(y_true, y_pred, average='weighted', zero_division=0),
            'F1': f1_score(y_true, y_pred, average='weighted', zero_division=0),
            'confusion_matrix': confusion_matrix(y_true, y_pred).tolist(),
            # 添加分类前的特征
            # 'features_before_classifier': all_features_array.tolist() if all_features_array is not None else None,
            # 'features_shape': all_features_array.shape if all_features_array is not None else None,
            # 'y_true': y_true.tolist(),  # 确保可序列化
            # 'y_pred': y_pred.tolist() # 确保可序列化
           # 'classification_report': classification_report(y_true, y_pred)
        }
        
        metrics1 = {
            'loss': total_loss / len(dataset),
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='micro', zero_division=0),
            'recall': recall_score(y_true, y_pred, average='micro', zero_division=0),
            'F1': f1_score(y_true, y_pred, average='micro', zero_division=0),
            'confusion_matrix': confusion_matrix(y_true, y_pred).tolist(),
            'y_true': y_true.tolist(),  # 确保可序列化
            'y_pred': y_pred.tolist(), # 确保可序列化
            # 添加分类前的特征
            'features_before_classifier': all_features_array.tolist() if all_features_array is not None else None,
            'features_shape': all_features_array.shape if all_features_array is not None else None,
            # 'classification_report': classification_report(y_true, y_pred)
        }
        # 保存metrics到pkl文件
        self.save_metrics(metrics1)
        
        return metrics, metrics1



    def save_metrics(self, metrics, filename=None):
        """
        保存metrics信息到pkl文件，以dict形式存储
        
        Args:
            metrics (dict): 包含评估指标的字典
            filename (str, optional): 文件名。如果不提供，将使用默认文件名并追加到现有数据
            
        Returns:
            str: 保存的文件路径
        """
        # 创建保存目录
        save_dir = osp.join(os.getcwd(), 'metrics_results')
        os.makedirs(save_dir, exist_ok=True)
        
        # 如果未指定文件名，使用默认文件名
        if filename is None:
            filename = self.DEFAULT_METRICS_FILE
        
        # 确保文件名有.pkl扩展名
        if not filename.endswith('.pkl'):
            filename += '.pkl'
        
        # 构建完整的文件路径
        filepath = osp.join(save_dir, filename)
        
        # 如果是默认文件且已存在，则加载现有数据并追加
        all_metrics = []
        if filename == self.DEFAULT_METRICS_FILE and os.path.exists(filepath):
            try:
                with open(filepath, 'rb') as f:
                    all_metrics = pickle.load(f)
                if not isinstance(all_metrics, list):
                    all_metrics = [all_metrics]  # 转换为列表
            except Exception as e:
                print(f"加载现有metrics文件出错，将创建新文件: {e}")
                all_metrics = []
        
        # 添加当前metrics
        all_metrics.append(metrics)
        
        # 保存所有metrics到文件
        with open(filepath, 'wb') as f:
            pickle.dump(all_metrics, f)
        
        return filepath
    



# IDSCalculator.criterion = torch.nn.CrossEntropyLoss(reduction='none')
TaskCalculator = IDSCalculator

# TaskCalculator = GeneralCalculator

















