import requests
import json
import time

def get_following_count(username):
    """获取GitHub用户关注的仓库数量"""
    url = f"https://api.github.com/users/{username}/following"
    response = requests.get(url)
    
    if response.status_code == 200:
        following_data = response.json()
        return len(following_data)
    else:
        print(f"请求失败，状态码: {response.status_code}")
        return None

def get_starred_repos(username, per_page=100):
    """获取GitHub用户星标的仓库列表"""
    all_repos = []
    page = 1
    
    while True:
        url = f"https://api.github.com/users/{username}/starred?page={page}&per_page={per_page}"
        headers = {"Accept": "application/vnd.github.v3+json"}
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            repos = response.json()
            if not repos:  # 没有更多仓库
                break
                
            all_repos.extend(repos)
            page += 1
            time.sleep(0.5)  # 避免触发API限制
        else:
            print(f"请求失败，状态码: {response.status_code}")
            break
    
    return all_repos

def check_if_starred(username, repo_full_name):
    """检查特定仓库是否在用户的星标列表中"""
    repos = get_starred_repos(username)
    for repo in repos:
        if repo['full_name'].lower() == repo_full_name.lower():
            return True, repo
    return False, None

def truncate_text(text, max_length=70):
    """截断文本，避免过长"""
    if not text:
        return "无描述"
    if len(text) <= max_length:
        return text
    return text[:max_length] + "..."

def print_repo_info(repo, index=None):
    """打印仓库信息"""
    print("-" * 80)
    if index:
        print(f"{index}. {repo['full_name']}")
    else:
        print(f"{repo['full_name']}")
    desc = truncate_text(repo.get('description', '无描述'))
    print(f"   描述: {desc}")
    print(f"   URL: {repo['html_url']}")
    print(f"   语言: {repo.get('language', '未指定')}")
    print(f"   星标数: {repo['stargazers_count']}")
    print("-" * 80)

if __name__ == "__main__":
    username = "yaomingchen"
    search_repo = "WwZzz/easyFL"
    
    print(f"正在检查用户 {username} 是否星标了 {search_repo}...")
    starred, repo_info = check_if_starred(username, search_repo)
    
    if starred:
        print(f"✅ 用户 {username} 已经星标了 {search_repo}")
        print_repo_info(repo_info)
    else:
        print(f"❌ 用户 {username} 没有星标 {search_repo}")
        
        # 尝试搜索类似的仓库
        print("\n正在搜索类似的仓库...")
        repos = get_starred_repos(username)
        found = False
        for repo in repos:
            if "easyFL" in repo['name'] or "WwZzz" in repo['full_name']:
                found = True
                print_repo_info(repo)
        
        if not found:
            print("没有找到类似的仓库") 