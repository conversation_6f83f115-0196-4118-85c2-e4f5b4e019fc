#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Plot server model update norm (agg_mean) data

This script reads data from server_agg_mean_norms.pkl file,
and plots the model update norm changes across training rounds.
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
import argparse
from matplotlib.ticker import MaxNLocator
from scipy.signal import savgol_filter

def load_agg_mean_norms(file_path):
    """Load server stored agg_mean norm data"""
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist")
        return None
    
    try:
        with open(file_path, 'rb') as f:
            data = pickle.load(f)
        print(f"Successfully loaded data with {len(data)} records")
        return data
    except Exception as e:
        print(f"Failed to load data: {str(e)}")
        return None

def plot_agg_mean_norms(data, output_path=None, figsize=None):
    """Plot agg_mean norm changes across training rounds"""
    if data is None or len(data) == 0:
        print("No data to plot")
        return
    
    # Extract rounds and norm data
    rounds = [item['round'] for item in data]
    norms = [item['norm'] for item in data]
    
    # Set global font to Times New Roman
    plt.rcParams['font.family'] = 'Times New Roman'
    plt.rcParams['mathtext.fontset'] = 'stix'
    plt.rcParams['axes.unicode_minus'] = False
    
    # Create figure
    if figsize is None:
        figsize = (6.4, 4.8)  # matplotlib default
    
    fig, ax = plt.subplots(figsize=figsize)
    
    # Plot main curve without markers
    ax.plot(rounds, norms, linestyle='-', linewidth=1.5, 
            color='#1f77b4', alpha=1, label='Model Update Norm')
    
    # Add smooth trend line
    if len(norms) > 10:
        # Calculate window length (must be odd and less than data length)
        window_length = min(len(norms) - (len(norms) % 2 == 0), 11)
        # Ensure window_length is odd
        if window_length % 2 == 0:
            window_length -= 1
        # Apply Savitzky-Golay filter for smoothing
        poly_order = min(3, window_length - 1)
        smooth_norms = savgol_filter(norms, window_length, poly_order)
        ax.plot(rounds, smooth_norms, linestyle='--', linewidth=1.5, 
                color='#ff7f0e', label='Trend Line')
    
    # Set chart labels
    ax.set_xlabel('Rounds', fontsize=16, labelpad=10)
    ax.set_ylabel('Average Gradient Value', fontsize=16, labelpad=10)
    
    # Set x-axis to start from 0 and end at 100
    ax.set_xlim(left=0, right=100)
    
    # Set x-axis ticks
    rounds_step = 10
    x_ticks = list(range(0, 101, rounds_step))
    ax.set_xticks(x_ticks)
    ax.set_xticklabels([str(x) for x in x_ticks], fontsize=14)
    
    # Set y-axis range and ticks
    y_min = 0.4
    y_max = max(norms) * 1.1 if norms else 1.0
    ax.set_ylim(y_min, y_max)
    
    # Set y-axis main ticks
    y_step = 0.4
    y_ticks = np.arange(y_min, y_max + 0.01, y_step)
    ax.set_yticks(y_ticks)
    ax.set_yticklabels([f"{y:.1f}" for y in y_ticks], fontsize=14)
    
    # Turn off minor ticks
    ax.minorticks_off()
    
    # Add grid lines
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # Add legend
    legend = ax.legend(fontsize=14, loc='best')
    legend.get_frame().set_linewidth(1.0)
    
    # Adjust layout
    plt.tight_layout()
    
    # Save chart
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {output_path}")
    
    # Show chart
    plt.show()

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Plot server model update norm (agg_mean) data')
    parser.add_argument('--input', type=str, default='C:/Users/<USER>/Desktop/result/CICDIS17/draw/server_agg_mean_norms.pkl',
                        help='Input data file path (default: ../../server_agg_mean_norms.pkl)')
    parser.add_argument('--output', type=str, default='server_agg_mean_norms.png',
                        help='Output chart file path (default: server_agg_mean_norms.png)')
    parser.add_argument('--width', type=float, default=6.4,
                        help='Chart width (default: 6.4)')
    parser.add_argument('--height', type=float, default=4.8,
                        help='Chart height (default: 4.8)')
    
    args = parser.parse_args()
    
    # Load data
    data = load_agg_mean_norms(args.input)
    
    # Plot chart
    if data:
        plot_agg_mean_norms(data, args.output, figsize=(args.width, args.height))

if __name__ == '__main__':
    main() 