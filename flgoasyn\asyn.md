# 车联网场景下结合个性化本地RDP和全局RDP修正的FedAsync异步联邦学习方案

本文档详细描述了在车联网（IoV）场景下的FedAsync异步联邦学习方案。该方案结合了个性化本地Rényi差分隐私（RDP）和服务器端的全局RDP修正机制，旨在平衡模型精度、训练效率与隐私保护。

## API命名约定说明

**重要**：本项目基于FLGO框架开发，在实现和扩展组件时遵循以下命名约定：

- 在所有代码中使用`server`而非`coordinator`引用联邦学习服务器/协调器对象
- 特别是在自定义Logger类中（如`myLogger.py`），应使用`self.server`而非`self.coordinator`访问服务器组件
- 所有与服务器相关的方法调用（如`.test()`、`.global_test()`等）均通过`server`对象进行

此命名约定与FLGO框架保持一致，不遵循可能导致运行时错误或API不兼容问题。若发现代码中使用了`coordinator`，应将其更正为`server`以确保兼容性。

## 自定义日志系统 (myLogger.py)

本方案实现了一个增强型日志系统，通过扩展FLGO的`BasicLogger`类，为联邦学习过程提供更全面的指标记录和可视化功能。`myLogger`类是核心组件，具有以下特点：

### 功能设计

1. **多维指标追踪**：除了基础的准确率外，系统自动记录精确率、召回率、F1分数等关键指标，提供全面的模型评估
2. **多层次测试支持**：通过`flag`参数的设计，支持在训练、验证和测试三种数据划分上分别进行评估
3. **分层统计分析**：提供了全局和本地两个维度的指标分析
   * 全局模型在整体测试集上的表现
   * 全局模型在各数据划分（训练、验证、测试）上的表现
   * 本地模型在每个客户端数据上的表现分布
   * 客户端性能的统计特性（均值、标准差）
4. **加权性能计算**：基于客户端数据量自动对本地指标进行加权平均，更准确反映系统真实性能
5. **实时监控**：每轮训练后输出当前关键指标，便于实时监控训练进展

### 实现细节

`myLogger`类在`flgoasyn/utils/myLogger.py`中实现，包含以下主要方法：

- **initialize()**: 初始化各类指标存储容器，为不同数据划分和不同指标类型设置记录空间
- **log_once()**: 核心记录方法，在每轮训练后收集并记录各种指标
  * 调用`self.server.test()`获取全局测试指标
  * 调用`self.server.test(flag='train'/'val')`获取训练/验证指标
  * 调用`self.server.global_test(flag='train'/'val'/'test')`获取所有客户端上的本地指标
  * 计算加权平均和统计特性
- **show_current_output()**: 格式化输出当前轮次的关键指标

### 与差分隐私的协同

`myLogger`类与差分隐私机制无缝集成，可以准确记录在不同隐私预算和噪声设置下的模型表现，帮助研究人员分析隐私保护与模型性能之间的权衡关系。

**A. 系统设定与初始化**

1.  **全局RDP预算 (Global RDP Budget):**
    *   设定整个训练过程的RDP总预算 `epsilon_total` 和 `delta`。
    *   Rényi阶数 `alphas` 用于精确计算隐私消耗。

2.  **模型与训练参数:**
    下表列出了关键参数及其在 `flgoasyn/algorithm/asyncfl.py` 代码中的对应变量名。

| 概念/符号 | 代码变量名 | 描述 |
| :--- | :--- | :--- |
| **通用参数** | | |
| 客户端本地学习率 | `learning_rate` | 客户端本地训练的学习率。 |
| 客户端本地训练轮数/步数 | `num_epochs`/`num_steps` | 客户端本地训练的迭代次数。 |
| 每梯度裁剪范数 | `clip_norm` | DP-SGD中单样本梯度的裁剪阈值。 |
| **服务器端自适应学习率 (方案一)** | | |
| 学习率基础系数 | `lambda_alr` | 自适应学习率的基础系数。 |
| 学习率平滑项 | `epsilon_alr` | 自适应学习率分母的平滑项。 |
| **服务器端自适应学习率 (方案二)** | | |
| 基础学习率 | `eta_0` | 学习率的基础值。 |
| Staleness衰减指数 | `staleness_exponent` | Staleness衰减项的指数，默认为0.5。 |
| 隐私预算衰减指数 | `gamma_priv` | 隐私预算衰减项的指数，默认为0.5。 |
| **Staleness度量** | | |
| Staleness度量平滑项 | `delta_sm` | Staleness计算时分母的平滑项。 |
| **客户端自适应噪声** | | |
| 噪声自适应因子权重 | `gamma` | 融合损失和梯度相似度的权重。 |
| 噪声衰减率 | `noise_decay_rate` | 控制噪声随轮次衰减的速度(λ_noise)。 |
| 噪声基准模式 | `noise_base_mode` | σ_{initial_base}计算方式，可选值为'fixed'或'dynamic'。 |
| 初始最大噪声值 | `initial_max_noise` | 固定模式下使用的初始最大噪声值。 |
| **N_k^{eff}计算方式** | | |
| 有效更新范数计算方法 | `n_eff_method` | 选择客户端计算N_k^{eff}的方法 (`model_diff` 或 `clean_grad_sum`)。 |
| **全局更新方向** | | |
| EMA平滑因子 | `global_direction_ema_alpha` | 用于计算`global_update_direction`的指数移动平均平滑因子。 |
| **服务器端预算分配** | | |
| 隐私预算分配策略 | `budget_allocation_strategy`| 服务器端每轮隐私预算的分配策略。 |
| 预期总轮次 | `expected_total_rounds` | 用于预算分配策略的参考总轮数。 |
| 每轮最大预算 | `max_budget_per_round` | 单轮聚合的隐私预算上限。 |
| **服务器端敏感度计算** | | |
| 敏感度计算方法 | `sensitivity_method` | 服务器端聚合噪声的敏感度计算方法 (`'max'` 或 `'median'`)。 |

**B. 客户端（车辆）侧：个性化本地差分隐私SGD (DP-SGD)**

对于每个参与更新的车辆客户端 `k`：

1.  **下载全局模型:**
    客户端 `k` 从服务器下载当前的全局模型 $w_{\tau_k}^g$ (版本号为 $\tau_k$)，并获取一个近期的"全局更新方向"估计 `global_update_direction`。

2.  **计算自适应本地噪声 (Adaptive Local Noise):**
    在本地训练开始前，客户端会基于当前模型状态计算一个自适应的噪声水平。此过程由 `DPManager.get_adaptive_noise_multiplier()` 方法封装。
    *   **a. 评估模型状态:**
        *   **本地损失 $L_k$**: 在本地数据的一个样本批次上计算当前模型的损失。
        *   **梯度方向余弦相似度 $sim_k$**: 计算本地初始梯度方向与服务器提供的 `global_update_direction` 之间的余弦相似度。
    *   **b. 选择噪声基准模式:**
        系统支持两种σ_{initial_base}（噪声基准值）的计算模式：
        *   **固定模式 (`noise_base_mode='fixed'`)**: 使用预设的`initial_max_noise`作为固定的噪声基准值。此模式下，噪声基准值在整个训练过程中保持不变，只受衰减因子影响。
        *   **动态模式 (`noise_base_mode='dynamic'`, 默认)**: 使用当前的`noise_multiplier`（即`noise_scale`参数初始化的值）作为动态调整的噪声基准值。此模式下，噪声基准值可能随训练过程中的各种因素变化。
    *   **c. 计算噪声乘数:**
        该方法根据 `a1.md` 中的公式计算最终的噪声标准差（在代码中体现为`noise_multiplier`）。
        其核心公式为：
        $σ_k = σ_{initial\_base} \cdot \sqrt{\beta_k} \cdot e^{-0.5 \lambda_{noise} t_{global}}$
        其中：
        *   $σ_{initial\_base}$ 是基础噪声标准差，由所选的噪声基准模式决定：
            - 固定模式下：$σ_{initial\_base} = initial\_max\_noise$
            - 动态模式下：$σ_{initial\_base} = noise\_scale$
        *   $e^{-\lambda_{noise} t_{global}}$ 是噪声衰减项，`λ_noise` 由 `noise_decay_rate` 参数控制，$t_{global}$ 为当前轮次。
        *   $\beta_{k}$ 是自适应因子，计算方式为：$\beta _{k}= \gamma \cdot (1-sim_k) + (1- \gamma)\cdot L̂_k $
            *   $\gamma$ 是 `gamma` 参数，用于权衡梯度相似度和损失。
            *   $sim_k$ 是归一化后的梯度相似度。
            *   $L̂_k$ 是归一化后的本地损失。
        这个机制旨在为"贡献较大"或"方向一致"的更新（即低损失、高相似度）分配更小的噪声，反之亦然。

3.  **本地差分隐私SGD (DP-SGD) 训练:**
    客户端使用 `DPManager` 将其本地模型、优化器和数据加载器转换为差分隐私模式。在`flgoasyn`中，这通过调用一个类似Opacus库的`make_private`方法实现。该方法会自动处理DP-SGD的每个步骤：
    *   i.  **计算每样本梯度**。
    *   ii. **梯度裁剪**: 将每个样本的梯度范数裁剪到 `clip_norm`。
    *   iii. **添加高斯噪声**: 根据第二步计算出的 `noise_multiplier` 和 `clip_norm`，为裁剪后的梯度添加高斯噪声。
    *   iv. **模型更新**: 使用加噪后的梯度更新本地模型。

    整个训练过程（`num_epochs` 或 `num_steps`）由DP库自动管理。

4.  **有效更新范数 $N_k^{eff}$ 计算:**
    根据 `n_eff_method` 参数，客户端可以选择两种方式计算有效更新范数：
    *   **方法1 (`n_eff_method='model_diff'`, 默认方法)**: 
        $N_k^{eff} = ||Δw_k||_2$ 
        即计算加噪后的模型更新的L2范数。这是初始实现方式，简单但可能受噪声影响。
    *   **方法2 (`n_eff_method='clean_grad_sum'`)**: 
        $N_k^{eff} = ||Δw_{k,clean\_sum}||_2 = || \sum_{j=1}^{J_k} η_l g̃_{k,j} ||_2$
        即跟踪所有已裁剪但未加噪的梯度，并将其乘以学习率后求和得到的L2范数。这种方法更符合理论定义，不受噪声影响，但计算复杂度更高。

5.  **计算并打包上传信息:**
    训练完成后，客户端将以下信息打包发送给服务器：
    *   **隐私化后的模型更新 $Δŵ_k$**: 即训练后的本地模型与初始全局模型的差值。
    *   **有效更新范数 $N_k^{eff}$**: 根据选定的 `n_eff_method` 计算得到，服务器将使用此值来计算staleness。
    *   **本地RDP成本 $ε_{k,local}$**: 本次本地训练所消耗的RDP预算，由`DPManager`根据训练步数和噪声水平计算得出。
    *   **本地训练步数 `local_steps`**: 客户端在本地执行的SGD步数。
    *   **全局模型版本号 $\tau_k$**: 客户端训练所基于的全局模型版本。

**C. 边缘节点：FedAsync聚合与自适应全局RDP修正**

服务器持续接收来自客户端的更新包，并将其存入缓冲区。当缓冲区大小达到 `buffer_size` 时，触发一次聚合。

1.  **计算自适应学习率 $\eta_{g,k}$**:
    对于聚合集 $S_t$ 中的每个客户端 `k`，服务器计算一个独立的全局学习率（或聚合权重）$\eta_{g,k}$。服务器通过 `lr_scheme` 参数选择不同的计算方案：
    *   **方案 1 (`lr_scheme=1`, 基于Staleness范数比率):**
        *   **a. 计算 Staleness $\gamma(k)$**:
            $\gamma(k) = \frac{||w_t^g - w_{\tau_k}^g||_2}{N_k^{eff} + \delta_{sm}}$
            其中 $w_t^g$ 是服务器当前模型，$w_{\tau_k}^g$ 是客户端 `k` 训练时使用的旧模型，$N_k^{eff}$ 是客户端上传的有效更新范数（由`n_eff_method`确定计算方式）。
        *   **b. 计算学习率 $\eta_{g,k}$**:
            $\eta_{g,k} = \frac{\lambda_{alr}}{\gamma(k) + \varepsilon_{alr}}$

    *   **方案 2 (`lr_scheme=2`, 基于Staleness轮次差和隐私预算衰减):**
        *   **a. 定义 Staleness**: $d_k = t - \tau_k$ (当前服务器轮次与客户端更新轮次的差)。
        *   **b. 计算学习率 $\eta_{g,k}$**:
            $\eta_{g,k} = \eta_0 \cdot (d_k + 1)^{-a} \cdot \left(\frac{\epsilon_{rem}^t}{\epsilon_{max}}\right)^{\gamma_{priv}}$
            其中 $a$ 是 `staleness_exponent`，$\gamma_{priv}$ 是 `gamma_priv`。此方案使"陈旧"的更新和在"隐私预算紧张"时期的更新获得较小的权重。

2.  **计算聚合更新**:
    加权的平均更新计算如下：
    $ΔW_t^{agg\_mean} = \frac{1}{|S_t|} \sum_{k \in S_t} \eta_{g,k} Δŵ_k$

3.  **自适应全局噪声（服务器端噪声 $z_g$）:**
    为了确保整个聚合过程满足隐私预算，服务器可能会在聚合结果上添加额外的噪声。
    *   **a. 预算分配**: 服务器首先根据 `budget_allocation_strategy` 决定本轮聚合操作的总RDP预算 `epsilon_round_target`。
        *   `'fixed'`: 分配剩余预算的一个固定比例。
        *   `'uniform'`: 将剩余总预算均匀分配给未来的轮次。
        *   `'rdp_optimal'`: 根据RDP理论，在训练早期分配更多预算。
    *   **b. 计算服务器噪声预算**: 服务器从本轮总预算中减去所有参与聚合客户端已声明消耗的RDP成本，得到服务器可用的噪声预算 `epsilon_g_budget`。
    *   **c. 计算服务器噪声方差 $σ_g^2$**:
        如果 `epsilon_g_budget > 0`，服务器会计算添加噪声所需的方差。这基于L2敏感度 $S_g$，其计算方式由 `sensitivity_method` 参数决定：
        *   **`'max'` (默认)**: 基于理论上最坏情况的敏感度。
            $S_g = \frac{1}{|S_t|} \max_{k \in S_t} (\eta_{g,k} \cdot \text{local\_steps}_k \cdot \text{learning\_rate} \cdot \text{clip\_norm})$
        *   **`'median'`**: 基于本轮所有更新范数中位数的经验敏感度。
            $S_g = \frac{1}{|S_t|} \text{median}_{k \in S_t} (||Δŵ_k||_2)$
            (注意：在代码实现中，$||Δŵ_k||_2$ 对应于客户端上传的 `N_k^{eff}` 值)

        然后，服务器使用计算出的敏感度 $S_g$ 和预算 `epsilon_g_budget` 计算出噪声标准差 `sigma`，并生成高斯噪声 $z_g \sim N(0, \sigma^2 I)$。

4.  **更新全局模型:**
    $ΔŴ_t^{final} = ΔW_t^{agg\_mean} + z_g$
    $w_{t+1}^g = w_t^g + ΔŴ_t^{final}$

5.  **RDP 核算 (RDP Accountant):** 
    服务器端的 `DPManager` 会持续追踪并累加所有客户端和服务器自身产生的RDP消耗，确保总消耗不超过预设的 `epsilon_total`。

6.  **更新全局更新方向 (`global_update_direction`)**
    为了向客户端提供一个稳定的全局训练趋势参考，服务器会维护一个`global_update_direction`。该方向是历次聚合更新`ΔW_t^{agg_mean}`的指数移动平均（Exponential Moving Average, EMA）。

    *   **首次计算**: `global_update_direction` 初始化为第一次聚合的平均更新 `ΔW_0^{agg_mean}`。
    *   **后续更新**:
        $D_{t+1} = (1 - \alpha) \cdot D_t + \alpha \cdot ΔW_t^{agg\_mean}$
        其中 $D_t$ 是旧的`global_update_direction`，$ΔW_t^{agg\_mean}$ 是当前聚合的平均更新，$\alpha$ 是可配置的平滑因子 `global_direction_ema_alpha`。

**D. 检测模型架构**

本方案采用基于LSTM和Transformer的混合神经网络模型进行入侵检测，模型的主要组成部分包括：

1.  **LSTM特征编码器 (`ids/models/lstm_encoder.py`):**
    - 使用LSTM网络对输入的时序数据进行特征提取。
    - 能够捕获序列数据中的时间依赖性。
    - 支持双向LSTM (`bidirectional=True`) 以同时考虑过去和未来的信息。
    - 输入形状为 `[batch_size, seq_len, input_size]`，输出形状为 `[batch_size, seq_len, hidden_size * 2 (if bidirectional) or hidden_size]`。

2.  **位置编码器 (`ids/models/position_encoder.py`):**
    - 为Transformer模型提供序列中每个元素的位置信息。
    - 使用正弦和余弦函数生成位置编码，并将其加到LSTM的输出特征上。
    - 输入和输出形状均为 `[batch_size, seq_len, d_model]`。

3.  **稀疏多头注意力模块 (`ids/models/sparse_transformer.py`):**
    - 实现Transformer中的自注意力机制，但引入了Top-K稀疏性。
    - 通过仅保留注意力分数中的Top-K个最大值，减少计算量，并使模型更关注重要的特征交互。
    *   `k_ratio` 参数控制保留的比例 (0到1之间)。
    *   `input_query`, `input_key`, `input_value` 张量形状为 `[batch_size, seq_len, embed_dim]`，输出形状为 `[batch_size, seq_len, embed_dim]`。

4.  **Transformer编码器层 (`ids/models/transformer_ids_model.py` 中的 `TransformerEncoderLayer`):**
    - 包含稀疏多头自注意力和前馈网络。
    - 每个编码器层对输入序列进行转换和提炼特征。
    - 输入和输出形状均为 `[batch_size, seq_len, d_model]`。

5.  **Transformer入侵检测模型 (`ids/models/transformer_ids_model.py` 中的 `TransformerIDSModel`):**
    - 整合了上述所有组件。
    - 输入数据首先通过LSTM编码器，然后添加位置编码，接着通过多层Transformer编码器层。
    - 最终，Transformer的输出（通常是序列最后一个时间步的输出或池化后的输出）通过一个线性分类器，输出入侵类别的logits。
    - 模型架构流程：输入数据 → LSTM编码 → 位置编码 → 多层Transformer编码器 → 分类器 → 输出logits。

该模型结构旨在有效处理车联网入侵检测任务中的时序性和复杂特征关系，同时为后续的联邦学习训练和差分隐私保护提供基础。

**E. 收敛性与RDP理论的满足**

1.  **RDP理论满足:**
    *   客户端的DP-SGD和服务器的加噪机制均由 `DPManager`（基于RDP核算器）管理，确保了端到端的隐私保护。
    *   通过组合定理，每一轮的RDP成本被精确累加，保证总隐私预算不被超出。

2.  **收敛性:**
    *   自适应学习率机制是保证异步联邦学习收敛的关键。方案通过两种 `lr_scheme` 惩罚陈旧的更新，减轻异步带来的模型震荡。
    *   方案二进一步将隐私预算的消耗情况纳入考量，当预算紧张、噪声增大时，动态减小学习率，有助于在训练后期稳定收敛。
    *   通过 `n_eff_method` 参数选择合适的有效更新范数计算方式，可以提高staleness度量的准确性，进一步改善收敛性能。

**F. 通信压缩方案**

为了提高通信效率，本方案集成了 **QSGD (Quantized Stochastic Gradient Descent)** 量化压缩算法，该功能由 `flgoasyn/utils/compression.py` 中的 `CompressionManager` 提供。

1. **QSGD量化压缩**:
   * **功能**: QSGD通过将模型更新从32位浮点数量化到更低的位数（如4位或8位）来显著减小通信载荷。
   * **实现**: 在客户端，`pack` 方法会调用 `CompressionManager` 对模型更新进行压缩。服务器在 `unpack` 时进行解压。
   * **配置**: 通过 `use_compression: true` 启用，并可设置 `compression_method: 'qsgd'` 和 `compression_bits`。当前代码仅支持 `'qsgd'`

**G. 噪声衰减机制详解**

在本方案中，噪声衰减机制是关键隐私控制组件之一，设计用于随训练进程动态调整噪声水平，以平衡隐私保护和模型性能。系统支持两种噪声衰减模式，各有特点：

1. **指数衰减模式 (exponential):**
   - **数学公式:** $decay_{exp}(t) = e^{-0.5 \lambda_{noise} \cdot t}$
   - **参数:** $\lambda_{noise}$ (噪声衰减率，通过`noise_decay_rate`设置)
   - **特性:**
     * 衰减曲线呈指数形式，训练初期衰减快，后期缓慢
     * 较大的$\lambda_{noise}$值会导致更快的衰减
     * 当$t \to \infty$时，$decay_{exp}(t) \to 0$
   - **适用场景:**
     * 训练初期需要强隐私保护，后期更关注模型收敛
     * 当数据敏感度随时间显著降低的场景
     * 适合对后期训练精度要求高的应用

2. **双曲衰减模式 (hyperbolic):**
   - **数学公式:** $decay_{hyp}(t) = \frac{1}{\sqrt{1 + k_{hyp} \cdot t}}$
   - **参数:** $k_{hyp}$ (双曲衰减系数，通过`hyperbolic_decay_k`设置)
   - **特性:**
     * 衰减曲线更平缓，呈双曲线形式
     * 较大的$k_{hyp}$值会导致更陡峭的初始衰减
     * 当$t \to \infty$时，$decay_{hyp}(t) \sim \frac{1}{\sqrt{t}}$，衰减速度比指数模式慢
   - **适用场景:**
     * 需要在整个训练过程中保持更均衡的隐私-效用权衡
     * 避免噪声过快衰减可能带来的隐私风险
     * 数据敏感度在训练全程中保持较高水平的场景

3. **模式选择与参数调优:**
   - 通过命令行参数`--noise_decay_mode`选择衰减模式
   - 指数模式下，调整`--noise_decay_rate`($\lambda_{noise}$)：
     * 较小值(如0.001)：非常缓慢的衰减
     * 中等值(如0.01)：适中衰减速度
     * 较大值(如0.1)：快速衰减
   - 双曲模式下，调整`--hyperbolic_decay_k`($k_{hyp}$)：
     * 较小值(如0.01)：非常缓慢的衰减
     * 中等值(如0.05)：适中衰减速度
     * 较大值(如0.5)：相对较快的初始衰减

4. **实现细节:**
   ```python
   def get_adaptive_noise_multiplier(self, round_idx, beta_k=1.0):
       """根据轮次和自适应因子计算噪声乘数"""
       # 选择基准噪声值
       if self.noise_base_mode == 'fixed':
           sigma_base = self.initial_max_noise
       else:  # 'dynamic'模式
           sigma_base = self.noise_scale
           
       # 根据所选衰减模式计算衰减因子
       if self.noise_decay_mode == 'exponential':
           # 指数衰减: e^(-0.5 * λ * t)
           decay_factor = np.exp(-0.5 * self.noise_decay_rate * round_idx)
       else:  # 'hyperbolic'模式
           # 双曲衰减: 1/sqrt(1 + k * t)
           decay_factor = 1.0 / np.sqrt(1.0 + self.hyperbolic_decay_k * round_idx)
           
       # 计算最终噪声乘数
       adaptive_noise = sigma_base * np.sqrt(beta_k) * decay_factor
       return adaptive_noise
   ```

5. **噪声衰减与隐私预算的关系:**
   - 噪声衰减会导致每轮RDP成本随训练进行而增加
   - 系统通过RDP Accountant严格追踪累积隐私消耗
   - 当累积RDP接近总预算时，可能会增加服务器端噪声或调整衰减策略

**H. 客户端噪声自适应因子**

自适应因子$\beta_k$是个性化本地噪声机制的核心，它根据两个关键指标调整噪声水平：

1. **本地损失$L_k$:**
   - 反映当前模型在客户端本地数据上的性能
   - 高损失表示模型与本地数据拟合度低，潜在的"低质量"更新

2. **梯度方向余弦相似度$sim_k$:**
   - 衡量本地梯度与全局方向的一致性
   - 高相似度表示客户端更新与全局方向一致，潜在的"高质量"更新

3. **计算方式:**
   - $\beta_k = \gamma \cdot (1-sim_k) + (1-\gamma) \cdot \hat{L}_k$
   - 参数$\gamma$控制两个因素的权重平衡
   - $\beta_k$较小时，表示"高质量"更新，分配较小的噪声
   - $\beta_k$较大时，表示"低质量"更新，分配较大的噪声

4. **最终噪声计算:**
   - 结合基准噪声、自适应因子和衰减因子：
     * 指数模式: $\sigma_k = \sigma_{base} \cdot \sqrt{\beta_k} \cdot e^{-0.5 \lambda_{noise} \cdot t}$
     * 双曲模式: $\sigma_k = \sigma_{base} \cdot \sqrt{\beta_k} \cdot \frac{1}{\sqrt{1 + k_{hyp} \cdot t}}$

**I. 边缘节点自适应学习率机制**

边缘节点使用自适应学习率来处理异步更新，支持两种方案：

1. **方案1 (基于Staleness范数比率):**
   - $\eta_{g,k} = \frac{\lambda_{alr}}{\gamma(k) + \varepsilon_{alr}}$
   - 基于模型版本差异与有效更新范数的比值

2. **方案2 (基于Staleness轮次差和隐私预算):**
   - $\eta_{g,k} = \eta_0 \cdot (d_k + 1)^{-a} \cdot \left(\frac{\epsilon_{rem}^t}{\epsilon_{max}}\right)^{\gamma_{priv}}$
   - 同时考虑版本落后轮数和剩余隐私预算

**J. 服务器端全局RDP修正**

为确保整体隐私保护，服务器实施全局RDP修正机制：

1. **预算分配策略:**
   - `fixed`: 每轮使用固定预算
   - `uniform`: 均匀分配总预算
   - `rdp_optimal`: 根据RDP理论优化分配

2. **敏感度计算:**
   - 根据自适应学习率和客户端更新特性
   - 支持最大值(`max`)和中位数(`median`)两种方法

3. **服务器噪声添加:**
   - 根据剩余预算和敏感度计算噪声方差
   - 对聚合更新添加适量噪声，确保满足隐私要求

**J-2. 自定义Logger与指标收集**

为全面评估系统性能和监控训练过程，本方案实现了一个专用的`myLogger`类，该类在异步联邦学习流程中扮演关键角色：

1. **系统集成与初始化**
   - 在`main.py`中通过`flgo.init(..., logger=myLogger)`方式注册
   - 初始化时自动为各种指标创建存储容器
   - 获取所有参与者信息并记录初始状态

2. **与服务器的交互**
   - 通过`self.server`接口访问服务器组件
   - 调用`self.server.test(...)`和`self.server.global_test(...)`方法获取全局和本地指标
   - 记录服务器的当前轮次(`current_round`)和时间信息

3. **全面的指标收集体系**
   - **全局指标**: 准确率、精确率、召回率、F1分数
   - **数据划分**: 每种指标在训练集/验证集/测试集上分别评估
   - **客户端级别**: 所有指标在每个客户端上单独计算

4. **自适应性设计**
   - 自动识别客户端数据量并进行加权平均
   - 根据客户端分布计算均值和标准差
   - 支持不同的评估频率设置(通过`eval_interval`参数)

5. **与差分隐私的协作**
   - 记录不同隐私预算设置下的模型性能
   - 随着噪声调整跟踪性能变化
   - 为隐私-性能权衡分析提供数据支持

与其他组件不同，`myLogger`在整个系统生命周期中持续工作，为研究人员提供所需的数据可视化和分析基础，是评估异步联邦学习方案有效性的关键工具。

**K. 最终算法流程**

1. **初始化:**
   - 设置全局预算、模型参数和超参数
   - 初始化RDP Accountant和历史模型管理器

2. **客户端训练:**
   - 下载全局模型
   - 计算个性化噪声(选择基准和衰减模式)
   - 执行DP-SGD训练
   - 上传隐私化更新和元数据

3. **边缘节点聚合:**
   - 接收异步更新
   - 计算Staleness和自适应学习率
   - 添加全局RDP噪声
   - 更新全局模型
   - 追踪隐私预算消耗

4. **循环迭代:**
   - 重复步骤2-3直到达到预定轮次或预算耗尽

5. **日志记录与分析:**
   - 通过`myLogger`类在每轮训练后自动记录多维指标
   - 对全局模型在测试、验证和训练集上的表现进行评估
   - 收集和分析每个客户端上的本地模型性能
   - 计算客户端性能的加权平均值和统计特征
   - 输出关键指标用于实时监控和后续分析

通过这种方式，系统在车联网异构环境中实现了高效、隐私保护的分布式异常检测。