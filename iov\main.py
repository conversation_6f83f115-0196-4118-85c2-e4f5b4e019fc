import os
import argparse
import torch
import numpy as np
from loguru import logger

from simulation_flgo import IOVFlgoSimulation    
from utils.utils import setup_logger

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='车联网联邦学习入侵检测')
    
    # 数据参数
    parser.add_argument('--data_path', type=str, default='data/sample_cicids.csv',
                        help='数据集路径')
    parser.add_argument('--test_size', type=float, default=0.2,
                        help='测试集比例')
    parser.add_argument('--alpha', type=float, default=0.5,
                        help='Dirichlet分布参数, 控制非独立同分布程度')
    
    # 模型参数
    parser.add_argument('--hidden_dim', type=int, default=128,
                        help='隐藏层维度')
    parser.add_argument('--local_layers', type=int, default=2,
                        help='本地特征提取器层数')
    parser.add_argument('--global_layers', type=int, default=2,
                        help='全局特征提取器层数')
    parser.add_argument('--num_heads', type=int, default=4,
                        help='注意力头数')
    parser.add_argument('--model_type', type=str, default='transformer',
                        choices=['transformer', 'cnn_transformer'],
                        help='全局特征提取器架构类型(transformer: 纯Transformer, cnn_transformer: CNN+Transformer混合)')
    
    # 训练参数
    parser.add_argument('--batch_size', type=int, default=64,
                        help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-5,
                        help='权重衰减')
    parser.add_argument('--local_epochs', type=int, default=1,
                        help='本地训练轮数')
    parser.add_argument('--num_rounds', type=int, default=50,
                        help='全局训练轮数')
    parser.add_argument('--target_accuracy', type=float, default=0.95,
                        help='目标准确率')
    
    # 联邦学习参数
    parser.add_argument('--num_vehicles', type=int, default=20,
                        help='车辆数量')
    parser.add_argument('--num_edges', type=int, default=3,
                        help='边缘服务器数量')
    parser.add_argument('--buffer_size', type=int, default=5,
                        help='缓冲区大小')
    parser.add_argument('--staleness_threshold', type=int, default=3,
                        help='过时阈值')
    parser.add_argument('--edge_aggregation_interval', type=int, default=5,
                        help='边缘服务器全局聚合间隔（轮次，仅对FedBuff生效）')
    
    # 移动参数
    parser.add_argument('--movement_ratio', type=float, default=0.2,
                        help='每次移动的车辆比例')
    parser.add_argument('--movement_interval', type=int, default=5,
                        help='移动间隔（轮次）')
    
    # 其他参数
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    parser.add_argument('--output_dir', type=str, default='output',
                        help='输出目录')
    parser.add_argument('--log_file', type=str, default='logs/simulation.log',
                        help='日志文件')
    parser.add_argument('--gpu', type=int, default=-1,
                        help='GPU ID，-1表示使用CPU')
    
    # FLGO参数
    parser.add_argument('--partition_type', type=str, default='dirichlet',
                        choices=['dirichlet', 'shards', 'iid'],
                        help='FLGO数据分区类型')
    
    # 算法选择
    parser.add_argument('--algorithm', type=str, default='fedbuff',
                        choices=['fedbuff', 'fedavg'],
                        help='联邦学习算法选择')
    
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志
    os.makedirs(os.path.dirname(args.log_file), exist_ok=True)
    setup_logger(args.log_file)
    
    # 设置设备
    if args.gpu >= 0 and torch.cuda.is_available():
        device = torch.device(f'cuda:{args.gpu}')
        logger.info(f"使用GPU: {torch.cuda.get_device_name(args.gpu)}")
    else:
        device = torch.device('cpu')
        logger.info("使用CPU")
    
    # 配置
    config = vars(args)
    
    # 创建并运行模拟
    if args.algorithm == 'fedbuff':
        logger.info(f"使用FedBuff异步聚合算法进行模拟，边缘服务器全局聚合间隔: {args.edge_aggregation_interval} 轮")
        config['algorithm_type'] = 'fedbuff'
    else:
        logger.info("使用FedAvg同步聚合算法进行模拟")
        config['algorithm_type'] = 'fedavg'
    
    # 记录模型类型
    logger.info(f"使用全局特征提取器架构: {args.model_type}")
    
    # 创建并运行模拟
    simulation = IOVFlgoSimulation(config)
    history = simulation.run_simulation()
    simulation.final_evaluation()
    
    logger.info("模拟完成")

if __name__ == "__main__":
    main()





# 使用FedBuff异步聚合 + Transformer架构
#python run.py --algorithm fedbuff --model_type transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50

# 使用FedBuff异步聚合 + CNN+Transformer混合架构
#python run.py --algorithm fedbuff --model_type cnn_transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50

# 使用FedAvg同步聚合 + Transformer架构
#python run.py --algorithm fedavg --model_type transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50

# 使用FedAvg同步聚合 + CNN+Transformer混合架构
#python run.py --algorithm fedavg --model_type cnn_transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50



