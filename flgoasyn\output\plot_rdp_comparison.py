#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
绘制多个方法的RDP消耗比较折线图，不带标记。
从pkl文件中读取不同方法的RDP数据，并绘制RDP消耗随轮次变化的折线图。
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import argparse


def load_rdp_from_pkl(file_path: str) -> List[Dict]:
    """
    从pkl文件中加载RDP数据。
    
    参数:
        file_path: pkl文件的路径
        
    返回:
        包含RDP记录的列表，每个元素是一个轮次的RDP字典
    """
    try:
        with open(file_path, 'rb') as f:
            rdp_data = pickle.load(f)
        
        # 确保返回的是列表
        if not isinstance(rdp_data, list):
            rdp_data = [rdp_data]
            
        return rdp_data
    except Exception as e:
        print(f"加载文件 {file_path} 时出错: {e}")
        return []


def extract_rdp_from_records(rdp_records: List[Dict]) -> Tuple[List[int], List[float]]:
    """
    从RDP记录列表中提取轮次和local_rdp数据。
    
    参数:
        rdp_records: 包含RDP记录的列表，每个元素是一个包含'round'和'local_rdp'的字典
        
    返回:
        轮次列表和对应的local_rdp列表的元组
    """
    rounds = []
    local_rdps = []
    
    for record in rdp_records:
        if isinstance(record, dict) and 'round' in record and 'local_rdp' in record:
            rounds.append(record['round'])
            local_rdps.append(record['local_rdp'])
    
    # 按轮次排序
    sorted_data = sorted(zip(rounds, local_rdps), key=lambda x: x[0])
    if sorted_data:
        rounds, local_rdps = zip(*sorted_data)
    else:
        rounds, local_rdps = [], []
    
    return list(rounds), list(local_rdps)


def plot_rdp_lines(
    methods_data: Dict[str, Tuple[List[int], List[float]]],
    title: str = "Methods RDP Consumption Comparison",
    xlabel: str = "Rounds",
    ylabel: str = "Local RDP Cost",
    figsize: Tuple[float, float] = (6.4, 4.8),  # matplotlib默认大小
    save_path: Optional[str] = None,
    ylim: Optional[Tuple[float, float]] = None,
    rounds_step: int = 20,
    max_rounds: int = 100,
    linewidth: float = 2.0  # 线宽
) -> None:
    """
    绘制多个方法的RDP消耗比较折线图，不带标记。
    
    参数:
        methods_data: 字典，格式为 {方法名: (轮次列表, RDP列表)}
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        figsize: 图表大小，默认为 matplotlib 的标准大小 (6.4, 4.8)
        save_path: 保存路径，如果为None则不保存
        ylim: y轴范围，格式为(ymin, ymax)
        rounds_step: 轮次标签的步长
        max_rounds: 最大轮次数
        linewidth: 线宽，默认为2.0
    """
    # 设置全局字体为 Times New Roman
    plt.rcParams['font.family'] = 'Times New Roman'
    plt.rcParams['mathtext.fontset'] = 'stix'
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    plt.figure(figsize=figsize)
    
    # 定义线型和颜色
    linestyles = ['-', '--', '-.', ':']
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    # 定义方法的显示顺序
    display_order = ['proposed', 'Fixed RDP']
    
    # 按照指定顺序对方法进行排序
    ordered_methods = []
    for method_name in display_order:
        if method_name in methods_data:
            ordered_methods.append(method_name)
    
    # 添加任何不在display_order中但在methods_data中的方法
    for method_key in methods_data.keys():
        if method_key not in ordered_methods:
            ordered_methods.append(method_key)
    
    # 绘制每个方法的折线
    for i, method_key in enumerate(ordered_methods):
        rounds, rdp_values = methods_data[method_key]
        
        # 过滤超出max_rounds的数据
        filtered_data = [(r, v) for r, v in zip(rounds, rdp_values) if r <= max_rounds]
        if filtered_data:
            filtered_rounds, filtered_rdp = zip(*filtered_data)
        else:
            filtered_rounds, filtered_rdp = [], []
        
        # 使用方法的颜色
        color = colors[i % len(colors)]
        
        # 绘制折线，不带标记
        plt.plot(filtered_rounds, filtered_rdp, 
                 label=method_key,
                 linestyle=linestyles[i % len(linestyles)],
                 color=color,
                 linewidth=linewidth)
    
    # 设置图表属性
    plt.title(title, fontsize=18, family='Times New Roman')
    plt.xlabel(xlabel, fontsize=16, family='Times New Roman')
    plt.ylabel(ylabel, fontsize=16, family='Times New Roman')
    
    # 设置x轴刻度，步长为rounds_step
    x_ticks = list(range(0, max_rounds + 1, rounds_step))
    plt.xticks(x_ticks, fontsize=14, family='Times New Roman')
    
    # 添加 x 轴子刻度
    minor_x_ticks = list(range(0, max_rounds + 1, 10))  # 子刻度增幅为10
    # 过滤掉与主刻度重叠的子刻度
    minor_x_ticks = [x for x in minor_x_ticks if x not in x_ticks]
    plt.gca().set_xticks(minor_x_ticks, minor=True)
    plt.gca().set_xticklabels([str(x) for x in minor_x_ticks], minor=True, fontsize=14, family='Times New Roman')
    plt.gca().tick_params(axis='x', which='minor', length=4, color='gray', width=1.0)
    
    # 设置x轴范围
    plt.xlim(0, max_rounds)
    
    # 设置y轴范围和刻度
    if ylim:
        plt.ylim(ylim)
        # 计算合适的y轴主刻度间隔
        y_range = ylim[1] - ylim[0]
        y_step = 0.2 if y_range <= 1.0 else (0.5 if y_range <= 5.0 else 1.0)
        y_ticks = np.arange(ylim[0], ylim[1] + 0.01, y_step)
        plt.yticks(y_ticks, fontsize=14, family='Times New Roman')
        
        # 添加 y 轴子刻度
        y_minor_step = y_step / 4
        minor_y_ticks = np.arange(ylim[0], ylim[1] + 0.01, y_minor_step)
        # 过滤掉与主刻度重叠的子刻度
        minor_y_ticks = [y for y in minor_y_ticks if not any(abs(y - main_y) < 1e-6 for main_y in y_ticks)]
        plt.gca().set_yticks(minor_y_ticks, minor=True)
        plt.gca().set_yticklabels([f"{y:.2f}" for y in minor_y_ticks], minor=True, fontsize=14, family='Times New Roman')
        plt.gca().tick_params(axis='y', which='minor', length=4, color='gray', width=1.0)
    
    # 添加网格线（横向和纵向）
    plt.grid(True, linestyle='--', alpha=0.7)
    # 添加次要网格线
    plt.grid(True, which='minor', linestyle=':', alpha=0.3)
    
    # 添加图例，放在右上角
    legend = plt.legend(fontsize=12, loc='upper right', prop={'family': 'Times New Roman'})
    legend.get_frame().set_linewidth(1.0)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存至: {save_path}")
    
    # 显示图表
    plt.show()


def check_directory_and_update_mapping(data_dir):
    """检查目录并更新映射关系"""
    if not os.path.exists(data_dir):
        print(f"目录 {data_dir} 不存在")
        return {}
    
    pkl_files = [os.path.splitext(f)[0] for f in os.listdir(data_dir) if f.endswith('.pkl')]
    
    if not pkl_files:
        print(f"在目录 {data_dir} 中未找到pkl文件")
        return {}
    
    print(f"找到以下文件: {pkl_files}")
    
    # 根据找到的文件创建映射
    custom_names = {
        "client_10_local_rdp": "proposed",
        # "client_10_local_rdp_ton": "ToNIoT"
    }
    
    # 过滤掉不存在的文件
    filtered_names = {k: v for k, v in custom_names.items() if k in pkl_files}
    
    print("使用的映射关系:")
    for key, value in filtered_names.items():
        print(f"  - {key} -> {value}")
    
    return filtered_names


def plot_from_directory(data_dir,
                       output="rdp_comparison.png",
                       title="RDP Consumption Comparison",
                       ymin=None, ymax=None,
                       rounds_step=20, max_rounds=100,
                       linewidth=2.0):
    """从指定目录读取pkl文件并绘制比较图"""
    # 获取目录中的所有pkl文件
    if not os.path.exists(data_dir):
        print(f"目录 {data_dir} 不存在")
        return
        
    pkl_files = [f for f in os.listdir(data_dir) if f.endswith('.pkl')]
    
    if not pkl_files:
        print(f"在目录 {data_dir} 中未找到pkl文件")
        return
    
    # 获取自定义名称映射
    custom_names = check_directory_and_update_mapping(data_dir)
    
    # 打印找到的文件和自定义名称映射，用于调试
    print("找到的pkl文件:")
    for file in pkl_files:
        print(f"  - {file}")
    
    if custom_names:
        print("\n自定义方法名称映射:")
        for key, value in custom_names.items():
            print(f"  - {key} -> {value}")
    
    # 读取每个方法的数据
    methods_data = {}
    all_rdp_values = []  # 用于自动确定y轴范围
    
    # 创建固定RDP值的列表（100个0.305116855762209）
    fixed_rdp_rounds = list(range(1, 101))  # 1到100的轮次
    fixed_rdp_values = [0.305116855762209] * 100  # 100个固定值
    
    # 添加固定RDP值到methods_data
    methods_data["Fixed RDP"] = (fixed_rdp_rounds, fixed_rdp_values)
    all_rdp_values.extend(fixed_rdp_values)
    print(f"添加固定RDP值数据，值为0.305116855762209，共 {len(fixed_rdp_values)} 个数据点")
    
    for file_name in pkl_files:
        file_path = os.path.join(data_dir, file_name)
        method_name = os.path.splitext(file_name)[0]  # 使用文件名作为方法名
        
        # 加载RDP数据
        rdp_records = load_rdp_from_pkl(file_path)
        
        # 提取轮次和RDP数据
        rounds, rdp_values = extract_rdp_from_records(rdp_records)
        
        if rounds and rdp_values:
            # 收集所有RDP值用于自动确定y轴范围
            all_rdp_values.extend(rdp_values)
            
            # 如果存在自定义名称映射，则使用映射后的名称作为键
            if custom_names and method_name in custom_names:
                display_name = custom_names[method_name]
                methods_data[display_name] = (rounds, rdp_values)
                print(f"成功加载 {method_name} 的数据 (显示为 {display_name})，共 {len(rounds)} 个数据点")
            else:
                methods_data[method_name] = (rounds, rdp_values)
                print(f"成功加载 {method_name} 的数据，共 {len(rounds)} 个数据点")
    
    if not methods_data:
        print("未能从pkl文件中提取到有效的RDP数据")
        return
    
    # 如果未指定y轴范围，自动确定
    if ymin is None or ymax is None:
        if all_rdp_values:
            if ymin is None:
                ymin = max(0, min(all_rdp_values) * 0.9)  # 留出10%的下边距
            if ymax is None:
                ymax = max(all_rdp_values) * 1.1  # 留出10%的上边距
    
    # 绘制比较图
    save_path = os.path.join(data_dir, output)
    plot_rdp_lines(
        methods_data=methods_data,
        title=title,
        save_path=save_path,
        ylim=(ymin, ymax) if ymin is not None and ymax is not None else None,
        rounds_step=rounds_step,
        max_rounds=max_rounds,
        linewidth=linewidth
    )


def main():
    """主函数，处理命令行参数并执行绘图"""
    parser = argparse.ArgumentParser(description='绘制多个方法的RDP消耗比较折线图，不带标记')
    parser.add_argument('--data_dir', type=str, default="C:\\Users\\<USER>\\Desktop\\result\\CICDIS17\\draw",
                        help='包含pkl文件的目录路径')
    parser.add_argument('--output', type=str, default="rdp_comparison.png",
                        help='输出图表的文件名')
    parser.add_argument('--title', type=str, default="",
                        help='图表标题')
    parser.add_argument('--ymin', type=float, default=None,
                        help='y轴最小值')
    parser.add_argument('--ymax', type=float, default=None,
                        help='y轴最大值')
    parser.add_argument('--rounds_step', type=int, default=20,
                        help='轮次标签的步长')
    parser.add_argument('--max_rounds', type=int, default=100,
                        help='最大轮次数')
    parser.add_argument('--linewidth', type=float, default=2.0,
                        help='线宽')
    
    args = parser.parse_args()
    
    # 从文件读取数据并绘图
    plot_from_directory(
        data_dir=args.data_dir,
        output=args.output,
        title=args.title,
        ymin=args.ymin,
        ymax=args.ymax,
        rounds_step=args.rounds_step,
        max_rounds=args.max_rounds,
        linewidth=args.linewidth
    )


if __name__ == "__main__":
    main() 