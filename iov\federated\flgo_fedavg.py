import torch
import copy
import numpy as np
from typing import Dict, List, Optional, Tuple
from loguru import logger

import flgo.algorithm.fedavg as fedavg
from flgo.algorithm.fedavg import Server as FedAvgServer
from flgo.algorithm.fedavg import Client

class IOVFedAvgClient(Client):
    """车联网FedAvg客户端 - 基于flgo实现"""
    
    def __init__(self, client_id: int, model: torch.nn.Module, dataloader: torch.utils.data.DataLoader,
                 optimizer: torch.optim.Optimizer, criterion: torch.nn.Module, device: str = 'cpu') -> None:
        """
        初始化车联网FedAvg客户端
        
        参数:
            client_id: 客户端ID
            model: 本地模型
            dataloader: 数据加载器
            optimizer: 优化器
            criterion: 损失函数
            device: 计算设备
        """
        self.client_id = client_id
        self.model = model
        self.dataloader = dataloader
        self.optimizer = optimizer
        self.criterion = criterion
        self.device = device
        
        # 当前轮次
        self.current_round = 0
        
        # 当前边缘服务器
        self.current_edge_server = None
        
        # 数据大小
        self.data_size = len(dataloader.dataset)
        
        logger.info(f"车辆 {client_id} 初始化完成，数据大小={self.data_size}")
    
    def connect_to_edge(self, edge_server: 'IOVFedAvgServer') -> None:
        """
        连接到边缘服务器
        
        参数:
            edge_server: 边缘服务器
        """
        if self.current_edge_server is not None:
            self.disconnect_from_edge()
            
        self.current_edge_server = edge_server
        edge_server.add_vehicle(self.client_id)
        
        # 获取全局特征提取器
        global_extractor = edge_server.global_model.global_extractor
        self.model.set_global_extractor(global_extractor)
        
        logger.info(f"车辆 {self.client_id} 连接到边缘服务器 {edge_server.edge_id}")
    
    def disconnect_from_edge(self) -> None:
        """断开与当前边缘服务器的连接"""
        if self.current_edge_server is not None:
            self.current_edge_server.remove_vehicle(self.client_id)
            logger.info(f"车辆 {self.client_id} 断开与边缘服务器 {self.current_edge_server.edge_id} 的连接")
            self.current_edge_server = None
    
    def train(self, num_epochs: int = 1) -> Tuple[Optional[float], Optional[float]]:
        """
        训练本地模型
        
        参数:
            num_epochs: 训练轮数
            
        返回:
            训练损失和准确率
        """
        if self.current_edge_server is None:
            logger.warning(f"车辆 {self.client_id} 未连接到边缘服务器，无法训练")
            return None, None
            
        self.model.train()
        total_loss = 0
        correct = 0
        total = 0
        
        for epoch in range(num_epochs):
            epoch_loss = 0
            epoch_correct = 0
            epoch_total = 0
            
            for batch_idx, (data, target) in enumerate(self.dataloader):
                data, target = data.to(self.device), target.to(self.device)
                
                if len(data.shape) == 2:
                    data = data.unsqueeze(1)
                
                self.optimizer.zero_grad()
                output, gate_weights = self.model(data)
                loss = self.criterion(output, target)
                loss.backward()
                self.optimizer.step()
                
                epoch_loss += loss.item() * data.size(0)
                pred = output.argmax(dim=1, keepdim=True)
                epoch_correct += pred.eq(target.view_as(pred)).sum().item()
                epoch_total += data.size(0)
                
                local_weight = gate_weights[:, 0].mean().item()
                global_weight = gate_weights[:, 1].mean().item()
                
                if batch_idx % 10 == 0:
                    logger.info(f"车辆 {self.client_id} 训练: 轮次 {epoch+1}/{num_epochs}, 批次 {batch_idx}/{len(self.dataloader)}, "
                               f"损失: {loss.item():.4f}, 准确率: {100. * epoch_correct / epoch_total:.2f}%, "
                               f"门控权重: 本地={local_weight:.4f}, 全局={global_weight:.4f}")
            
            total_loss += epoch_loss
            correct += epoch_correct
            total += epoch_total
            
            logger.info(f"车辆 {self.client_id} 训练: 轮次 {epoch+1}/{num_epochs} 完成, "
                       f"损失: {epoch_loss / epoch_total:.4f}, 准确率: {100. * epoch_correct / epoch_total:.2f}%")
        
        self.current_round += 1
        return total_loss / total, correct / total
    
    def evaluate(self, test_dataloader: torch.utils.data.DataLoader) -> Tuple[float, float]:
        """
        评估模型
        
        参数:
            test_dataloader: 测试数据加载器
            
        返回:
            测试损失和准确率
        """
        self.model.eval()
        test_loss = 0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in test_dataloader:
                data, target = data.to(self.device), target.to(self.device)
                
                if len(data.shape) == 2:
                    data = data.unsqueeze(1)
                
                output, _ = self.model(data)
                test_loss += self.criterion(output, target).item() * data.size(0)
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += data.size(0)
        
        test_loss /= total
        accuracy = correct / total
        
        logger.info(f"车辆 {self.client_id} 评估: 损失: {test_loss:.4f}, 准确率: {100. * accuracy:.2f}%")
        
        return test_loss, accuracy
    
    def get_model_update(self) -> Dict[str, torch.Tensor]:
        """
        获取模型更新
        
        返回:
            模型更新 (OrderedDict)
        """
        model_update = {}
        
        for name, param in self.model.named_parameters():
            if 'global_extractor' in name and param.requires_grad:
                model_update[name] = param.data.clone().to('cpu')
                
        return model_update
    
    def upload_model(self) -> bool:
        """
        上传模型到边缘服务器
        
        返回:
            是否触发聚合
        """
        if self.current_edge_server is None:
            logger.warning(f"车辆 {self.client_id} 未连接到边缘服务器，无法上传模型")
            return False
            
        model_update = self.get_model_update()
        
        if not self.current_edge_server.should_update(self.client_id, model_update):
            logger.info(f"车辆 {self.client_id} 的更新被拒绝，不进行上传")
            return False
            
        triggered = self.current_edge_server.add_client_update(
            self.client_id, model_update, self.current_round, self.data_size
        )
        
        logger.info(f"车辆 {self.client_id} 上传模型到边缘服务器 {self.current_edge_server.edge_id}")
        
        return triggered

class IOVFedAvgServer(FedAvgServer):
    """边缘服务器 - 管理一组车辆客户端，使用同步FedAvg实现"""
    
    def __init__(self, global_model: torch.nn.Module, device: str = 'cpu', edge_id: Optional[int] = None) -> None:
        """
        初始化边缘服务器
        
        参数:
            global_model: 全局模型
            device: 计算设备
            edge_id: 边缘服务器ID
        """
        self.global_model = global_model
        self.device = device
        self.edge_id = edge_id
        
        # 当前全局轮次
        self.current_round = 0
        
        # 记录每个客户端的最后一次更新轮次
        self.client_last_round = {}
        
        # 记录每个客户端的数据量
        self.client_data_sizes = {}
        
        # 管理的车辆集合
        self.vehicles = set()
        
        # 等待更新的客户端集合
        self.waiting_clients = set()
        
        logger.info(f"边缘服务器 {edge_id} 初始化完成")
    
    def add_vehicle(self, vehicle_id: int) -> None:
        """添加车辆到边缘服务器"""
        self.vehicles.add(vehicle_id)
        logger.info(f"边缘服务器 {self.edge_id} 添加车辆 {vehicle_id}，当前管理 {len(self.vehicles)} 辆车")
    
    def remove_vehicle(self, vehicle_id: int) -> None:
        """从边缘服务器移除车辆"""
        if vehicle_id in self.vehicles:
            self.vehicles.remove(vehicle_id)
            logger.info(f"边缘服务器 {self.edge_id} 移除车辆 {vehicle_id}，当前管理 {len(self.vehicles)} 辆车")
    
    def get_managed_vehicles(self) -> List[int]:
        """获取当前管理的车辆列表"""
        return list(self.vehicles)
    
    def add_client_update(self, client_id: int, model_update: Dict[str, torch.Tensor],
                         client_round: int, data_size: int) -> bool:
        """
        添加客户端更新
        
        参数:
            client_id: 客户端ID
            model_update: 模型更新
            client_round: 客户端当前轮次
            data_size: 客户端数据大小
            
        返回:
            是否触发聚合
        """
        # 记录客户端的最后一次更新轮次
        self.client_last_round[client_id] = client_round
        
        # 记录客户端的数据量
        self.client_data_sizes[client_id] = data_size
        
        # 将客户端添加到等待集合
        self.waiting_clients.add(client_id)
        
        logger.info(f"边缘服务器 {self.edge_id} 收到客户端 {client_id} 的更新")
        
        # 检查是否所有客户端都已上传
        if len(self.waiting_clients) == len(self.vehicles):
            logger.info(f"边缘服务器 {self.edge_id} 收到所有客户端的更新，开始聚合")
            self.aggregate()
            return True
            
        return False
    
    def aggregate(self) -> None:
        """聚合所有客户端的更新"""
        if not self.waiting_clients:
            logger.warning("没有等待聚合的客户端更新")
            return
            
        # 计算权重
        total_data_size = sum(self.client_data_sizes[client_id] for client_id in self.waiting_clients)
        weights = {client_id: self.client_data_sizes[client_id] / total_data_size 
                  for client_id in self.waiting_clients}
        
        # 聚合更新
        aggregated_update = {}
        
        # 初始化聚合更新
        for name, param in self.global_model.named_parameters():
            if param.requires_grad:
                aggregated_update[name] = torch.zeros_like(param.data)
        
        # 加权聚合
        for client_id in self.waiting_clients:
            weight = weights[client_id]
            for name, param in self.global_model.named_parameters():
                if name in aggregated_update and param.requires_grad:
                    aggregated_update[name] += weight * param.data.to(self.device)
        
        # 更新全局模型
        with torch.no_grad():
            for name, param in self.global_model.named_parameters():
                if name in aggregated_update and param.requires_grad:
                    param.data = aggregated_update[name]
        
        # 清空等待集合
        self.waiting_clients.clear()
        
        # 更新全局轮次
        self.current_round += 1
        
        logger.info(f"边缘服务器 {self.edge_id} 完成第 {self.current_round} 轮聚合")
    
    def should_update(self, client_id: int, model_update: Dict[str, torch.Tensor],
                     similarity_threshold: float = 0.9) -> bool:
        """
        判断客户端的更新是否应该被接受
        
        参数:
            client_id: 客户端ID
            model_update: 模型更新
            similarity_threshold: 相似度阈值
            
        返回:
            是否应该接受更新
        """
        if client_id not in self.client_last_round:
            return True
            
        similarity = self._compute_update_similarity(model_update)
        
        if similarity < similarity_threshold:
            logger.info(f"客户端 {client_id} 的更新与全局模型相似度为 {similarity:.4f}，接受更新")
            return True
        else:
            logger.info(f"客户端 {client_id} 的更新与全局模型相似度为 {similarity:.4f}，拒绝更新")
            return False
    
    def _compute_update_similarity(self, model_update: Dict[str, torch.Tensor]) -> float:
        """
        计算更新与全局模型的相似度
        
        参数:
            model_update: 模型更新
            
        返回:
            相似度 (0-1)
        """
        dot_product = 0
        global_norm = 0
        update_norm = 0
        
        for name, param in self.global_model.named_parameters():
            if name in model_update and param.requires_grad:
                global_param = param.data.view(-1)
                update_param = model_update[name].view(-1)
                
                dot_product += torch.sum(global_param * update_param).item()
                global_norm += torch.sum(global_param ** 2).item()
                update_norm += torch.sum(update_param ** 2).item()
        
        if global_norm == 0 or update_norm == 0:
            return 0
            
        similarity = dot_product / (np.sqrt(global_norm) * np.sqrt(update_norm))
        return abs(similarity)

class IOVCloudServer:
    """云服务器 - 管理多个边缘服务器"""
    
    def __init__(self, global_model: torch.nn.Module, device: str = 'cpu') -> None:
        """
        初始化云服务器
        
        参数:
            global_model: 全局模型
            device: 计算设备
        """
        self.global_model = global_model
        self.device = device
        self.edge_servers = {}  # 边缘服务器字典
        self.current_round = 0
        
    def add_edge_server(self, edge_id: int, edge_server: IOVFedAvgServer) -> None:
        """
        添加边缘服务器
        
        参数:
            edge_id: 边缘服务器ID
            edge_server: 边缘服务器实例
        """
        self.edge_servers[edge_id] = edge_server
        logger.info(f"云服务器添加边缘服务器 {edge_id}，当前管理 {len(self.edge_servers)} 个边缘服务器")
    
    def aggregate_edge_models(self) -> None:
        """聚合所有边缘服务器的模型"""
        if not self.edge_servers:
            logger.warning("没有边缘服务器，无法聚合")
            return
            
        # 获取每个边缘服务器管理的车辆数量作为权重
        edge_weights = {}
        total_vehicles = 0
        
        for edge_id, edge_server in self.edge_servers.items():
            num_vehicles = len(edge_server.get_managed_vehicles())
            edge_weights[edge_id] = num_vehicles
            total_vehicles += num_vehicles
            
        # 归一化权重
        if total_vehicles > 0:
            for edge_id in edge_weights:
                edge_weights[edge_id] /= total_vehicles
        else:
            # 如果没有车辆，使用均匀权重
            for edge_id in edge_weights:
                edge_weights[edge_id] = 1.0 / len(self.edge_servers)
                
        # 聚合模型
        aggregated_model = {}
        
        # 初始化聚合模型
        for name, param in self.global_model.named_parameters():
            if param.requires_grad:
                aggregated_model[name] = torch.zeros_like(param.data)
                
        # 加权聚合
        for edge_id, edge_server in self.edge_servers.items():
            weight = edge_weights[edge_id]
            for name, param in edge_server.global_model.named_parameters():
                if name in aggregated_model and param.requires_grad:
                    aggregated_model[name] += weight * param.data.to(self.device)
        
        # 更新全局模型
        with torch.no_grad():
            for name, param in self.global_model.named_parameters():
                if name in aggregated_model and param.requires_grad:
                    param.data = aggregated_model[name]
        
        # 更新全局轮次
        self.current_round += 1
        
        logger.info(f"云服务器完成第 {self.current_round} 轮聚合，聚合了 {len(self.edge_servers)} 个边缘服务器的模型")
        
        # 将全局模型分发到各边缘服务器
        self.distribute_global_model()
    
    def distribute_global_model(self) -> None:
        """将全局模型分发到各边缘服务器"""
        for edge_id, edge_server in self.edge_servers.items():
            # 只更新全局特征提取器部分
            for name, param in self.global_model.named_parameters():
                if 'global_extractor' in name and param.requires_grad:
                    with torch.no_grad():
                        edge_server.global_model.get_parameter(name).data = param.data.clone()
            
            logger.info(f"将全局模型分发到边缘服务器 {edge_id}")
    
    def get_global_model(self) -> torch.nn.Module:
        """
        获取全局模型
        
        返回:
            全局模型的深拷贝
        """
        return copy.deepcopy(self.global_model) 