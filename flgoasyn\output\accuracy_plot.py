"""
绘图工具模块，用于绘制实验结果图表。
包括柱状图、混淆矩阵图和折线图等可视化功能。
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Union, Optional
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
import matplotlib.ticker as ticker
import matplotlib

# 设置全局字体为 Times
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['mathtext.fontset'] = 'stix'  # 数学字体设置为与 Times 兼容的字体
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# # 设置字体，仅为中文设置特殊字体，英文使用默认字体
# matplotlib.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
# # 仅当需要显示中文时才设置中文字体
# def set_chinese_font():
#     try:
#         plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
#     except:
#         print("警告: 无法设置中文字体，图表中的中文可能无法正确显示")

# 设置一组美观的颜色
COLORS = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']

# 设置一组柔和的颜色，适合柱状图
SOFT_COLORS = [
    '#8ecae6',  # 柔和的蓝色
    '#a8dadc',  # 淡青色
    '#ffb4a2',  # 淡珊瑚色
    '#b5e48c',  # 淡绿色
    '#d8e2dc',  # 淡灰绿色
    '#e9c46a',  # 淡黄色
    '#cdb4db',  # 淡紫色
    '#f4a261',  # 淡橙色
    '#b8c0ff',  # 淡蓝紫色
    '#caf0f8'   # 极淡的蓝色
]

def plot_bar_chart(
    data: Dict[str, Dict[str, float]],
    title: str = "",
    xlabel: str = "",
    ylabel: str = "Test Accuracy",
    figsize: Tuple[float, float] = (6.4, 4.8),
    save_path: Optional[str] = None,
    show_values: bool = True,
    ylim: Optional[Tuple[float, float]] = (0.3, 0.9),
    use_soft_colors: bool = False,
    bar_width_ratio: float = 0.8,
    group_spacing: float = 1.6,
    legend_alpha: float = 1.0 # 添加图例透明度参数
) -> None:
    """
    绘制分组柱状图，比较不同条件下多种方法的性能。
    
    参数:
        data: 嵌套字典，格式为 {条件: {方法: 值}}
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        figsize: 图表大小，默认为 matplotlib 的标准大小 (6.4, 4.8)
        save_path: 保存路径，如果为None则不保存
        show_values: 是否在柱子上方显示数值
        ylim: y轴范围，格式为(ymin, ymax)，默认为(0.0, 1.0)
        use_soft_colors: 是否使用柔和的颜色方案，默认为True
        bar_width_ratio: 柱状图宽度比例，默认为0.6，范围(0,1)
        group_spacing: 组间距，默认为0.8，较小的值使组靠近中间，范围(0,2)
        legend_alpha: 图例的透明度，范围(0,1)，默认为0.8
    
    示例:
        data = {
            "No DP": {"Method 1": 0.852, "Method 2": 0.876, "Method 3": 0.821, "Method 4": 0.893},
            "With DP": {"Method 1": 0.785, "Method 2": 0.812, "Method 3": 0.798, "Method 4": 0.847}
        }
        plot_bar_chart(data, title="Comparison of Different Methods")
    """
    # 创建图表
    plt.figure(figsize=figsize)
    
    # 获取条件和方法
    conditions = list(data.keys())
    methods = list(data[conditions[0]].keys())
    num_conditions = len(conditions)
    num_methods = len(methods)
    
    # 验证并限制柱状图宽度比例
    bar_width_ratio = max(0.1, min(0.95, bar_width_ratio))
    
    # 验证并限制组间距
    group_spacing = max(0.1, min(2.0, group_spacing))
    
    # 验证并限制图例透明度
    legend_alpha = max(0.0, min(1.0, legend_alpha))
    
    # 设置柱子宽度和位置
    bar_width = bar_width_ratio / num_methods
    
    # 使用组间距调整索引位置，较小的组间距使组更靠近中间
    if num_conditions > 1:
        indices = np.arange(0, num_conditions * group_spacing, group_spacing)
    else:
        indices = np.array([0])
    
    # 选择颜色方案
    color_palette = SOFT_COLORS if use_soft_colors else COLORS
    
    # 绘制柱状图
    for i, method in enumerate(methods):
        values = [data[condition][method] for condition in conditions]
        positions = indices + (i - num_methods/2 + 0.5) * bar_width
        bars = plt.bar(positions, values, bar_width, label=method, color=color_palette[i % len(color_palette)])
        
        # 在柱子上方显示数值
        if show_values:
            for bar, value in zip(bars, values):
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{value:.3f}', ha='center', va='bottom', fontsize=9, 
                        family='Times New Roman')
    
    # 设置图表属性
    plt.xlabel(xlabel, fontsize=16, family='Times New Roman')
    plt.ylabel(ylabel, fontsize=16, family='Times New Roman')
    plt.title(title, fontsize=18, family='Times New Roman')
    plt.xticks(indices, conditions, fontsize=16, family='Times New Roman')
    
    # 设置y轴范围和刻度
    if ylim:
        plt.ylim(ylim)
        ax = plt.gca()
        
        # 设置y轴刻度，以0.1为增幅
        ticks = np.arange(ylim[0], ylim[1] + 0.01, 0.1)
        ax.set_yticks(ticks)
        ax.set_yticklabels([f'{tick:.1f}' for tick in ticks], fontsize=14, family='Times New Roman')
        
        # 为所有刻度添加网格线
        ax.grid(which='major', axis='y', linestyle='--', alpha=0.7)
        
        # 计算图例位置，确保在图表内部
        # 将图例放在图表内部上方区域
        legend_y = ylim[1] + 0.1#0.0005 # 从顶部向下偏移一点
    else:
        # 如果没有设置ylim，则使用默认的刻度设置
        ax = plt.gca()
        ax.grid(axis='y', linestyle='--', alpha=0.7)
        legend_y = 0.95  # 默认位置
    
    # 将图例放在图表内部上方，并设置透明度和黑色边框
    # 根据方法数量决定图例的列数，避免过宽
    ncols = min(len(methods), 4)  # 最多4列，避免过宽
    
    # 创建图例
    legend = plt.legend(fontsize=7, loc='upper center', 
                       bbox_to_anchor=(0.5, legend_y),
                       ncol=ncols, prop={'family': 'Times New Roman'},
                       framealpha=legend_alpha,
                       shadow=False,
                       fancybox=False)
    
    # # 设置图例框为黑色直角框
    # frame = legend.get_frame()
    # frame.set_edgecolor('black')      # 设置边框颜色为黑色
    # frame.set_linewidth(1.0)          # 设置边框线宽
    # frame.set_boxstyle('square') 
    # # frame.set_facecolor('white')      # 设置背景色为白色
    

    
    # 调整x轴范围，使图表居中
    if len(indices) > 0:
        x_min = min(indices) - bar_width * num_methods
        x_max = max(indices) + bar_width * num_methods
        plt.xlim(x_min, x_max)
    
    # 添加背景网格
    plt.gca().set_axisbelow(True)
    
    # 保存图表
    if save_path:
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {save_path}")
    
    # 显示图表
    plt.tight_layout()
    plt.show()


def plot_confusion_matrix(
    conf_matrix: np.ndarray,
    class_names: List[str] = None,
    title: str = "Confusion Matrix",
    figsize: Tuple[float, float] = (6.4, 4.8),
    cmap: str = "Blues",
    normalize: bool = True,
    save_path: Optional[str] = None
) -> None:
    """
    绘制混淆矩阵图。
    
    参数:
        conf_matrix: 混淆矩阵数组
        class_names: 类别名称列表
        title: 图表标题
        figsize: 图表大小，默认为 matplotlib 的标准大小 (6.4, 4.8)
        cmap: 颜色映射
        normalize: 是否将混淆矩阵归一化为比例
        save_path: 保存路径，如果为None则不保存
    
    示例:
        conf_matrix = np.array([
            [120, 5, 2, 3],
            [8, 130, 4, 2],
            [5, 7, 125, 3],
            [6, 3, 8, 135]
        ])
        class_names = ['Method 1', 'Method 2', 'Method 3', 'Method 4']
        plot_confusion_matrix(conf_matrix, class_names)
    """
    # 创建图表
    plt.figure(figsize=figsize)
    
    # 如果没有提供类别名称，则使用索引
    if class_names is None:
        class_names = [str(i) for i in range(conf_matrix.shape[0])]
    
    # 归一化混淆矩阵
    if normalize:
        conf_matrix_norm = conf_matrix.astype('float') / conf_matrix.sum(axis=1)[:, np.newaxis]
        conf_matrix_display = conf_matrix_norm
        fmt = '.2f'
    else:
        conf_matrix_display = conf_matrix
        fmt = 'd'
    
    # 使用seaborn绘制热图
    ax = sns.heatmap(conf_matrix_display, annot=True, fmt=fmt, cmap=cmap,
                xticklabels=class_names, yticklabels=class_names, cbar=True)
    
    # 设置图表属性
    plt.title(title, fontsize=18, family='Times New Roman')
    plt.xlabel('Predicted Label', fontsize=16, family='Times New Roman')
    plt.ylabel('True Label', fontsize=16, family='Times New Roman')
    
    # 旋转x轴和y轴标签25度
    plt.xticks(fontsize=10, family='Times New Roman', rotation=25, ha='right')
    plt.yticks(fontsize=10, family='Times New Roman', rotation=25, va='top')
    
    # 设置图例框为直角黑框
    cbar = ax.collections[0].colorbar
    cbar.outline.set_edgecolor('black')
    cbar.outline.set_linewidth(1.0)
    
    # 保存图表
    if save_path:
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {save_path}")
    
    # 显示图表
    plt.tight_layout()
    plt.show()


def plot_line_chart(
    data: Dict[str, List[float]],
    x_values: List[int] = None,
    title: str = "Accuracy vs. Rounds",
    xlabel: str = "Rounds",
    ylabel: str = "Accuracy",
    figsize: Tuple[float, float] = (6.4, 4.8),
    markers: List[str] = None,
    linestyles: List[str] = None,
    save_path: Optional[str] = None,
    ylim: Optional[Tuple[float, float]] = (0.0, 1.0),
    grid: bool = True,
    legend_loc: str = 'lower right',
    start_from_zero: bool = True
) -> None:
    """
    绘制折线图，展示多种方法的性能随轮次的变化。
    
    参数:
        data: 字典，格式为 {方法: [值列表]}，值范围为0到1
        x_values: x轴值，如果为None则使用0到n-1的序列
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        figsize: 图表大小，默认为 matplotlib 的标准大小 (6.4, 4.8)
        markers: 标记样式列表
        linestyles: 线条样式列表
        save_path: 保存路径，如果为None则不保存
        ylim: y轴范围，格式为(ymin, ymax)，默认为(0.0, 1.0)
        grid: 是否显示网格
        legend_loc: 图例位置
        start_from_zero: 是否从第0轮开始（准确率为0），默认为True
    
    示例:
        data = {
            "Method 1": [0.652, 0.705, 0.758, 0.782, 0.801, 0.815, 0.823, 0.830, 0.835, 0.840],
            "Method 2": [0.683, 0.721, 0.765, 0.798, 0.823, 0.841, 0.856, 0.862, 0.868, 0.872],
            "Method 3": [0.628, 0.684, 0.729, 0.761, 0.785, 0.802, 0.817, 0.825, 0.831, 0.838],
            "Method 4": [0.701, 0.745, 0.789, 0.823, 0.847, 0.865, 0.878, 0.885, 0.891, 0.895]
        }
        plot_line_chart(data)
    """
    # 创建图表
    plt.figure(figsize=figsize)
    
    # 获取方法
    methods = list(data.keys())
    
    # 如果没有提供x轴值，则使用从0开始的索引
    if x_values is None:
        # 使用第一个方法的数据长度作为参考
        x_values = list(range(0, len(data[methods[0]])))
        if start_from_zero:
            # 添加第0轮
            x_values = [0] + x_values
    
    # 如果没有提供标记样式，则使用默认值
    if markers is None:
        markers = ['o', 's', '^', 'D', 'v', '>', '<', 'p', '*', 'h']
    
    # 如果没有提供线条样式，则使用默认值
    if linestyles is None:
        linestyles = ['-', '--', '-.', ':']
    
    # 绘制折线图
    for i, method in enumerate(methods):
        plot_data = data[method]
        plot_x = x_values
        
        # 如果需要从第0轮开始，且x_values不包含0，则在数据前添加0
        if start_from_zero and 0 not in x_values and len(x_values) > 0 and x_values[0] > 0:
            plot_data = [0.0] + plot_data
            plot_x = [0] + list(x_values)
        elif start_from_zero and len(x_values) > 0 and x_values[0] == 0 and len(plot_data) < len(x_values):
            plot_data = [0.0] + plot_data
        
        plt.plot(plot_x, plot_data, 
                 marker=markers[i % len(markers)], 
                 linestyle=linestyles[i % len(linestyles)],
                 color=COLORS[i % len(COLORS)],
                 linewidth=2,
                 markersize=8,
                 label=method)
    
    # 设置图表属性
    plt.xlabel(xlabel, fontsize=12, family='Times New Roman')
    plt.ylabel(ylabel, fontsize=12, family='Times New Roman')
    plt.title(title, fontsize=14, family='Times New Roman')
    
    # 设置网格
    if grid:
        plt.grid(linestyle='--', alpha=0.7)
    
    # 设置y轴范围和刻度
    if ylim:
        plt.ylim(ylim)
        # 设置y轴主刻度和次刻度
        major_ticks = np.arange(ylim[0], ylim[1] + 0.01, 0.2)
        minor_ticks = np.arange(ylim[0], ylim[1] + 0.01, 0.1)
        plt.yticks(major_ticks, fontsize=16, family='Times New Roman')
        ax = plt.gca()
        ax.set_yticks(minor_ticks, minor=True)
        ax.tick_params(which='minor', length=4, color='gray', width=1.0)
        
        # 为主刻度和次刻度添加网格线
        ax.grid(which='major', axis='y', linestyle='--', alpha=0.7)
        ax.grid(which='minor', axis='y', linestyle=':', alpha=0.4)
    
    # 设置x轴范围，从0开始顶头
    if len(x_values) > 0:
        plt.xlim(0, max(x_values))
    
    # 添加图例
    plt.legend(fontsize=16, loc=legend_loc, prop={'family': 'Times New Roman'})
    
    # 设置x轴刻度
    if len(x_values) > 10:
        # 如果x轴值过多，则只显示部分刻度
        step = max(1, len(x_values) // 10)
        plt.xticks(x_values[::step], fontsize=16, family='Times New Roman')
    else:
        plt.xticks(x_values, fontsize=16, family='Times New Roman')
    
    # 保存图表
    if save_path:
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {save_path}")
    
    # 显示图表
    plt.tight_layout()
    plt.show()



import numpy as np
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

def metrics_from_confusion_sklearn(cm):
    """
    输入:
      cm: 混淆矩阵 (C x C) 的 np.ndarray 或嵌套列表，
          cm[i, j] = 真实为 i 被预测为 j 的样本数
    返回:
      accuracy, precision_micro, recall_micro, f1_micro
    """
    cm = np.asarray(cm, dtype=int)
    n_classes = cm.shape[0]

    # 1) 把混淆矩阵展开成 y_true, y_pred
    y_true = []
    y_pred = []
    for i in range(n_classes):
        for j in range(n_classes):
            count = cm[i, j]
            if count:
                y_true.extend([i] * count)
                y_pred.extend([j] * count)
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)

    # 2) 调用 sklearn 计算
    acc = accuracy_score(y_true, y_pred)
    p_micro = precision_score(y_true, y_pred, average='weighted', zero_division=0)
    r_micro = recall_score(y_true, y_pred, average='weighted', zero_division=0)
    f1_micro = f1_score(y_true, y_pred, average='weighted', zero_division=0)

    return acc, p_micro, r_micro, f1_micro

# 示例用法
if __name__ == "__main__":
    # 示例1：柱状图
    # bar_data = {
    #     "No DP": {"CNN": 0.9158, "Multi-Head": 0.8752, "Multi-Scale": 0.9150, "ConvTransformer": 0.9220},
    #     "With DP": {"CNN": 0.8615, "Multi-Head": 0.8044, "Multi-Scale": 0.8658, "ConvTransformer": 0.8696}
    # }
    bar_data = {
        "No DP": {"CNN": 0.7761, "Multi-Head": 0.7533, "Multi-Scale": 0.7678, "ConvTransformer": 0.7811},
        "With DP": {"CNN": 0.7611, "Multi-Head": 0.7488, "Multi-Scale": 0.7601, "ConvTransformer": 0.7702}
    }
    # plot_bar_chart(bar_data, title="", save_path="bar_chart_example.png")
    
    # # 示例2：混淆矩阵
    # conf_matrix = np.array([
    #     [4403, 10, 8, 23, 17, 0, 4, 23, 9, 3], [2, 1759, 1, 0, 3, 0, 3, 12, 16, 4], [16, 0, 1362, 19, 117, 0, 23, 22, 189, 52], [28, 0, 18, 1695, 44, 1, 1, 4, 0, 9], [6, 0, 179, 27, 1153, 0, 368, 10, 12, 45], [7, 0, 1, 23, 43, 0, 3, 4, 3, 10], [4, 7, 12, 13, 921, 0, 492, 212, 126, 13], [19, 247, 23, 1, 0, 0, 4, 1429, 25, 52], [0, 13, 130, 7, 7, 0, 43, 175, 1421, 4], [7, 1, 355, 59, 329, 0, 77, 165, 83, 724]
    # ]) # 有DP

    # conf_matrix = np.array([[4311, 29, 14, 19, 13, 0, 41, 10, 55, 8], [1, 1765, 3, 2, 2, 0, 11, 1, 7, 8],
    #  [21, 22, 1297, 9, 53, 1, 62, 6, 235, 94], [19, 5, 11, 1659, 24, 15, 4, 7, 31, 25],
    #  [3, 13, 102, 14, 890, 1, 603, 0, 43, 131], [10, 0, 0, 27, 12, 11, 2, 3, 3, 26],
    #  [3, 48, 11, 13, 563, 1, 951, 27, 167, 16], [12, 266, 11, 15, 1, 1, 17, 1219, 146, 112],
    #  [0, 67, 119, 3, 6, 0, 62, 23, 1512, 8], [5, 13, 75, 31, 226, 2, 49, 28, 149, 1222]]
    # ) # 无DP
    # class_names =['Normal', 'Backdoor', 'DDoS', 'DoS', 'Injection', 'Mitm', 'Password', 'Ransomware', 'Scanning', 'XSS' ] #['Normal', 'DDoS', 'DoS', 'Heartbleed', 'Bot', 'Brute', 'Web', 'Infiltration' ]



    # conf_matrix = np.array([
    #     [4903, 106, 122, 136, 76, 1, 3, 49], [228, 357, 0, 2, 0, 0, 0, 0], [86, 0, 494, 0, 0, 0, 0, 20], [37, 0, 0, 527, 36, 0, 0, 0], [61, 0, 6, 33, 497, 0, 0, 2], [1, 0, 0, 0, 0, 2, 0, 0], [9, 0, 0, 0, 0, 0, 2, 0], [43, 0, 31, 0, 7, 0, 0, 519]
    # ]) # 有DP
    # conf_matrix = np.array(
    #     [[5063, 78, 101, 66, 51, 0, 0, 37], [141, 441, 5, 0, 0, 0, 0, 0], [12, 0, 583, 0, 0, 0, 0, 5],
    #      [17, 0, 0, 565, 18, 0, 0, 0], [45, 0, 3, 5, 545, 0, 0, 1], [0, 0, 0, 0, 0, 2, 1, 0], [9, 0, 0, 0, 0, 1, 1, 0],
    #      [18, 0, 35, 0, 6, 0, 0, 541]]
    # ) # 无DP
    #
    # class_names =['Normal', 'Bot',  'Brute', 'DDoS',  'DoS', 'Heartbleed',  'Infiltration',  'Web']





    #Car
    conf_matrix = np.array([[2393, 8, 264, 259, 76], [0, 2854, 0, 0, 0], [81, 1, 1451, 20, 5], [528, 3, 105, 88, 26], [44, 2, 3, 5, 549]]
    ) # 无DP
    class_names =['Normal', 'Flooding', 'Fuzzing', 'Replay', 'Spoofing'] 






    plot_confusion_matrix(conf_matrix, class_names, save_path="confusion_matrix_example.png")
    #
    # m1 = np.array([[5063, 78, 101, 66, 51, 0, 0, 37], [141, 441, 5, 0, 0, 0, 0, 0], [12, 0, 583, 0, 0, 0, 0, 5], [17, 0, 0, 565, 18, 0, 0, 0], [45, 0, 3, 5, 545, 0, 0, 1], [0, 0, 0, 0, 0, 2, 1, 0], [9, 0, 0, 0, 0, 1, 1, 0], [18, 0, 35, 0, 6, 0, 0, 541]])
    #
    # acc, p_micro, r_micro, f1_micro = metrics_from_confusion_sklearn(m1)
    # print(f"Accuracy:        {acc:.4f}")
    # print(f"Micro Precision: {p_micro:.4f}")
    # print(f"Micro Recall:    {r_micro:.4f}")
    # print(f"Micro F1:        {f1_micro:.4f}")
    
    # 示例3：折线图
    # line_data = {
    #     "Method 1": [0.652, 0.705, 0.758, 0.782, 0.801, 0.815, 0.823, 0.830, 0.835, 0.840],
    #     "Method 2": [0.683, 0.721, 0.765, 0.798, 0.823, 0.841, 0.856, 0.862, 0.868, 0.872],
    #     "Method 3": [0.628, 0.684, 0.729, 0.761, 0.785, 0.802, 0.817, 0.825, 0.831, 0.838],
    #     "Method 4": [0.701, 0.745, 0.789, 0.823, 0.847, 0.865, 0.878, 0.885, 0.891, 0.895]
    # }
    # # 使用从1开始的轮次，但图表从0开始显示（第0轮准确率为0）
    # rounds = list(range(1, 11))
    # plot_line_chart(line_data, x_values=rounds, save_path="line_chart_example.png")



    # Normal -> 0
    # Flooding -> 1
    # Fuzzing -> 2
    # Replay -> 3
    # Spoofing -> 4
