"""
稀疏多头注意力模块，实现Top-K稀疏注意力机制。
"""
from typing import Optional, Tuple

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class SparseMultiheadAttention(nn.Module):
    """稀疏多头注意力模块，实现Top-K稀疏注意力机制。
    
    通过只保留注意力权重中的Top-K个值，实现稀疏注意力，
    降低计算复杂度并提高模型对关键特征的关注度。
    
    Attributes:
        embed_dim (int): 输入嵌入维度
        num_heads (int): 注意力头数
        dropout (float): Dropout概率
        head_dim (int): 每个注意力头的维度
        scaling (float): 缩放因子，用于缩放注意力分数
        k_ratio (float): Top-K筛选比例，范围[0,1]
        q_proj (nn.Linear): 查询投影层
        k_proj (nn.Linear): 键投影层
        v_proj (nn.Linear): 值投影层
        out_proj (nn.Linear): 输出投影层
    """
    
    def __init__(
        self, 
        embed_dim: int, 
        num_heads: int, 
        dropout: float = 0.0, 
        k_ratio: float = 0.4
    ) -> None:
        """初始化稀疏多头注意力模块。
        
        Args:
            embed_dim: 输入嵌入维度
            num_heads: 注意力头数
            dropout: Dropout概率
            k_ratio: Top-K筛选比例，范围[0,1]
        """
        super(SparseMultiheadAttention, self).__init__()
        
        assert embed_dim % num_heads == 0, "embed_dim必须能被num_heads整除"
        assert 0.0 <= k_ratio <= 1.0, "k_ratio必须在0到1之间"
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.dropout = dropout
        self.head_dim = embed_dim // num_heads
        self.scaling = self.head_dim ** -0.5
        self.k_ratio = k_ratio
        
        # 投影层
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        
    def _sparse_softmax(
        self, 
        attn_scores: torch.Tensor, 
        attention_mask: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """执行稀疏Softmax操作，仅保留Top-K的注意力分数。
        
        Args:
            attn_scores: 注意力分数，形状为 [batch_size, num_heads, seq_len, seq_len]
            attention_mask: 注意力掩码，形状为 [batch_size, 1, 1, seq_len] 或 [batch_size, 1, seq_len, seq_len]
            
        Returns:
            torch.Tensor: 稀疏化后的注意力权重，形状与输入相同
        """
        # 应用掩码（如果提供）
        if attention_mask is not None:
            attn_scores = attn_scores + attention_mask
        
        # 如果k_ratio小于等于0或等于1，则不进行稀疏化，直接应用标准softmax
        if self.k_ratio <= 0 or self.k_ratio >= 1.0:
            if self.training:  # 只在训练模式下打印警告
                if self.k_ratio <= 0:
                    # print(f"警告: k_ratio={self.k_ratio} <= 0，使用标准注意力机制而非稀疏注意力")
                    pass
                else:  # self.k_ratio >= 1.0
                    # print(f"警告: k_ratio={self.k_ratio} >= 1.0，使用标准注意力机制而非稀疏注意力")
                    pass
            
            attn_probs = F.softmax(attn_scores, dim=-1)
            attn_probs = F.dropout(attn_probs, p=self.dropout, training=self.training)
            return attn_probs
        
        # 获取序列长度
        seq_len = attn_scores.size(-1)
        
        # 计算每行保留的Top-K数量，确保至少为1
        k = max(1, int(seq_len * self.k_ratio))
        
        # 创建掩码以保留每行的Top-K值
        batch_size, num_heads, seq_len1, seq_len2 = attn_scores.size()
        
        # 对每一行找到Top-K的值（在最后一个维度上）
        topk_values, _ = torch.topk(attn_scores, k=k, dim=-1)
        
        # 获取每行的第k大值作为阈值
        threshold = topk_values[..., -1].unsqueeze(-1)
        
        # 创建掩码：只保留大于或等于阈值的值
        sparse_mask = (attn_scores >= threshold).float()
        
        # 应用掩码
        attn_scores = attn_scores * sparse_mask
        
        # 替换掩码为0的位置为负无穷（使softmax后为0）
        attn_scores = attn_scores.masked_fill((sparse_mask == 0), float('-inf'))
        
        # 应用softmax得到注意力权重
        attn_probs = F.softmax(attn_scores, dim=-1)
        
        # 应用dropout
        attn_probs = F.dropout(attn_probs, p=self.dropout, training=self.training)
        
        return attn_probs
    
    def forward(
        self, 
        query: torch.Tensor, 
        key: torch.Tensor, 
        value: torch.Tensor, 
        attention_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """前向传播，执行稀疏多头注意力计算。
        
        Args:
            query: 查询张量，形状为 [batch_size, seq_len, embed_dim]
            key: 键张量，形状为 [batch_size, seq_len, embed_dim]
            value: 值张量，形状为 [batch_size, seq_len, embed_dim]
            attention_mask: 注意力掩码，防止关注填充位置，形状为 [batch_size, 1, 1, seq_len]
            
        Returns:
            tuple: (注意力输出, 注意力权重)
                - 注意力输出形状: [batch_size, seq_len, embed_dim]
                - 注意力权重形状: [batch_size, num_heads, seq_len, seq_len]
        """
        batch_size = query.size(0)
        
        # 线性投影
        q = self.q_proj(query) * self.scaling
        k = self.k_proj(key)
        v = self.v_proj(value)
        
        # 调整形状以进行多头注意力计算
        q = q.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力分数 (QK^T)
        attn_scores = torch.matmul(q, k.transpose(-2, -1))
        
        # 应用稀疏softmax
        attn_probs = self._sparse_softmax(attn_scores, attention_mask)
        
        # 应用注意力权重得到加权值 (AV)
        attn_output = torch.matmul(attn_probs, v)
        
        # 调整形状，合并多头
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, -1, self.embed_dim
        )
        
        # 输出投影
        attn_output = self.out_proj(attn_output)
        
        return attn_output, attn_probs 