from flgo.experiment.logger import BasicLogger
import numpy as np
import flgo.simulator.base as ss
import json
import os
import time

class SimpleLogger(BasicLogger):
    r"""Simple Logger. Only evaluating model performance on testing dataset and validation dataset."""
    def initialize(self):
        """This method is used to record the stastic variables that won't change across rounds (e.g. local data size)"""
        for c in self.participants:
            self.output['client_datavol'].append(len(c.train_data))

    """This logger only records metrics on validation dataset"""
    def log_once(self, *args, **kwargs):
        self.info('Current_time:{}'.format(self.clock.current_time))
        self.output['time'].append(self.clock.current_time)
        test_metric = self.coordinator.test()
        for met_name, met_val in test_metric.items():
            self.output['test_' + met_name].append(met_val)
        val_metrics = self.coordinator.global_test(flag='val')
        local_data_vols = [c.datavol for c in self.participants]
        total_data_vol = sum(local_data_vols)
        for met_name, met_val in val_metrics.items():
            self.output['val_'+met_name+'_dist'].append(met_val)
            self.output['val_' + met_name].append(1.0 * sum([client_vol * client_met for client_vol, client_met in zip(local_data_vols, met_val)]) / total_data_vol)
            self.output['mean_val_' + met_name].append(np.mean(met_val))
            self.output['std_val_' + met_name].append(np.std(met_val))
        # output to stdout
        self.show_current_output()


    def save_output(self):
        """保存记录的输出到文件。
        
        覆盖BasicLogger的方法，处理文件名过长的问题。
        """
        try:
            # 确保目录存在
            if hasattr(self, 'task') and self.task:
                record_dir = os.path.join(self.task, 'record')
                if not os.path.exists(record_dir):
                    os.makedirs(record_dir, exist_ok=True)
                    
                # 生成一个更短的文件名
                algorithm = self.option.get('algorithm', 'unknown')
                timestamp = int(time.time())
                short_filename = f"{algorithm}_run_{timestamp}.json"
                
                # 完整路径
                output_path = os.path.join(record_dir, short_filename)
                
                # 保存输出
                with open(output_path, 'w') as f:
                    json.dump(self.output, f)
                self.info(f"日志已保存到: {output_path}")
                return True
            else:
                self.warning("无法保存日志，任务路径未定义")
                return False
        except Exception as e:
            self.error(f"保存日志时出错: {str(e)}")
            return False 