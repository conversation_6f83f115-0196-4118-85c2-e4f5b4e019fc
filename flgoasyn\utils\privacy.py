"""
差分隐私工具模块，基于Opacus库实现差分隐私相关功能。

此模块提供了对Opacus库的封装，使其更易于在异步联邦学习环境中使用。
主要功能包括:
1. 初始化差分隐私引擎
2. 计算和追踪隐私预算
3. 动态调整噪声参数
4. 支持联邦学习场景下的差分隐私SGD
"""
import copy
import logging
import math
import importlib.util
import warnings
from typing import Dict, List, Tuple, Any, Optional, Union

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import numpy as np
import opacus
# 抑制PyTorch非完整backward hook警告
# warnings.filterwarnings("ignore", message="Using a non-full backward hook when the forward contains multiple autograd Nodes")

# 不要修改 PyTorch 的 backward hook 行为，这会导致 Opacus 内部函数参数不匹配
# 我们将使用其他方法来处理警告



# 检查Opacus库是否可用
OPACUS_AVAILABLE = importlib.util.find_spec("opacus") is not None
print(f"OPACUS_AVAILABLE: {OPACUS_AVAILABLE}")
if OPACUS_AVAILABLE:
    # Opacus库导入
    try:
        from opacus.accountants import rdp
        from opacus.accountants.analysis import rdp as rdp_analysis
        logger = logging.getLogger(__name__)
        logger.info("成功导入Opacus库")
    except ImportError as e:
        OPACUS_AVAILABLE = False
        logger = logging.getLogger(__name__)
        logger.warning(f"导入Opacus库组件失败: {str(e)}")
else:
    logger = logging.getLogger(__name__)
    logger.warning("Opacus库不可用，将使用自定义实现")

logger = logging.getLogger(__name__)


class DPManager:
    """差分隐私管理器，封装Opacus库功能，提供易用的API。
    
    此类提供了初始化差分隐私引擎、追踪隐私预算以及动态调整噪声参数的功能。
    """
    
    def __init__(self, config):
        """初始化差分隐私管理器。
        
        Args:
            config: 配置字典，包含差分隐私参数
        """
        # 检查Opacus库是否可用
        self.opacus_available = OPACUS_AVAILABLE
        if not self.opacus_available:
            logger.warning("Opacus库不可用，将使用自定义实现，某些功能可能受限")
        
        # 基本参数
        self.epsilon_total = config.get('epsilon_total', 5.0)
        self.delta = config.get('delta', 1e-5)
        self.clip_norm = config.get('clip_norm', 1.0)
        
        # 注意: noise_scale在配置中可能表示标准差，但我们将其转换为噪声乘数
        noise_scale = config.get('noise_scale', 1.0)
        self.noise_multiplier = noise_scale  # 这是真正的噪声乘数，而非标准差
        
        # 设置RDP阶数参数alphas，优先使用配置中的值，否则使用默认值
        self.alphas = config.get('alphas', [1 + x / 10.0 for x in range(1, 100)] + list(range(12, 64)))
        logger.info(f"使用RDP阶数列表，包含{len(self.alphas)}个值")
        
        self.secure_mode = config.get('secure_mode', True)
        self.gamma = config.get('gamma', 0.5)  # 噪声自适应因子权重
        
        # 噪声基准模式和衰减相关参数
        self.noise_base_mode = config.get('noise_base_mode', 'dynamic')  # 'fixed' 或 'dynamic'
        self.initial_max_noise = config.get('initial_max_noise', 5.0)  # 固定模式下的初始噪声值
        
        # 新增: 噪声衰减模式和衰减系数
        self.noise_decay_mode = config.get('noise_decay_mode', 'exponential')  # 'exponential' 或 'hyperbolic'
        self.noise_decay_rate = config.get('noise_decay_rate', 0.01)  # 指数衰减模式的衰减率
        self.hyperbolic_decay_k = config.get('hyperbolic_decay_k', 0.05)  # 双曲衰减模式的系数k
        
        # 参考损失值，将保持为本地最大损失值
        self.loss_ref = None
        
        # 隐私引擎实例
        self.privacy_engine = None
        
        # 隐私预算追踪
        self.epsilon_used = 0.0
        self.epsilon_cumulative_total = 0.0  # 累积的总RDP消耗
        self.epsilon_remaining = self.epsilon_total  # 剩余隐私预算
        self.steps_count = 0
        
        # 添加对当前噪声参数的追踪
        self.current_noise_multiplier = self.noise_multiplier  # 当前使用的噪声乘数
        self.current_sample_rate = None                   # 当前使用的采样率，初始为None
        
        # 初始化RDP核算器
        self._init_accountant()
        
        # 初始化自定义RDP账本（当Opacus accountant不可用时使用）
        self.custom_rdp_steps = []  # 记录每步的{noise_multiplier, sample_rate}
        
        logger.info(f"初始化差分隐私管理器: epsilon={self.epsilon_total}, delta={self.delta}, "
                   f"clip_norm={self.clip_norm}, noise_multiplier={self.noise_multiplier}, gamma={self.gamma}, "
                   f"noise_base_mode={self.noise_base_mode}, initial_max_noise={self.initial_max_noise}, "
                   f"noise_decay_mode={self.noise_decay_mode}, noise_decay_rate={self.noise_decay_rate}, "
                   f"hyperbolic_decay_k={self.hyperbolic_decay_k}")
    
    def _init_accountant(self):
        """初始化RDP核算器。"""
        if self.opacus_available:
            try:
                from opacus.accountants.rdp import RDPAccountant
                # 修改：不再在初始化时传递alphas参数
                self.accountant = RDPAccountant()
                # 如果确实需要设置alphas，可以尝试使用其他API方法（如果存在）
                logger.debug("使用Opacus RDPAccountant")
            except ImportError:
                self.accountant = None
                logger.warning("Opacus RDPAccountant不可用，使用自定义RDP核算方法")
        else:
            self.accountant = None
            logger.warning("Opacus不可用，使用自定义RDP核算方法")
    
    def make_private(
        self,
        model: nn.Module,
        optimizer: torch.optim.Optimizer,
        data_loader: DataLoader,
        poisson_sampling: bool = True,
        skip_validation: bool = False
    ) -> Tuple[nn.Module, torch.optim.Optimizer, DataLoader]:
        """将模型、优化器和数据加载器转换为隐私保护版本。
        
        Args:
            model: 待保护的模型
            optimizer: 优化器
            data_loader: 数据加载器
            poisson_sampling: 是否使用泊松采样
            skip_validation: 是否跳过模型验证
            
        Returns:
            转换后的模型、优化器和数据加载器
        """
        # 首先检查Opacus是否可用
        if not self.opacus_available:
            logger.error("Opacus库不可用，无法进行差分隐私训练")
            return model, optimizer, data_loader
        
        try:
            # 检查Opacus依赖
            from opacus import PrivacyEngine
            from opacus.validators import ModuleValidator
            
            # 替换不兼容的模块（特别是nn.LSTM）
            try:
                # 检查模型是否有不兼容的模块
                if not ModuleValidator.is_valid(model):
                    logger.warning("模型包含不支持差分隐私计算的层，尝试修复...")
                    # 替换不兼容的模块（如LSTM）
                    model = ModuleValidator.fix(model)
                    logger.warning("已将不兼容的模块替换为DP兼容版本")
                    
                    # 在模型结构变化后重新初始化优化器
                    optimizer = type(optimizer)(model.parameters(), **optimizer.defaults)
            except Exception as e:
                logger.error(f"替换不兼容模块时发生错误: {str(e)}")
                # 尝试手动替换LSTM
                try:
                    # 尝试不同的导入路径，因为Opacus的API可能随版本变化
                    try:                            # 旧版本可能的路径
                        from opacus.layers import DPLSTM
                    except ImportError:
                        logger.error("无法导入DPLSTM，请确认Opacus版本和模块路径")
                        raise ImportError("无法找到Opacus中的DPLSTM模块")
                            
                    # 递归替换模型中的所有LSTM层
                    def replace_lstm(module):
                        for name, child in module.named_children():
                            if isinstance(child, nn.LSTM):
                                # 获取LSTM的参数
                                input_size = child.input_size
                                hidden_size = child.hidden_size
                                num_layers = child.num_layers
                                bias = child.bias
                                batch_first = child.batch_first
                                dropout = child.dropout
                                bidirectional = child.bidirectional
                                
                                # 创建DPLSTM替代
                                dp_lstm = DPLSTM(
                                    input_size=input_size,
                                    hidden_size=hidden_size,
                                    num_layers=num_layers,
                                    bias=bias,
                                    batch_first=batch_first,
                                    dropout=dropout,
                                    bidirectional=bidirectional
                                )
                                
                                # 如果可能，复制权重
                                try:
                                    dp_lstm.load_state_dict(child.state_dict())
                                except Exception as e:
                                    logger.warning(f"无法复制LSTM权重到DPLSTM: {str(e)}")
                                
                                # 替换模块
                                setattr(module, name, dp_lstm)
                                logger.info(f"已将{name}从nn.LSTM替换为DPLSTM")
                            else:
                                # 递归处理子模块
                                replace_lstm(child)
        
                    # 执行替换
                    replace_lstm(model)
                    
                    # 重新初始化优化器
                    optimizer = type(optimizer)(model.parameters(), **optimizer.defaults)
                except Exception as e2:
                    logger.error(f"手动替换LSTM失败: {str(e2)}")
            
            # 计算采样率
            sample_rate = 1.0 / len(data_loader) if len(data_loader) > 0 else None
            if sample_rate is None or sample_rate <= 0 or sample_rate > 1:
                logger.warning(f"无效的采样率: {sample_rate}，使用默认值0.01")
                sample_rate = 0.01
            
            # 保存当前的采样率
            self.current_sample_rate = sample_rate
            
            # 初始化隐私引擎，启用secure_mode以确保安全性
            # 实验阶段可以关闭secure_mode以提高性能，生产环境应设为True
            secure_mode = self.secure_mode
            
            # 重新初始化accountant和privacy_engine
            self._init_accountant()
            
            # 创建PrivacyEngine实例
            try:
                # 尝试使用新版API (Opacus >= 0.15.0)
                self.privacy_engine = PrivacyEngine(secure_mode=secure_mode)
                logger.info(f"使用Opacus新版API创建PrivacyEngine，secure_mode={secure_mode}")
            except TypeError:
                # 尝试使用旧版API
                try:
                    self.privacy_engine = PrivacyEngine(secure_rng=secure_mode)
                    logger.info(f"使用Opacus旧版API创建PrivacyEngine，secure_rng={secure_mode}")
                except Exception as e:
                    logger.error(f"创建PrivacyEngine失败: {e}")
                    raise e
            
            if not secure_mode:
                logger.warning("安全RNG已关闭。这在实验阶段是可以接受的，但在生产环境中应开启secure_mode=True")
            
            # 转换模型和优化器为隐私保护版本
            logger.info(f"开始将模型转换为差分隐私版本: noise_multiplier={self.current_noise_multiplier:.4f}, max_grad_norm={self.clip_norm}")
            try:
                # 尝试使用新版API
                model, optimizer, data_loader = self.privacy_engine.make_private(
                    module=model,
                    optimizer=optimizer,
                    data_loader=data_loader,
                    noise_multiplier=self.current_noise_multiplier,
                    max_grad_norm=self.clip_norm,
                    poisson_sampling=poisson_sampling
                )
                
                # 确保accountant被正确初始化
                if hasattr(self.privacy_engine, "accountant"):
                    self.accountant = self.privacy_engine.accountant
                    logger.info("成功获取privacy_engine中的accountant")
                
                # 保存原始模型的自定义方法到GradSampleModule
                # 这里我们检查原始模型是否有get_device方法，如果有，则添加到包装后的模型
                if hasattr(model, '_module'):
                    # 保存get_device方法
                    if hasattr(model._module, 'get_device') and not hasattr(model, 'get_device'):
                        logger.info("将原始模型的get_device方法添加到GradSampleModule")
                        model.get_device = lambda: model._module.get_device()
                    
                    # 保存其他可能需要的自定义方法
                    custom_methods = ['__add__', '__radd__', '__sub__', '__mul__', '__rmul__', 
                                     '__truediv__', '__pow__', '__neg__', 'norm', 'zeros_like', 
                                     'dot', 'cos_sim', 'op_with_graph', 'op_without_graph', 
                                     'load', 'freeze_grad', 'enable_grad', 'zero_dict', 
                                     'normalize', 'has_nan', 'count_parameters']
                    
                    for method_name in custom_methods:
                        if hasattr(model._module, method_name) and not hasattr(model, method_name):
                            logger.debug(f"将原始模型的{method_name}方法添加到GradSampleModule")
                            # 使用闭包保存方法名
                            def create_method_wrapper(name):
                                def wrapper(self, *args, **kwargs):
                                    method = getattr(self._module, name)
                                    return method(*args, **kwargs)
                                return wrapper
                            
                            import types
                            method = create_method_wrapper(method_name)
                            setattr(model, method_name, types.MethodType(method, model))
                
            except TypeError as e:
                # 处理可能的参数不匹配问题
                logger.warning(f"使用标准参数调用make_private失败: {e}，尝试替代参数")
                try:
                    # 尝试使用旧版API
                    model, optimizer, data_loader = self.privacy_engine.make_private_with_epsilon(
                        module=model,
                        optimizer=optimizer,
                        data_loader=data_loader,
                        target_epsilon=self.epsilon_total,
                        target_delta=self.delta,
                        epochs=1,  # 假设每次调用为1个epoch
                        max_grad_norm=self.clip_norm
                    )
                    
                    # 确保accountant被正确初始化
                    if hasattr(self.privacy_engine, "accountant"):
                        self.accountant = self.privacy_engine.accountant
                        logger.info("成功获取privacy_engine中的accountant (旧版API)")
                    
                    # 同样添加自定义方法
                    if hasattr(model, '_module'):
                        if hasattr(model._module, 'get_device') and not hasattr(model, 'get_device'):
                            logger.info("将原始模型的get_device方法添加到GradSampleModule")
                            model.get_device = lambda: model._module.get_device()
                except Exception as e2:
                    logger.error(f"使用替代参数调用make_private也失败: {e2}")
                    
                    # 最后尝试最基本的API
                    try:
                        model, optimizer, data_loader = self.privacy_engine.make_private(
                            module=model,
                            optimizer=optimizer,
                            data_loader=data_loader,
                            noise_multiplier=self.current_noise_multiplier,
                            max_grad_norm=self.clip_norm
                        )
                        
                        # 确保accountant被正确初始化
                        if hasattr(self.privacy_engine, "accountant"):
                            self.accountant = self.privacy_engine.accountant
                            logger.info("成功获取privacy_engine中的accountant (基本API)")
                    except Exception as e3:
                        logger.error(f"所有尝试都失败: {e3}")
                        raise e3
            
            # 手动记录一次步骤，确保隐私预算开始计算
            self.update_steps(
                steps=1, 
                noise_multiplier=self.current_noise_multiplier,
                sample_rate=self.current_sample_rate
            )
            
            logger.warning(f"模型已转换为支持差分隐私训练，噪声乘数={self.current_noise_multiplier:.4f}，采样率={sample_rate:.4f}")
            logger.warning(f"此刻模型类型: {type(model)}")
            
            return model, optimizer, data_loader
        except Exception as e:
            logger.error(f"转换模型为差分隐私版本时发生错误: {str(e)}")
            # 返回原始模型和优化器，但记录错误
            logger.error("将返回未修改的模型和优化器，差分隐私保护可能无法正常工作")
            logger.warning(f"此刻模型类型: {type(model)}")
            return model, optimizer, data_loader
    
    def get_privacy_spent(self, steps: Optional[int] = None) -> Tuple[float, float]:
        """获取当前消耗的隐私预算。
        
        Args:
            steps: 训练步数，如果为None则使用内部计数器
            
        Returns:
            (epsilon, delta)元组，表示当前消耗的隐私预算
        """
        # 使用传入的步数或内部计数器
        steps = steps if steps is not None else self.steps_count
        
        # 检查privacy_engine是否初始化
        if self.privacy_engine is None:
            logger.warning("隐私引擎未初始化，使用自定义RDP账本计算隐私预算")
            
            # 确保自定义RDP账本不为空
            if not self.custom_rdp_steps:
                logger.warning("自定义RDP账本为空，返回零隐私消耗")
                return 0.0, self.delta
                
            epsilon = self._compute_privacy_from_custom_rdp()
            
            # 更新隐私预算追踪
            self.epsilon_used = epsilon
            self.epsilon_remaining = max(0.0, self.epsilon_total - epsilon)
            
            return epsilon, self.delta
        
        # 使用Opacus内置的隐私预算计算
        try:
            # 首先检查privacy_engine是否有accountant属性
            if hasattr(self.privacy_engine, "accountant") and self.privacy_engine.accountant is not None:
                try:
                    # 尝试新版API
                    epsilon = self.privacy_engine.accountant.get_epsilon(delta=self.delta)
                    logger.debug(f"Opacus accountant计算的隐私预算: epsilon={epsilon}, delta={self.delta}")
                except (AttributeError, TypeError) as e:
                    logger.warning(f"使用新版API获取epsilon失败: {e}")
                    try:
                        # 尝试旧版API
                        epsilon = self.privacy_engine.get_epsilon(delta=self.delta)
                        logger.debug(f"Opacus privacy_engine计算的隐私预算: epsilon={epsilon}, delta={self.delta}")
                    except (AttributeError, TypeError) as e2:
                        logger.warning(f"使用旧版API获取epsilon失败: {e2}")
                        try:
                            # 尝试更旧版API
                            epsilon, _ = self.privacy_engine.get_privacy_spent(target_delta=self.delta)
                            logger.debug(f"Opacus旧版privacy_engine计算的隐私预算: epsilon={epsilon}, delta={self.delta}")
                        except Exception as e3:
                            logger.warning(f"所有API尝试失败: {e3}，回退到自定义计算")
                            epsilon = self._compute_privacy_from_custom_rdp()
            else:
                # 直接尝试从privacy_engine获取epsilon
                try:
                    epsilon = self.privacy_engine.get_epsilon(delta=self.delta)
                    logger.debug(f"从privacy_engine直接获取epsilon: {epsilon}")
                except (AttributeError, TypeError) as e:
                    logger.warning(f"从privacy_engine直接获取epsilon失败: {e}")
                    epsilon = self._compute_privacy_from_custom_rdp()
        except Exception as e:
            logger.warning(f"使用Opacus计算隐私预算失败: {e}，回退到自定义计算")
            epsilon = self._compute_privacy_from_custom_rdp()
        
        # 更新隐私预算追踪
        self.epsilon_used = epsilon
        self.epsilon_remaining = max(0.0, self.epsilon_total - epsilon)
        
        return epsilon, self.delta
    
    def _compute_privacy_from_custom_rdp(self) -> float:
        """使用自定义RDP账本计算隐私预算。
        
        当Opacus的accountant不可用时，使用自定义记录的步骤计算RDP。
        
        Returns:
            计算出的epsilon值
        """
        if not self.custom_rdp_steps:
            logger.warning("自定义RDP账本为空，无法计算隐私预算")
            return 0.0
            
        # 按步骤分组计算RDP
        rdp_total = [0.0] * len(self.alphas)
        
        total_steps = 0  # 总步数
        for step in self.custom_rdp_steps:
            noise_multiplier = step["noise_multiplier"]
            sample_rate = step["sample_rate"]
            count = step.get("count", 1)  # 获取步数计数，默认为1
            total_steps += count
            
            if sample_rate is None or sample_rate <= 0 or sample_rate > 1:
                logger.warning(f"步骤中无效的采样率: {sample_rate}，使用默认值0.01")
                sample_rate = 0.01
                
            try:
                # 计算此步骤的RDP
                rdp_step = rdp_analysis.compute_rdp(
                    q=sample_rate,
                    noise_multiplier=noise_multiplier,
                    steps=count,  # 使用步数计数
                    orders=self.alphas
                )
                
                # 累加RDP
                for i in range(len(rdp_total)):
                    rdp_total[i] += rdp_step[i]
            except Exception as e:
                logger.error(f"计算RDP时出错: {e}")
                continue
        
        try:
            # 将RDP转换为(ε,δ)-DP
            epsilon = rdp_analysis.get_privacy_spent(
                orders=self.alphas,
                rdp=rdp_total,
                delta=self.delta
            )[0]
            
            logger.info(f"自定义RDP账本计算的隐私预算: epsilon={epsilon:.4f}, delta={self.delta}, 总步数={total_steps}")
        except Exception as e:
            logger.error(f"转换RDP为DP时出错: {e}")
            # 返回一个保守估计
            epsilon = 0.0  # 改为返回0而不是无穷大，因为隐私预算应该从0开始增长
        
        logger.debug(f"自定义RDP账本计算的隐私预算: epsilon={epsilon}, delta={self.delta}")
        return epsilon

    def update_steps(self, steps=1, noise_multiplier=None, sample_rate=None):
        """更新步数计数器和RDP核算器。
        
        Args:
            steps: 执行的步数
            noise_multiplier: 本次使用的噪声乘数（如果与默认值不同）
            sample_rate: 本次使用的采样率（如果与默认值不同）
        """
        self.steps_count += steps
        
        # 更新噪声参数（如果提供）
        noise_multiplier = noise_multiplier if noise_multiplier is not None else self.current_noise_multiplier
        self.current_noise_multiplier = noise_multiplier
        
        if sample_rate is not None:
            self.current_sample_rate = sample_rate
        
        # 始终更新自定义RDP账本，以便在privacy_engine不可用时作为备份
        self.custom_rdp_steps.append({
            "noise_multiplier": noise_multiplier,
            "sample_rate": sample_rate if sample_rate is not None else 0.0,
            "count": steps
        })
        
        # 记录步骤到RDP核算器
        if self.accountant is not None:
            try:
                # 使用Opacus核算器记录步骤
                try:
                    # 尝试调用step方法
                    self.accountant.step(
                        noise_multiplier=noise_multiplier,
                        sample_rate=sample_rate if sample_rate is not None else 0.0
                    )
                except TypeError as te:
                    # 如果方法签名与预期不同，尝试替代方案
                    logger.warning(f"更新RDP核算器失败，可能是API不匹配: {te}")
                    # 记录到自定义账本作为后备
                logger.warning(f"记录步骤到RDP核算器: steps={steps}, noise={noise_multiplier:.4f}, "
                           f"sample_rate={sample_rate if sample_rate is not None else 'None'}")
            except Exception as e:
                # 如果Opacus核算器失败，记录到自定义账本
                logger.warning(f"记录步骤到Opacus accountant失败: {e}")
        else:
            # 使用自定义账本记录步骤
            logger.warning(f"记录步骤到自定义RDP账本: steps={steps}, noise={noise_multiplier:.4f}, "
                       f"sample_rate={sample_rate if sample_rate is not None else 'None'}")

    def _update_loss_ref(self, loss_value):
        """更新参考损失值L_ref。
        
        根据a1.md中的定义，L_ref是本地最大损失值。
        这个方法会在首次收到损失值时初始化L_ref，之后会保持L_ref为所有观察到的损失值中的最大值。
        
        Args:
            loss_value: 当前损失值
        """
        if loss_value is None:
            return
            
        # 确保损失值为非负且为有效数值
        try:
            loss_value = float(loss_value)
            if not math.isfinite(loss_value):  # 检查是否为NaN或Inf
                logger.warning(f"收到非有限损失值: {loss_value}，忽略")
                return
            loss_value = max(loss_value, 0.0)  # 确保损失值为非负
        except (TypeError, ValueError) as e:
            logger.warning(f"无法将损失值转换为浮点数: {e}，忽略")
            return
        
        # 如果是首次收到损失值，初始化L_ref
        if self.loss_ref is None:
            self.loss_ref = loss_value
            logger.info(f"初始化本地最大损失值L_ref={self.loss_ref:.4f}")
        # 否则，更新L_ref为最大损失值
        elif loss_value > self.loss_ref:
            old_ref = self.loss_ref
            self.loss_ref = loss_value
            logger.debug(f"更新本地最大损失值L_ref: {old_ref:.4f} -> {self.loss_ref:.4f}")
            
    def get_adaptive_noise_multiplier(
        self, 
        current_round: int, 
        loss_value: float = None, 
        grad_similarity: float = None,
        noise_decay_rate: float = None,  # 可选的噪声衰减率参数
        hyperbolic_decay_k: float = None,  # 可选的双曲衰减系数参数
    ) -> float:
        """计算自适应噪声乘数。
        
        基于当前轮次、损失值和梯度方向余弦相似度动态调整噪声乘数。
        注意：内部首先计算噪声标准差(σ)，然后将其转换为噪声乘数(z = σ/C)返回。
        
        支持两种噪声衰减模式:
        1. 指数衰减: σ_k = σ_{initial_base} · sqrt(β_k) · e^(-0.5 λ_{noise} t_{global})
        2. 双曲衰减: σ_k = σ_{initial_base} · sqrt(β_k) / sqrt(1 + k · t_{global})
        
        其中:
        - β_k = γ·(1-sim_k) + (1-γ)·L̂_k 是自适应因子
        - 返回的噪声乘数 z_k = σ_k / C，其中C是梯度裁剪范数
        
        Args:
            current_round: 当前训练轮次
            loss_value: 当前损失值
            grad_similarity: 梯度方向余弦相似度（范围[-1,1]）
            noise_decay_rate: 噪声衰减率λ_{noise}，默认使用实例属性值
            hyperbolic_decay_k: 双曲衰减系数k，默认使用实例属性值
            
        Returns:
            float: 计算得到的噪声乘数(noise_multiplier)，而非噪声标准差(sigma)
        """
        # 使用传入的参数或默认值
        noise_decay_rate = noise_decay_rate if noise_decay_rate is not None else self.noise_decay_rate
        hyperbolic_decay_k = hyperbolic_decay_k if hyperbolic_decay_k is not None else self.hyperbolic_decay_k
        
        # 根据噪声基准模式计算σ_{initial_base}
        if self.noise_base_mode == 'fixed':
            # 固定初始噪声模式：使用initial_max_noise作为σ_{initial_base}
            initial_base = self.initial_max_noise
            logger.debug(f"使用固定噪声基准值: σ_{{initial_base}}={initial_base:.4f}")
        else:
            # 动态噪声模式：使用current_noise_multiplier作为σ_{initial_base}
            initial_base = self.current_noise_multiplier
            logger.debug(f"使用动态噪声基准值: σ_{{initial_base}}={initial_base:.4f}")
        
        # 计算基于损失和梯度相似度的自适应因子β_k
        
        # 更新本地最大损失值L_ref
        if loss_value is not None:
            self._update_loss_ref(loss_value)
        
        # 初始化β_k为1.0（默认无调整）
        beta_k = 1.0
        
        # 计算基于损失的组件
        loss_component = 1.0
        if loss_value is not None and self.loss_ref is not None and self.loss_ref > 0:
            # 归一化损失值，使用本地最大损失值作为参考: L̂_k = L_k / L_{ref}
            norm_loss = loss_value / self.loss_ref
            # 限制在[0, 1]范围内，符合归一化的定义
            norm_loss = min(max(norm_loss, 0.0), 1.0)
            loss_component = norm_loss
            logger.debug(f"损失归一化: 原始损失={loss_value:.4f}, 本地最大损失={self.loss_ref:.4f}, 归一化结果={norm_loss:.4f}")
        elif loss_value is not None and (self.loss_ref is None or self.loss_ref == 0):
            # 如果还没有设置参考损失值或参考损失值为0，使用当前损失值作为参考
            self._update_loss_ref(loss_value)  # 设置初始本地最大损失值
            loss_component = 1.0  # 首次损失的归一化值为1
            if self.loss_ref is not None:
                logger.debug(f"首次损失计算，设置本地最大损失值L_ref={self.loss_ref:.4f}, 归一化结果=1.0")
            else:
                logger.debug("首次损失计算，但无法设置本地最大损失值，使用默认归一化结果=1.0")
        
        # 计算基于梯度相似度的组件
        similarity_component = 0.0
        if grad_similarity != -100000:
            # 将相似度从[-1,1]映射到[0,1]
            sim_k = (1 + grad_similarity) / 2
            similarity_component = 1.0 - sim_k  # 相似度越高，噪声越小
            logger.debug(f"梯度相似度组件: 原始相似度={grad_similarity:.4f}, 归一化相似度={sim_k:.4f}, 组件值={similarity_component:.4f}")
        
        # 计算自适应因子β_k = γ·(1-sim_k) + (1-γ)·L̂_k
        # 如果是初始轮次且没有梯度相似度，则更多依赖损失组件
        if current_round == 1 and grad_similarity ==-100000:
            # 初始轮次中，更多依赖损失组件
            beta_k = loss_component
            logger.warning(f"初始轮次中，仅使用损失组件计算自适应因子: β_k={beta_k:.4f}")
        else:
            # 正常计算自适应因子
            beta_k = self.gamma * similarity_component + (1 - self.gamma) * loss_component
        
        # 确保β_k >= 0
        beta_k = max(0.0, beta_k)
        logger.warning(f"自适应因子β_k计算: γ={self.gamma:.4f}, 相似度组件={similarity_component:.4f}, 损失组件={loss_component:.4f}, β_k={beta_k:.4f}")
        
        # 根据噪声衰减模式计算最终的噪声标准差
        if self.noise_decay_mode == 'hyperbolic':
            # 双曲衰减模式: σ_k = σ_{initial_base} · sqrt(β_k) / sqrt(1 + k · t_{global})
            decay_factor = 1.0 / math.sqrt(1.0 + hyperbolic_decay_k * current_round)
            final_noise = initial_base * math.sqrt(beta_k) * decay_factor
            logger.debug(f"使用双曲衰减模式: 衰减系数k={hyperbolic_decay_k:.4f}, "
                        f"当前轮次={current_round}, 衰减因子={decay_factor:.4f}")
        else:
            # 指数衰减模式（默认）: σ_k = σ_{initial_base} · sqrt(β_k) · e^(-0.5 λ_{noise} t_{global})
            decay_factor = math.exp(-0.5 * noise_decay_rate * current_round)
            final_noise = initial_base * math.sqrt(beta_k) * decay_factor
            logger.debug(f"使用指数衰减模式: 衰减率λ={noise_decay_rate:.4f}, "
                        f"当前轮次={current_round}, 衰减因子={decay_factor:.4f}")
        
        # 设置最小噪声值下限，确保差分隐私的有效性
        final_noise = max(final_noise, 0.01)
        
        
        # 更新当前噪声乘数（但不增加步数）
        self.current_noise_multiplier = final_noise / self.clip_norm  # 将标准差转换为乘数
        
        logger.warning(f"计算自适应噪声: 基础噪声模式={self.noise_base_mode}, 衰减模式={self.noise_decay_mode}, "
                    f"initial_base={initial_base:.4f}, beta_k={beta_k:.4f}, 噪声标准差sigma={final_noise:.4f}, "
                    f"噪声乘数z={self.current_noise_multiplier:.4f}, 裁剪范数C={self.clip_norm:.4f}")
        
        # print('self.current_noise_multiplier====================================================', self.current_noise_multiplier)
        
        return self.current_noise_multiplier  # 返回噪声乘数而不是标准差
    
    def calculate_rdp_cost(self, 
                         num_steps: int, 
                         noise_multiplier: float, 
                         sample_rate: float, 
                         alpha: float = 2.0
                        ) -> float:
        """计算RDP成本。
        
        Args:
            num_steps: 步数
            noise_multiplier: 噪声乘数
            sample_rate: 采样率
            alpha: RDP阶数
            
        Returns:
            RDP成本
        """
        # 计算一步的RDP成本
        q = sample_rate
        sigma = noise_multiplier
        
        # 使用Opacus的RDP会计函数计算RDP成本
        # 使用self.alphas列表而不是单个alpha参数
        rdp_vals = rdp.compute_rdp(q=q, noise_multiplier=sigma, steps=num_steps, orders=self.alphas)
        
        # 返回平均RDP成本
        return np.mean(rdp_vals)
    
    def estimate_noise_for_target_epsilon(
        self,
        target_epsilon: float,
        steps: int,
        sample_rate: float,
        delta: float = None
    ) -> float:
        """估算实现目标epsilon所需的噪声乘数。
        
        使用二分搜索查找噪声乘数。
        
        Args:
            target_epsilon: 目标epsilon值
            steps: 训练步数
            sample_rate: 采样率
            delta: delta值，默认使用初始化时设定的值
            
        Returns:
            估算的噪声乘数
        """
        delta = self.delta if delta is None else delta
        
        # 二分搜索的边界
        low, high = 0.1, 100.0
        best_noise = high
        best_eps = float('inf')
        
        # 二分搜索
        for _ in range(20):  # 20次迭代通常足够
            mid = (low + high) / 2
            
            # 使用Opacus的RDP会计函数计算epsilon
            rdp_vals = rdp.compute_rdp(
                q=sample_rate,
                noise_multiplier=mid,
                steps=steps,
                orders=self.alphas
            )
            
            eps = rdp.get_privacy_spent(
                orders=self.alphas,
                rdp=rdp_vals,
                delta=delta
            )[0]
            
            # 更新最佳结果
            if abs(eps - target_epsilon) < abs(best_eps - target_epsilon):
                best_noise = mid
                best_eps = eps
            
            # 调整搜索边界
            if eps > target_epsilon:
                low = mid
            else:
                high = mid
                
        logger.debug(f"估算噪声乘数: target_epsilon={target_epsilon}, steps={steps}, "
                    f"estimated_noise={best_noise}, resulting_epsilon={best_eps}")
        
        return best_noise

    def privacy_analysis_per_sample(self, 
                                   model: nn.Module, 
                                   sample_gradients: List[torch.Tensor], 
                                   max_grad_norm: float = None
                                  ) -> Dict[str, float]:
        """对单个样本进行隐私分析。
        
        Args:
            model: 模型
            sample_gradients: 样本梯度列表
            max_grad_norm: 梯度裁剪范数，默认使用初始化时设定的值
            
        Returns:
            包含隐私分析结果的字典
        """
        max_grad_norm = self.clip_norm if max_grad_norm is None else max_grad_norm
        
        # 分析结果
        result = {}
        
        # 计算梯度范数
        grad_norms = []
        for grad in sample_gradients:
            if grad is not None:
                grad_norms.append(torch.norm(grad).item())
        
        # 计算统计信息
        if grad_norms:
            result['min_grad_norm'] = min(grad_norms)
            result['max_grad_norm'] = max(grad_norms)
            result['mean_grad_norm'] = sum(grad_norms) / len(grad_norms)
            result['clipped_fraction'] = sum(1 for norm in grad_norms if norm > max_grad_norm) / len(grad_norms)
            
            # 计算裁剪后的平均敏感度
            clipped_norms = [min(norm, max_grad_norm) for norm in grad_norms]
            result['mean_sensitivity'] = sum(clipped_norms) / len(clipped_norms)
        
        return result


def create_dp_manager(config: Dict[str, Any]) -> DPManager:
    """创建差分隐私管理器实例。
    
    Args:
        config: 差分隐私配置参数
        
    Returns:
        DPManager: 差分隐私管理器实例
    """
    # 验证必要的参数
    required_params = ['epsilon_total', 'delta']
    for param in required_params:
        if param not in config:
            logger.error(f"创建DPManager失败: 缺少必要参数 '{param}'")
            raise ValueError(f"配置中缺少必要的差分隐私参数: {param}")
    
    # 验证参数类型和范围
    if config['epsilon_total'] <= 0:
        logger.warning(f"隐私预算epsilon_total必须为正数，当前值: {config['epsilon_total']}，将使用默认值5.0")
        config['epsilon_total'] = 5.0
    
    if config['delta'] <= 0 or config['delta'] >= 1:
        logger.warning(f"隐私参数delta必须在(0,1)之间，当前值: {config['delta']}，将使用默认值1e-5")
        config['delta'] = 1e-5
    
    # 检查Opacus依赖
    has_opacus = False
    try:
        import opacus
        logger.info(f"检测到Opacus版本: {opacus.__version__}")
        has_opacus = True
    except ImportError:
        logger.warning("未检测到Opacus库，将使用自定义RDP实现，建议安装Opacus以获得更好的性能和准确性")
    
    # 确保config中包含适当的RDP阶数参数
    if 'alphas' not in config and not has_opacus:
        logger.info("未指定RDP阶数参数，使用默认值alphas列表")
        config['alphas'] = [1 + x / 10.0 for x in range(1, 100)] + list(range(12, 64))
    
    # 创建安全默认配置，以便在出错时使用
    safe_config = {
        'epsilon_total': 5.0,
        'delta': 1e-5,
        'clip_norm': 1.0,
        'noise_scale': 2.0,
        'secure_mode': False
    }
    
    try:
        dp_manager = DPManager(config)
        return dp_manager
    except Exception as e:
        logger.error(f"创建DPManager时发生错误: {str(e)}")
        logger.error("将返回具有安全默认值的DPManager")
        
        try:
            # 最后尝试用安全默认配置创建DPManager
            return DPManager(safe_config)
        except Exception as e2:
            logger.critical(f"使用安全默认配置创建DPManager也失败: {str(e2)}")
            logger.critical("差分隐私功能将无法使用，请检查Opacus库版本和安装情况")
            # 返回一个空的DPManager
            return None


def per_sample_gradients(model: nn.Module, inputs: torch.Tensor, targets: torch.Tensor, 
                         loss_fn: callable = nn.CrossEntropyLoss()) -> List[torch.Tensor]:
    """计算每个样本的梯度。
    
    Args:
        model: 模型
        inputs: 输入数据
        targets: 目标数据
        loss_fn: 损失函数
        
    Returns:
        每个样本的梯度列表
    """
    # 要求模型为每个样本创建计算图
    model.zero_grad()
    outputs = model(inputs)
    
    # 计算每个样本的损失
    per_sample_losses = []
    for i in range(len(inputs)):
        if isinstance(outputs, tuple):
            output = tuple(o[i:i+1] for o in outputs)
        else:
            output = outputs[i:i+1]
        target = targets[i:i+1]
        loss = loss_fn(output, target)
        per_sample_losses.append(loss)
    
    # 计算每个样本的梯度
    sample_grads = []
    for loss in per_sample_losses:
        model.zero_grad()
        loss.backward(retain_graph=True)
        
        grad = []
        for param in model.parameters():
            if param.grad is not None:
                grad.append(param.grad.clone())
        
        sample_grads.append(grad)
    
    return sample_grads 




# 1. 计算每个样本的梯度
# sample_grads = per_sample_gradients(model, inputs, targets, loss_fn)

# # 2. 对这些梯度进行分析（可选）
# analysis_results = privacy_manager.privacy_analysis_per_sample(model, sample_grads, max_grad_norm)

# # 3. 使用这些梯度进行差分隐私训练（裁剪、加噪等）
# # ...