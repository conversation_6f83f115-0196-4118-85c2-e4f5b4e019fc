"""
CarHacking数据集处理模块。
用于读取和预处理车辆网络安全数据集，执行标签编码，并提取指定比例的数据。
"""
import os
import pandas as pd
import numpy as np
import re
import traceback
from sklearn.preprocessing import LabelEncoder
from typing import Dict, Tuple, List, Any, Optional, Union

def process_csv_file(file: str, csv_samples: int = 1000, random_seed: int = 42) -> Optional[pd.DataFrame]:
    """处理单个CSV文件并提取样本。
    
    Args:
        file: CSV文件路径
        csv_samples: 每类提取的样本数，默认为1000条
        random_seed: 随机种子，用于确保抽样结果可复现，默认为42
        
    Returns:
        处理后的DataFrame或None（如果处理失败）
    """
    try:
        filename = os.path.basename(file).lower()
        print(f"处理CSV文件: {filename}")
        
        # CSV文件默认没有列名，共12列
        column_names = ["Timestamp", "CAN_ID", "DLC", "Data0", "Data1", "Data2", 
                         "Data3", "Data4", "Data5", "Data6", "Data7", "Flag"]
        
        # 读取CSV文件，指定列名
        df = pd.read_csv(file, names=column_names, low_memory=False)
        
        # 删除最后一个Flag列
        if "Flag" in df.columns:
            df = df.drop(columns=["Flag"])
        
        # 处理Data0-Data7列
        data_columns = [col for col in df.columns if col.startswith('Data')]
        
        # 1. 删除包含"R"的记录
        initial_count = len(df)
        for col in data_columns:
            if col in df.columns:
                # 确保是字符串类型
                df[col] = df[col].astype(str)
                # 删除包含"R"的记录
                df = df[~df[col].str.contains('R', case=False, na=False)]
        
        removed_count = initial_count - len(df)
        if removed_count > 0:
            print(f"删除了{removed_count}条包含'R'的记录")
        
        if len(df) == 0:
            print(f"警告: 文件 {filename} 中所有记录都包含'R'，无法提取数据")
            return None
        
        # 2. 对少于两个字符的字符串，补0对齐至2位
        for col in data_columns:
            if col in df.columns:
                # 补0对齐至2位
                df[col] = df[col].astype(str).apply(
                    lambda x: x.zfill(2) if len(x) < 2 else x
                )
                print(f"处理{col}列: 补0对齐至2位")
        
        # 根据文件名添加类型标签
        if 'dos' in filename:
            print(f"文件 {filename} 包含DoS标签")
            df['type'] = 'DoS'
        elif 'fuzzy' in filename:
            print(f"文件 {filename} 包含Fuzzing标签")
            df['type'] = 'Fuzzing'
        elif 'gear' in filename:
            print(f"文件 {filename} 包含Gear标签")
            df['type'] = 'Gear'
        elif 'rpm' in filename:
            print(f"文件 {filename} 包含RPM标签")
            df['type'] = 'RPM'
        else:
            print(f"文件 {filename} 没有特定标签，跳过")
            return None
            
        # 提取指定条数据
        if len(df) > csv_samples:
            df = df.sample(csv_samples, random_state=random_seed)
            
        return df
        
    except Exception as e:
        print(f"处理CSV文件 {os.path.basename(file)} 时出错: {str(e)}")
        traceback.print_exc()
        return None

def process_carhacking_data(data_path: str, sample_ratio: float = 0.1, 
                           csv_samples: int = 1000, txt_samples: int = 5000, 
                           save_csv: bool = True, output_path: Optional[str] = None,
                           random_seed: int = 42) -> pd.DataFrame:
    """处理CarHacking数据集。
    
    Args:
        data_path: 数据所在的路径
        sample_ratio: 从原始数据中按类别提取的比例，默认为0.1
        csv_samples: 从CSV文件中提取的每类样本数，默认为1000条
        txt_samples: 从TXT文件中提取的Normal样本数，默认为5000条
        save_csv: 是否保存处理后的数据框到CSV文件，默认为True
        output_path: 输出CSV文件的路径，默认为flgoasyn/benchmark/RawData/carhacking.csv
        random_seed: 随机种子，用于确保抽样结果可复现，默认为42
        
    Returns:
        处理后的DataFrame
    """
    print(f"处理CarHacking数据集，路径: {data_path}")
    print(f"每类CSV样本提取数: {csv_samples}, TXT样本提取数: {txt_samples}")
    print(f"随机种子: {random_seed}")
    
    # 设置随机种子
    np.random.seed(random_seed)
    
    # 设置默认输出路径
    if output_path is None:
        # 获取项目根目录
        root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../"))
        output_path = os.path.join(root_dir, "flgoasyn/benchmark/RawData/carhacking.csv")
    
    # 寻找csv和txt文件
    data_files = []
    for root, dirs, files in os.walk(data_path):
        for file in files:
            if file.endswith('.csv') or file.endswith('.txt'):
                data_files.append(os.path.join(root, file))
    
    if not data_files:
        raise FileNotFoundError(f"在{data_path}路径下未找到任何csv或txt文件")
    
    print(f"找到{len(data_files)}个数据文件")
    
    # 分别处理CSV和TXT文件
    csv_data = pd.DataFrame()  # 用于存储CSV文件处理结果
    txt_data = pd.DataFrame()  # 用于存储TXT文件处理结果
    
    # 处理所有文件
    for file in data_files:
        file_ext = os.path.splitext(file)[1].lower()
        
        if file_ext == '.csv':
            df = process_csv_file(file, csv_samples, random_seed)
            if df is not None:
                csv_data = pd.concat([csv_data, df], ignore_index=True)
        
        elif file_ext == '.txt':
            try:
                filename = os.path.basename(file).lower()
                print(f"处理TXT文件: {filename}")
                
                # 读取TXT文件内容
                with open(file, 'r') as f:
                    lines = f.readlines()
                
                # 过滤掉空行并删除最后一行（可能不完整）
                lines = [line.strip() for line in lines if line.strip()]
                if lines:  # 确保有数据行
                    print(f"删除最后一行数据: {lines[-1]}")
                    lines = lines[:-1]  # 删除最后一行
                
                # 检查是否还有数据可处理
                if not lines:
                    print(f"警告: 删除最后一行后，文件 {filename} 中没有数据可处理")
                    return None
                
                # 创建空列表存储解析后的数据
                parsed_data = []
                
                # 解析每一行数据
                for line in lines:
                    try:
                        # 解析格式如: "Timestamp: 1479121436.103254        ID: 0329    000    DLC: 8    40 b9 7e 14 12 20 00 14"
                        # 使用正则表达式提取各个字段
                        timestamp_match = re.search(r'Timestamp:\s+(\S+)', line)
                        id_match = re.search(r'ID:\s+(\S+)', line)
                        dlc_match = re.search(r'DLC:\s+(\d+)', line)
                        
                        if not (timestamp_match and id_match and dlc_match):
                            print(f"警告: 无法解析行: {line}")
                            continue
                        
                        timestamp = timestamp_match.group(1)
                        can_id = id_match.group(1)
                        dlc = int(dlc_match.group(1))
                    
                        # 提取数据字节
                        # 查找DLC后面的所有内容，然后分割为数据字节
                        data_part = re.search(r'DLC:\s+\d+\s+(.*)', line)
                        if not data_part:
                            print(f"警告: 无法提取数据字节: {line}")
                            continue
                            
                        data_bytes = data_part.group(1).strip().split()
                
                        # 确保有足够的数据字节
                        if len(data_bytes) < dlc:
                            print(f"警告: 数据字节数量({len(data_bytes)})小于DLC({dlc}): {line}")
                            # 补充缺失的数据字节为"00"
                            data_bytes.extend(["00"] * (dlc - len(data_bytes)))
                        elif len(data_bytes) > 8:
                            print(f"警告: 数据字节数量({len(data_bytes)})超过8个: {line}")
                            data_bytes = data_bytes[:8]  # 只取前8个
                        
                        # 确保所有数据字节都是2字符长度（左补0对齐）
                        data_bytes = [byte.zfill(2) for byte in data_bytes]
                        
                        # 补充到8个数据字节
                        while len(data_bytes) < 8:
                            data_bytes.append("00")
                        
                        # 创建一行数据
                        row = {
                            'Timestamp': timestamp,
                            'CAN_ID': can_id,
                            'DLC': dlc
                        }
                        
                        # 添加数据字节
                        for i, byte in enumerate(data_bytes[:8]):  # 只取前8个
                            row[f'Data{i}'] = byte
                        
                        parsed_data.append(row)
                    except Exception as e:
                        print(f"解析行时出错: {line}")
                        print(f"错误详情: {str(e)}")
                
                # 创建DataFrame
                if not parsed_data:
                    print(f"警告: 文件 {filename} 中没有成功解析的行")
                    return None
                    
                processed_df = pd.DataFrame(parsed_data)
                
                # 根据文件名添加类型标签
                if 'normal' in filename:
                    print(f"文件 {filename} 包含Normal标签")
                    processed_df['type'] = 'Normal'
                    # 提取指定条TXT数据
                    if len(processed_df) > txt_samples:
                        processed_df = processed_df.sample(txt_samples, random_state=random_seed)
                    txt_data = pd.concat([txt_data, processed_df], ignore_index=True)
                else:
                    print(f"文件 {filename} 没有Normal标签，跳过")
                
            except Exception as e:
                print(f"处理TXT文件 {filename} 时出错: {str(e)}")
                traceback.print_exc()
    
    # 合并CSV和TXT处理的数据
    merged_data = pd.concat([csv_data, txt_data], ignore_index=True)
    
    if merged_data.empty:
        raise ValueError("没有成功处理任何数据")
    
    print(f"合并后的原始数据形状: {merged_data.shape}")
    
    # 进行数据清洗和转换
    try:
        # 处理Timestamp列 - 如果包含空格，取分割后的第二部分，并确保是数值类型
        if 'Timestamp' in merged_data.columns:
            # 首先处理空格问题（取第二部分，通常是实际的时间戳值）
            merged_data['Timestamp'] = merged_data['Timestamp'].astype(str).apply(
                lambda x: x.split(' ')[1] if ' ' in x and len(x.split(' ')) > 1 else x
            )
            
            # 确保Timestamp是数值类型（UNIX时间戳应该是数值）
            try:
                merged_data['Timestamp'] = pd.to_numeric(merged_data['Timestamp'], errors='coerce')
                # 检查是否有无法转换的值
                if merged_data['Timestamp'].isna().any():
                    print(f"警告: Timestamp列中有{merged_data['Timestamp'].isna().sum()}个值无法转换为数值，已填充为0")
                    merged_data['Timestamp'] = merged_data['Timestamp'].fillna(0)
                print("处理Timestamp列 - 已转换为数值类型")
            except Exception as e:
                print(f"警告: 处理Timestamp列时出错: {str(e)}")
                print("Timestamp列可能不是标准的UNIX时间戳格式")
        
        # 处理CAN_ID列 - 如果包含空格，取分割后的第二部分，并补齐4位
        if 'CAN_ID' in merged_data.columns:
            merged_data['CAN_ID'] = merged_data['CAN_ID'].astype(str).apply(
                lambda x: x.split(' ')[1] if ' ' in x and len(x.split(' ')) > 1 else x
            )
            # 补齐4位
            merged_data['CAN_ID'] = merged_data['CAN_ID'].apply(
                lambda x: x.zfill(4) if len(x) < 4 else x
            )
            # 将十六进制转换为整数（直接替换原始CAN_ID列）
            merged_data['CAN_ID'] = merged_data['CAN_ID'].apply(
                lambda x: int(x, 16) if all(c in '0123456789abcdefABCDEF' for c in x) else 0
            )
            print("处理CAN_ID列，将十六进制值转换为十进制整数")
        
        # 处理DLC列 - 如果包含空格，取分割后的第二部分
        if 'DLC' in merged_data.columns:
            merged_data['DLC'] = merged_data['DLC'].astype(str).apply(
                lambda x: x.split(' ')[1] if ' ' in x and len(x.split(' ')) > 1 else x
            )
            # 转换为整数
            merged_data['DLC'] = pd.to_numeric(merged_data['DLC'], errors='coerce').fillna(0).astype(int)
            print("处理DLC列")
        
        # 处理Data0-Data7列 - 补齐缺失值并转换为整数
        data_columns = [col for col in merged_data.columns if col.startswith('Data')]
        for col in data_columns:
            if col in merged_data.columns:
                # 填充缺失值
                merged_data[col] = merged_data[col].fillna('0')
                # 确保是字符串类型
                merged_data[col] = merged_data[col].astype(str)
                # 转换十六进制为整数
                merged_data[col] = merged_data[col].apply(
                    lambda x: int(x, 16) if all(c in '0123456789abcdefABCDEF' for c in x) else 0
                )
                print(f"处理{col}列")
        
        # 确保所有列都是数值类型
        encoders = {}  # 存储每列的编码器，便于后续使用
        for col in merged_data.columns:
            if col != 'type':  # 不处理type列，因为它将被转换为Label列
                if merged_data[col].dtype == 'object':
                    try:
                        # 尝试直接转换为数值
                        merged_data[col] = pd.to_numeric(merged_data[col], errors='raise')
                        print(f"将列 '{col}' 直接转换为数值类型")
                    except Exception as e:
                        # 如果无法直接转换，使用LabelEncoder
                        try:
                            encoder = LabelEncoder()
                            merged_data[col] = encoder.fit_transform(merged_data[col].astype(str))
                            encoders[col] = encoder
                            print(f"将列 '{col}' 使用LabelEncoder映射为整数编码")
                            # 打印编码映射
                            value_mapping = {label: idx for idx, label in enumerate(encoder.classes_)}
                            print(f"  列 '{col}' 的编码映射: {value_mapping}")
                        except Exception as e2:
                            print(f"警告: 无法对列 '{col}' 进行编码: {str(e2)}")
                            # 如果编码也失败，填充为0
                            merged_data[col] = 0
                            print(f"  列 '{col}' 已填充为0")
        
        # 保存编码器信息（可选）
        if encoders and save_csv:
            import pickle
            encoder_path = os.path.join(os.path.dirname(output_path), "carhacking_encoders.pkl")
            try:
                with open(encoder_path, 'wb') as f:
                    pickle.dump(encoders, f)
                print(f"特征编码器已保存到: {encoder_path}")
            except Exception as e:
                print(f"保存编码器时出错: {str(e)}")
        
        # 对type列进行标签编码，确保Normal类为0
        try:
            # 先使用LabelEncoder获取所有唯一类别
            label_encoder = LabelEncoder()
            original_labels = label_encoder.fit_transform(merged_data['type'])
            
            # 创建类别映射字典
            class_mapping = {label: idx for idx, label in enumerate(label_encoder.classes_)}
            reverse_mapping = {idx: label for label, idx in class_mapping.items()}
            
            # 检查是否存在Normal类别（不区分大小写）
            normal_class = None
            for class_name in label_encoder.classes_:
                # 确保class_name是字符串类型再调用lower()
                if isinstance(class_name, str) and class_name.lower() == 'normal':
                    normal_class = class_name
                    break
                elif not isinstance(class_name, str) and str(class_name).lower() == 'normal':
                    normal_class = class_name
                    break
            
            # 如果找到Normal类别，创建新的映射确保它为0
            if normal_class is not None:
                normal_idx = class_mapping[normal_class]
                
                # 创建新的映射关系
                new_mapping = {}
                idx_counter = 1  # 从1开始为非Normal类别编号
                
                # 将Normal类别映射为0
                new_mapping[normal_idx] = 0
                
                # 为其他类别重新分配索引
                for old_idx in range(len(label_encoder.classes_)):
                    if old_idx != normal_idx:
                        new_mapping[old_idx] = idx_counter
                        idx_counter += 1
                
                # 应用新映射到标签
                merged_data['Label'] = [new_mapping[idx] for idx in original_labels]
                
                # 创建更新后的类别到索引的映射用于显示
                updated_class_mapping = {reverse_mapping[old_idx]: new_idx 
                                        for old_idx, new_idx in new_mapping.items()}
                
                print("类别编码映射:")
                for class_name, idx in updated_class_mapping.items():
                    print(f"  {class_name} -> {idx}")
            else:
                # 如果没有找到Normal类别，直接使用LabelEncoder的结果
                merged_data['Label'] = original_labels
                print("警告: 未找到'Normal'类别，使用默认LabelEncoder编码")
                
            print("类别编码映射:")
            for class_name, idx in class_mapping.items():
                print(f"  {class_name} -> {idx}")
            
            # 删除原始type列
            merged_data = merged_data.drop(columns=['type'])
        except Exception as e:
            print(f"标签编码过程中出错: {str(e)}")
            traceback.print_exc()
            raise
    
    except Exception as e:
        print(f"数据清洗和转换过程中出错: {str(e)}")
        traceback.print_exc()
        raise
    
    # 处理缺失值
    merged_data = merged_data.fillna(0)

    # # 随机打乱数据，使用seed确保可复现
    # print(f"使用随机种子 {random_seed} 对数据进行随机打乱")
    # merged_data = merged_data.sample(frac=1, random_state=random_seed).reset_index(drop=True)
    # print("数据已随机打乱")

    print(f"处理后的数据形状: {merged_data.shape}")
    if 'Label' in merged_data.columns:
        print(f"类别分布:\n{merged_data['Label'].value_counts()}")
    
    # 保存处理后的数据框到CSV文件
    if save_csv and not merged_data.empty:
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"创建输出目录: {output_dir}")
        
        # 保存数据框到CSV文件
        merged_data.to_csv(output_path, index=False)
        print(f"处理后的数据已保存到: {output_path}")
    
    return merged_data

if __name__ == "__main__":
    # 如果直接运行此脚本，处理数据并保存到CSV
    import argparse
    
    parser = argparse.ArgumentParser(description='处理CarHacking数据集并保存为CSV')
    parser.add_argument('--data_path', type=str, default='D:/experiment/PFL/flgoasyn/benchmark/RawData/CarHacking',  help='数据所在的路径') #required=True,
    parser.add_argument('--output_path', type=str, default=None, 
                        help='输出CSV文件的路径，默认为flgoasyn/benchmark/RawData/carhacking.csv')
    parser.add_argument('--csv_samples', type=int, default=2000, help='从CSV文件中提取的每类样本数')
    parser.add_argument('--txt_samples', type=int, default=2000, help='从TXT文件中提取的Normal样本数')
    parser.add_argument('--random_seed', type=int, default=42, 
                        help='随机种子，用于确保抽样结果可复现，默认为42')
    
    args = parser.parse_args()
    
    # 处理数据并保存
    process_carhacking_data(
        data_path=args.data_path,
        csv_samples=args.csv_samples,
        txt_samples=args.txt_samples,
        save_csv=True,
        output_path=args.output_path,
        random_seed=args.random_seed
    )
    
    print("数据处理完成！")











# 主要修改内容包括：
# CSV文件处理：
# 为12列CSV文件指定列名：["Timestamp", "CAN_ID", "DLC", "Data0"~"Data7", "Flag"]
# 删除最后一列"Flag"
# 根据文件名添加类型标签（DoS、Fuzzing、Gear、RPM）
#首先对列"Data0"~"Data7处理，  如果这些列值的字符串中包含 "R"，那么删除该条记录。
# 然后，   下一步操作，如果这八列中的字符串少于两个字符，则在字符串补0对齐至2位。
# 为每种类型提取1000条数据
# TXT文件处理：
# 按4个以上空格分割文件内容得到行数据
# 删除第三列
# 分割最后一列获取数据字节
# 分配列名为["Timestamp", "CAN_ID", "DLC", "Data0"~"Data7"]
# 为包含"normal"的文件添加Normal标签
# 提取5000条Normal数据
# 数据清洗与转换：
# 处理Timestamp列，取空格分割后的第二部分
# 处理CAN_ID列，取空格分割后的第二部分，补齐4位，并转换为十进制整数
# 处理DLC列，取空格分割后的第二部分，转换为整数
# 处理Data0~Data7列，补齐缺失值，转换十六进制为整数
# 对type列进行标签编码生成Label列
# 数据验证：
# 添加了空数据检查
# 确保所有列都转换为数值类型
# 验证Label列存在

