name: pfllib
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - python=3.11
  - numpy<2
  - pip=22
  - pandas
  - scikit-learn
  - scipy
  - ujson
  - h5py
  - seaborn
  - matplotlib
  - click
  - pip:  # manually install with mirror URLs if a timeout occurs
    - torch==2.0.1
    - torchaudio
    - torchtext
    - torchvision
    - calmsize
    - memory-profiler
    - portalocker
    - cvxpy
    - higher
    - diffusers
    - accelerate
    - transformers
