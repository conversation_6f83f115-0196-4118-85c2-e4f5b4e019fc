"""
This is a non-official implementation of 'Measuring the Effects
of Non-Identical Data Distribution for Federated Visual Classification'
(http://arxiv.org/abs/1909.06335).
"""

from flgo.algorithm.asyncbase import AsyncServer
from flgo.algorithm.fedbase import BasicClient
import flgo.utils.fmodule as fmodule
import copy
import torch
import torch.nn.functional as F
import math
from flgo.algorithm.fedbase import BasicServer
import numpy as np
import logging
import os
import pickle
from typing import Dict, List, Any, Optional, Union, Tuple

# 差分隐私相关导入
from opacus.accountants.rdp import RDPAccountant

# 配置日志
logger = logging.getLogger(__name__)

class Server(BasicServer):
    def initialize(self, *args, **kwargs):
        """初始化FedAvgM服务器参数。"""
        # 从self.option获取用户传入的参数
        algo_para = self.option.get('algo_para', [])

        # 完全避免使用init_algo_para，直接为所有参数赋值
        # FedAvgM特有参数
        self.beta = 0.01  # FedAvgM动量参数

        # 按照main.py中train_option['algo_para']的顺序读取参数
        # 0: buffer_size
        self.buffer_size = 8 if len(algo_para) <= 0 else int(algo_para[0])
        # 1: sample_rate (client_sample_ratio)
        self.sample_rate = 0.5 if len(algo_para) <= 1 else float(algo_para[1])
        # 2: use_dp
        self.use_dp = bool(0 if len(algo_para) <= 2 else int(algo_para[2]))
        # 3: epsilon_total
        self.epsilon_total = 60 if len(algo_para) <= 3 else float(algo_para[3])
        # 4: delta
        self.delta = 1e-4 if len(algo_para) <= 4 else float(algo_para[4])
        # 5: clip_norm
        self.clip_norm = 1.0 if len(algo_para) <= 5 else float(algo_para[5])
        # 6: noise_scale
        self.noise_scale = 1.0 if len(algo_para) <= 6 else float(algo_para[6])
        # 25: num_clients
        self.num_clients = 20 if len(algo_para) <= 25 else int(algo_para[25])

        # FedAvgM的动量项
        self.v = self.model.zeros_like()

        logger.info(f"FedAvgM服务器初始化完成, beta={self.beta}, use_dp={self.use_dp}")

    def iterate(self):
        self.selected_clients = self.sample()
        models = self.communicate(self.selected_clients)['model']
        new_model = self.aggregate(models)
        self.v = self.beta*self.v + (self.model - new_model)
        self.model = self.model - self.v
        return
    

class Client(BasicClient):
    def initialize(self) -> None:
        """初始化客户端参数和差分隐私状态。"""
        super().initialize()

        # 初始化name属性（可以被外部覆盖）
        if not hasattr(self, 'name'):
            self.name = f"client_{self.id}" if hasattr(self, "id") else "unknown_client"

        # 初始化datavol属性（可以被外部覆盖）
        if not hasattr(self, 'datavol'):
            self.datavol = len(self.train_data) if hasattr(self, "train_data") and self.train_data else 0

        # 添加最小数据量跟踪
        self.min_data_size = len(self.train_data) if hasattr(self, "train_data") and self.train_data else 0

        # 从self.option获取用户传入的参数
        algo_para = self.option.get('algo_para', [])

        # 按照main.py中train_option['algo_para']的顺序读取参数
        # 2: use_dp
        self.use_dp = bool(0 if len(algo_para) <= 2 else int(algo_para[2]))
        # 3: epsilon_total
        self.epsilon_total = 60 if len(algo_para) <= 3 else float(algo_para[3])
        # 4: delta
        self.delta = 1e-4 if len(algo_para) <= 4 else float(algo_para[4])
        # 5: clip_norm
        self.clip_norm = 1.0 if len(algo_para) <= 5 else float(algo_para[5])
        # 6: noise_scale
        self.noise_scale = 1.0 if len(algo_para) <= 6 else float(algo_para[6])
        # 1: sample_rate (client_sample_ratio) - 用作proportion
        self.proportion = 0.5 if len(algo_para) <= 1 else float(algo_para[1])
        # 25: num_clients
        self.num_clients = 20 if len(algo_para) <= 25 else int(algo_para[25])

        # 差分隐私相关初始化
        if self.use_dp:
            # RDP相关参数
            self.alpha_opt = 10
            self.rdp_cost = 0.0
            self.current_steps = 0
            self.sigma = 1.0
            self.clip = self.clip_norm

            # 隐私预算管理
            self.total_rounds = self.option.get('num_rounds', 10)
            self.sampling_rate = self.proportion
            self.sigma_max = 4  # 最大噪声强度
            self.gamma = 0.1  # 噪声衰减参数

            # 计算总RDP预算
            self.rdp_total = self._calculate_total_rdp_budget(safety_factor=1)
            self.allocated_budgets = self._allocate_budgets()

            logger.info(f"客户端 {self.name} 初始化差分隐私管理器: epsilon={self.epsilon_total}, "
                        f"delta={self.delta}, noise_scale={self.noise_scale}")

        # 跟踪全局最小客户端数据量
        if not hasattr(Client, "_global_min_data_size"):
            Client._global_min_data_size = self.min_data_size
        else:
            if self.min_data_size < Client._global_min_data_size:
                Client._global_min_data_size = self.min_data_size

        logger.info(f"客户端 {self.name}: 数据量 = {self.min_data_size}, use_dp = {self.use_dp}")

    def _calculate_total_rdp_budget(self, safety_factor=1.0):
        """计算总RDP预算 (带缓冲的安全因子)"""
        rdp_budget = safety_factor * (self.epsilon_total +
                                      np.log(1 / self.delta) / (self.alpha_opt - 1))
        return rdp_budget

    def _allocate_budgets(self):
        """平均预算分配"""
        allocated_rdp = [self.rdp_total / self.total_rounds for _ in range(self.total_rounds)]
        return allocated_rdp

    def get_local_noise(self, round):
        """计算本地噪声强度 (时间衰减)"""
        self.gamma = 0.05
        decay = 1 / (1 + self.gamma * round)
        sigma_local = self.sigma_max * decay

        # 设置最小噪声强度
        min_noise = 0.00001 * self.sigma_max
        return max(sigma_local, min_noise)

    def _compute_local_rdp(self, accountant, sigma_local: float):
        """计算本地操作的RDP消耗"""
        accountant.step(
            noise_multiplier=sigma_local,
            sample_rate=self.sampling_rate
        )

        local_rdp = self._compute_rdp(accountant.history, [self.alpha_opt])
        return local_rdp[0]

    def _compute_rdp(self, history, orders: List[Union[float, int]] = None) -> List[float]:
        """计算所有历史操作的累计RDP值"""
        # 初始化累计RDP为0
        rdp = [0.0] * len(orders)

        # 累加所有操作的历史
        for (noise_multiplier, sample_rate, num_steps) in history:
            # 使用compute_rdp函数计算单次操作
            step_rdp = self.compute_rdp(
                q=sample_rate,
                noise_multiplier=noise_multiplier,
                steps=num_steps,
                orders=orders
            )

            # 累加RDP值
            rdp = [acc + current for acc, current in zip(rdp, step_rdp)]

        return rdp

    def compute_rdp(
            self,
            q: float,
            noise_multiplier: float,
            steps: int,
            orders: list,
            subsampled: bool = False,
    ) -> np.ndarray:
        """计算 Poisson 子采样 + Gaussian 机制的 RDP。"""
        rdp = []
        for alpha in orders:
            if noise_multiplier == 0:
                rdp_alpha = float("inf")
            elif not subsampled:
                # 近似上界
                rdp_alpha = alpha * (q ** 2) / (2 * noise_multiplier ** 2)
            else:
                # 精确 Poisson 子采样上界
                if q == 0:
                    rdp_alpha = 0.0
                else:
                    raw = alpha / (2 * noise_multiplier ** 2)
                    rdp_alpha = (
                            1.0 / (alpha - 1.0)
                            * np.log(1 + q ** 2 * (np.exp((alpha - 1.0) * raw) - 1))
                    )
            # 累积 steps 次
            rdp.append(rdp_alpha * steps)
        return np.array(rdp)

    def sigma_from_subsampled_rdp(
            self,
            eps_rdp: float,
            alpha: float,
            q: float,
            subsampled: bool = False
    ) -> float:
        """根据RDP消耗反推噪声倍增因子"""
        if alpha <= 1:
            raise ValueError("alpha must be > 1")
        if not (0 < q <= 1):
            raise ValueError("sampling rate q must be in (0,1]")

        # 计算基本 σ 单位敏感度值
        if not subsampled:
            # 纯 Gaussian 机制: ε_RDP = α / (2 σ^2)  => σ = sqrt(α / (2 ε_RDP))
            sigma_unit = math.sqrt(alpha / (2.0 * eps_rdp))
        else:
            exp_term = (alpha - 1) * eps_rdp
            # 数值稳定处理，避免 e^x 溢出
            if exp_term > 50:
                if q == 0:
                    raise ValueError("q must be > 0 for subsampled RDP inversion")
                raw = (exp_term - 2.0 * math.log(q)) / (alpha - 1)
            else:
                inner = math.exp(exp_term) - 1.0
                if q == 0:
                    raise ValueError("q must be > 0 for subsampled RDP inversion")
                raw = math.log(1.0 + inner / (q * q)) / (alpha - 1)

            # σ (unit sensitivity) = sqrt(α / (2 * raw))
            sigma_unit = math.sqrt(alpha / (2.0 * raw))

        return sigma_unit

    def reply(self, svr_pkg: Any) -> dict:
        """回复服务器，包含差分隐私处理。

        Args:
            svr_pkg: 服务器发送的包

        Returns:
            dict: 客户端回复的包
        """
        # 获取全局模型
        model = self.unpack(svr_pkg)

        # 保存全局模型副本
        global_model = copy.deepcopy(model)

        # 训练模型
        trained_model = self.train(model)

        # 如果启用差分隐私，对训练后的模型进行隐私保护处理
        if self.use_dp:
            # 获取当前轮次
            current_round = model._round if hasattr(model, '_round') else 0
            current_budget = self.allocated_budgets[min(current_round, len(self.allocated_budgets) - 1)]

            # 计算本地噪声
            sigma_local = self.get_local_noise(current_round)

            # 计算本地隐私消耗
            accountant = RDPAccountant()
            local_rdp = self._compute_local_rdp(accountant, sigma_local)

            # 如果本地隐私消耗超过当前预算，则调整噪声乘数
            if local_rdp > current_budget:
                logger.warning(
                    f"客户端 {self.name}: 本地消耗：{local_rdp:.6f}超过预算: {current_budget:.6f}")
                sigma_local = self.sigma_from_subsampled_rdp(current_budget, self.alpha_opt, self.sampling_rate)
                local_rdp = current_budget

            self.rdp_cost = local_rdp

            # 计算模型更新
            update = trained_model - global_model

            # 1. 梯度裁剪：将模型更新的参数临时拷贝到grad中进行裁剪
            params = list(update.parameters())
            for p in params:
                p.grad = p.data.clone()

            # 2. 全局 L₂ 裁剪
            torch.nn.utils.clip_grad_norm_(update.parameters(), max_norm=self.clip)

            # 3. 把裁剪后的 grad 写回 data，并清空 grad
            for p in params:
                p.data = p.grad.clone()
                p.grad = None

            # 4. 计算敏感度
            if hasattr(Client, '_global_min_data_size') and Client._global_min_data_size is not None:
                data_size = Client._global_min_data_size
            else:
                data_size = len(self.train_data)

            self.sensi = 2 * self.clip / data_size

            # 5. 添加高斯噪声
            noise_std = sigma_local * self.sensi

            for p in params:
                noise = torch.randn_like(p.data, device=self.device) * noise_std
                p.data.add_(noise)

            # 6. 将加噪后的更新应用到全局模型，得到最终的训练后模型
            trained_model = global_model + update

            logger.info(
                f"客户端 {self.name}: 对模型添加噪声，噪声标准差={noise_std:.6f}, "
                f"敏感度={self.sensi:.6f}, 噪声乘数={sigma_local:.4f}")

            self.sigma = sigma_local

        # 打包训练后的模型
        cpkg = self.pack(trained_model)
        return cpkg
    

    @fmodule.with_multi_gpus
    def train(self, model):
        r"""
        Standard local training procedure. Train the transmitted model with
        local training dataset.

        Args:
            model (FModule): the global model
        """
        model.train()
        optimizer = self.calculator.get_optimizer(model, lr=self.learning_rate, weight_decay=self.weight_decay,
                                                  momentum=self.momentum)
        
        # train_loader = self.calculator.get_dataloader(
        #     self.train_data, 
        #     batch_size=self.batch_size,
        #     shuffle=False,
        #     num_workers=self.loader_num_workers,
        #     pin_memory=self.option['pin_memory'], 
        #     drop_last=self.option.get('drop_last', False)
        # )
        
        # for _ in range(self.num_epochs):
        #     for batch_data in train_loader:
        #             # 移动数据到设备
        #         x, y = batch_data
        #         x, y = x.to(self.device), y.to(self.device)

        #         model.zero_grad()
        #         outputs = model(x)
        #         loss = F.cross_entropy(outputs, y)
                        
        #             # 反向传播和优化
        #         loss.backward()
        #         optimizer.step()
        # print("self.num_steps", self.num_steps)
        # print('new============', self.num_epochs * math.ceil(self.datavol / self.batch_size),   math.ceil(self.datavol / self.batch_size))
        for iter in range(self.num_epochs):
            # get a batch of data
            batch_data = self.get_batch_data()
            model.zero_grad()
            # calculate the loss of the model on batched dataset through task-specified calculator
            loss = self.calculator.compute_loss(model, batch_data)['loss']
            loss.backward()
            # torch.nn.utils.clip_grad_norm_(parameters=model.parameters(), max_norm=1.0)
            optimizer.step()

            # 更新本地步数（用于差分隐私）
            if self.use_dp:
                self.current_steps += 1

        return model