import os
from flgo.experiment.logger import BasicLogger
import numpy as np
import logging
import flgo.simulator.base as ss
import json
import time



class myLogger(BasicLogger):
    """自定义Logger类，扩展了BasicLogger以支持更多的指标记录。
    
    添加了对flag参数的记录，并支持记录包括准确率、精确率、召回率和F1分数在内的指标。
    """

    def initialize(self):
        """初始化方法，用于记录在多轮训练中不会改变的统计变量(例如本地数据大小)"""
        for c in self.participants:
            self.output['client_datavol'].append(len(c.train_data))
        
        # 初始化所有指标字段
        for key in ['train_loss', 'test_loss', 'val_loss']:
            if key not in self.output:
                self.output[key] = []
        
        # 初始化额外的指标字段
        for prefix in ['test', 'val', 'train', 'local_val', 'local_test']:
            for metric in ['accuracy', 'precision', 'recall', 'F1']:
                if f'{prefix}_{metric}' not in self.output:
                    self.output[f'{prefix}_{metric}'] = []
                if f'{prefix}_{metric}_dist' not in self.output:
                    self.output[f'{prefix}_{metric}_dist'] = []
                
        # 初始化均值和标准差指标
        for prefix in ['mean_local_train', 'std_local_train', 'mean_local_val', 'std_local_val', 'mean_local_test', 'std_local_test']:
            for metric in ['accuracy', 'precision', 'recall', 'F1']:
                if f'{prefix}_{metric}' not in self.output:
                    self.output[f'{prefix}_{metric}'] = []
                    
        # 执行初始测试，但捕获可能的异常
        try:
            # 全局测试指标
            test_metric = self.server.test()
            for met_name, met_val in test_metric.items():
                if f'test_{met_name}' not in self.output:
                    self.output[f'test_{met_name}'] = []
                self.output[f'test_{met_name}'].append(met_val)

            # 训练集指标
            train_metric = self.server.test(flag='train')
            for met_name, met_val in train_metric.items():
                if f'train_{met_name}' not in self.output:
                    self.output[f'train_{met_name}'] = []
                self.output[f'train_{met_name}'].append(met_val)

            # 验证集指标
            val_metric = self.server.test(flag='val')
            for met_name, met_val in val_metric.items():
                if f'val_{met_name}' not in self.output:
                    self.output[f'val_{met_name}'] = []
                self.output[f'val_{met_name}'].append(met_val)
        except Exception as e:
            self.info(f"初始化时测试失败: {str(e)}")
            # 可以在这里添加更多的异常处理逻辑

    def log_once(self, *args, **kwargs):
        """记录一轮训练的评估结果。
        
        Args:
            *args: 可变位置参数
            **kwargs: 可变关键字参数，可以包含flag参数以标识不同的记录场景
        """
        # 记录当前的标识
        round_num = self.server.current_round if hasattr(self.server, 'current_round') else 0
        
        # 获取check_interval参数
        # 正确获取check_interval的方式，从option字典中获取
        check_interval = 1  # 默认值
        if hasattr(self, 'option') and isinstance(self.option, dict):
            check_interval = self.option.get('check_interval', 1)
        elif hasattr(self.server, 'option') and isinstance(self.server.option, dict):
            check_interval = self.server.option.get('check_interval', 1)
        
        # 只在每check_interval轮次显示输出信息
        is_display_round = (round_num %  1== 0) or (round_num == 0) #check_interval
        
        # 每轮都记录日志信息
        self.info(f"记录第{round_num}轮训练")
        self.info('Current_time:{}'.format(self.clock.current_time))
        
        self.output['time'].append(self.clock.current_time)
        
        # 全局测试指标
        test_metric = self.server.test()
        for met_name, met_val in test_metric.items():
            self.output['test_' + met_name].append(met_val)

        # 训练集指标
        train_metric = self.server.test(flag='train')
        for met_name, met_val in train_metric.items():
            self.output['train_' + met_name].append(met_val)

        # 验证集指标
        val_metric = self.server.test(flag='val')
        for met_name, met_val in val_metric.items():
            self.output['val_' + met_name].append(met_val)
        
        # 计算训练数据集上所有参与者的指标加权平均
        local_data_vols = [c.datavol for c in self.clients]
        total_data_vol = sum(local_data_vols)
        train_metrics = self.server.global_test(flag='train')
        for met_name, met_val in train_metrics.items():
            self.output['train_' + met_name + '_dist'].append(met_val)
            
            # 修复：检查met_val是否为列表类型，如果是则进行加权平均计算
            if isinstance(met_val, list) and len(met_val) == len(local_data_vols):
                # 计算加权平均
                weighted_sum = sum([client_vol * client_met for client_vol, client_met in zip(local_data_vols, met_val) 
                                   if isinstance(client_met, (int, float))])  # 确保只处理数值类型
                self.output['train_' + met_name].append(1.0 * weighted_sum / total_data_vol)
                # 计算均值和标准差
                valid_values = [v for v in met_val if isinstance(v, (int, float))]
                if valid_values:
                    self.output['mean_local_train_' + met_name].append(np.mean(valid_values))
                    self.output['std_local_train_' + met_name].append(np.std(valid_values))
                else:
                    self.output['mean_local_train_' + met_name].append(0.0)
                    self.output['std_local_train_' + met_name].append(0.0)
            else:
                # 如果不是列表或长度不匹配，直接添加原值
                self.output['train_' + met_name].append(met_val if isinstance(met_val, (int, float)) else 0.0)
                self.output['mean_local_train_' + met_name].append(met_val if isinstance(met_val, (int, float)) else 0.0)
                self.output['std_local_train_' + met_name].append(0.0)
        
        # 计算验证数据集上所有客户端的指标加权平均和其他统计信息
        local_val_metrics = self.server.global_test(flag='val')
        for met_name, met_val in local_val_metrics.items():
            self.output['local_val_'+met_name+'_dist'].append(met_val)
            
            # 修复：检查met_val是否为列表类型，如果是则进行加权平均计算
            if isinstance(met_val, list) and len(met_val) == len(local_data_vols):
                # 计算加权平均
                weighted_sum = sum([client_vol * client_met for client_vol, client_met in zip(local_data_vols, met_val) 
                                   if isinstance(client_met, (int, float))])  # 确保只处理数值类型
                self.output['local_val_' + met_name].append(1.0 * weighted_sum / total_data_vol)
                # 计算均值和标准差
                valid_values = [v for v in met_val if isinstance(v, (int, float))]
                if valid_values:
                    self.output['mean_local_val_' + met_name].append(np.mean(valid_values))
                    self.output['std_local_val_' + met_name].append(np.std(valid_values))
                else:
                    self.output['mean_local_val_' + met_name].append(0.0)
                    self.output['std_local_val_' + met_name].append(0.0)
            else:
                # 如果不是列表或长度不匹配，直接添加原值
                self.output['local_val_' + met_name].append(met_val if isinstance(met_val, (int, float)) else 0.0)
                self.output['mean_local_val_' + met_name].append(met_val if isinstance(met_val, (int, float)) else 0.0)
                self.output['std_local_val_' + met_name].append(0.0)
        
        # 计算测试数据集上所有客户端的指标加权平均和其他统计信息
        local_test_metrics = self.server.global_test(flag='test')
        for met_name, met_val in local_test_metrics.items():
            self.output['local_test_'+met_name+'_dist'].append(met_val)
            
            # 修复：检查met_val是否为列表类型，如果是则进行加权平均计算
            if isinstance(met_val, list) and len(met_val) == len(local_data_vols):
                # 计算加权平均
                weighted_sum = sum([client_vol * client_met for client_vol, client_met in zip(local_data_vols, met_val) 
                                   if isinstance(client_met, (int, float))])  # 确保只处理数值类型
                self.output['local_test_' + met_name].append(1.0 * weighted_sum / total_data_vol)
                # 计算均值和标准差
                valid_values = [v for v in met_val if isinstance(v, (int, float))]
                if valid_values:
                    self.output['mean_local_test_' + met_name].append(np.mean(valid_values))
                    self.output['std_local_test_' + met_name].append(np.std(valid_values))
                else:
                    self.output['mean_local_test_' + met_name].append(0.0)
                    self.output['std_local_test_' + met_name].append(0.0)
            else:
                # 如果不是列表或长度不匹配，直接添加原值
                self.output['local_test_' + met_name].append(met_val if isinstance(met_val, (int, float)) else 0.0)
                self.output['mean_local_test_' + met_name].append(met_val if isinstance(met_val, (int, float)) else 0.0)
                self.output['std_local_test_' + met_name].append(0.0)

        # 如果是需要显示的轮次，主动调用show_current_output方法显示信息
        if is_display_round:
            self.show_current_output()

    def show_current_output(self):
        """显示当前的输出，增强版本包括所有指标，并处理空列表情况。只在每check_interval轮次显示一次。"""
        try:
            # 覆盖父类的实现，不再调用super().show_current_output()
            round_num = self.server.current_round if hasattr(self.server, 'current_round') else 0
            
            # 获取check_interval参数
            # 正确获取check_interval的方式，从option字典中获取
            check_interval = 1  # 默认值
            if hasattr(self, 'option') and isinstance(self.option, dict):
                check_interval = self.option.get('check_interval', 1)
            elif hasattr(self.server, 'option') and isinstance(self.server.option, dict):
                check_interval = self.server.option.get('check_interval', 1)
            
            # 只在每check_interval轮次显示信息
            if round_num % check_interval != 0 and round_num != 0:
                return
                
            self.info('-'*25)
            self.info(f'当前轮次: {round_num}')
            
            # 显示基本指标
            for key, val in self.output.items():
                if key in ['client_datavol', 'time']:
                    continue
                
                # 检查val的类型并相应处理
                if isinstance(val, list) and val:  # 确保列表不为空
                    try:
                        last_val = val[-1]
                        if isinstance(last_val, (int, float)):
                            self.info('{:<25s}{:.4f}'.format(key, last_val))
                        else:
                            self.info('{:<25s}{}'.format(key, last_val))
                    except (IndexError, TypeError, KeyError):
                        self.info('{:<25s}{}'.format(key, "Not available"))
                elif isinstance(val, dict) and val:  # 处理字典类型
                    try:
                        # 对于字典，显示其字符串表示
                        self.info('{:<25s}{}'.format(key, str(val)))
                    except Exception:
                        self.info('{:<25s}{}'.format(key, "Dict (error displaying)"))
                else:
                    # 对于空列表或其他类型，显示"Not available"
                    self.info('{:<25s}{}'.format(key, "Not available"))
            
            # 显示额外的指标
            if round_num > 0:
                print(f"轮次 {round_num} 详细指标:")
                for prefix in ['test', 'val', 'train']:
                    for metric in ['accuracy', 'precision', 'recall', 'F1']:
                        key = f'{prefix}_{metric}'
                        if key in self.output:
                            try:
                                if isinstance(self.output[key], list) and self.output[key]:
                                    # 使用安全的方式获取值
                                    val = self.output[key][-1]
                                    if isinstance(val, (int, float)):
                                        print(f"  {key}: {val:.4f}")
                                    else:
                                        print(f"  {key}: {val}")
                                else:
                                    print(f"  {key}: No data")
                            except (IndexError, TypeError, KeyError) as e:
                                print(f"  {key}: Not available (Error: {e})")
        except Exception as e:
            self.info(f"显示输出时出错: {str(e)}")
            import traceback
            traceback.print_exc() 

    def save_output(self):
        """保存记录的输出到文件。
        
        覆盖BasicLogger的方法，处理文件名过长的问题。
        """
        try:
            # 确保目录存在
            if hasattr(self, 'task') and self.task:
                record_dir = os.path.join(self.task, 'record')
                if not os.path.exists(record_dir):
                    os.makedirs(record_dir, exist_ok=True)
                    
                # 生成一个更短的文件名
                algorithm = self.option.get('algorithm', 'unknown')
                timestamp = int(time.time())
                short_filename = f"{algorithm}_run_{timestamp}.json"
                
                # 完整路径
                output_path = os.path.join(record_dir, short_filename)
                
                # 保存输出
                with open(output_path, 'w') as f:
                    json.dump(self.output, f)
                self.info(f"日志已保存到: {output_path}")
                return True
            else:
                self.warning("无法保存日志，任务路径未定义")
                return False
        except Exception as e:
            self.error(f"保存日志时出错: {str(e)}")
            return False 