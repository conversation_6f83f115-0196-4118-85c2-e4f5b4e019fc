"""
CNN/BiLSTM入侵检测模型。
仅使用CNN或BiLSTM进行特征提取和分类，用于车联网场景的入侵检测。
"""
from typing import Optional, Dict, Any, Literal

import torch
import torch.nn as nn

from .lstm_encoder import LSTMEncoder
from .cnn_encoder import CNNEncoder


class CNNLSTMIDSModel(nn.Module):
    """CNN/BiLSTM入侵检测模型，仅使用CNN或BiLSTM进行特征提取和分类。
    
    简化版的入侵检测模型架构：
    数据预处理 → 特征编码(CNN或BiLSTM) → 全局池化 → 分类
    
    Attributes:
        input_size (int): 输入特征维度
        hidden_size (int): 隐藏层维度
        num_classes (int): 分类类别数
        dropout (float): Dropout概率
        encoder_type (str): 特征编码器类型，'lstm'或'cnn'
        feature_encoder (nn.Module): 特征编码器，LSTM或CNN
        classifier (nn.Linear): 分类器
    """
    
    def __init__(
        self, 
        input_size: int, 
        hidden_size: int = 128,
        num_classes: int = 2, 
        dropout: float = 0.1,
        encoder_type: Literal['lstm', 'cnn'] = 'lstm',
        cnn_kernel_size: int = 5,
        cnn_num_layers: int = 3,
        lstm_num_layers: int = 2,
        bidirectional: bool = True
    ) -> None:
        """初始化CNN/BiLSTM入侵检测模型。
        
        Args:
            input_size: 输入特征维度
            hidden_size: 隐藏层维度
            num_classes: 分类类别数
            dropout: Dropout概率
            encoder_type: 特征编码器类型，'lstm'或'cnn'
            cnn_kernel_size: CNN编码器的卷积核大小
            cnn_num_layers: CNN编码器的卷积层数
            lstm_num_layers: LSTM编码器的层数
            bidirectional: 是否使用双向LSTM
        """
        super(CNNLSTMIDSModel, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_classes = num_classes
        self.dropout = dropout
        self.encoder_type = encoder_type
        
        # 特征编码器 (LSTM或CNN)
        if encoder_type == 'lstm':
            self.feature_encoder = LSTMEncoder(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=lstm_num_layers,
                dropout=dropout,
                bidirectional=bidirectional
            )
        else:  # encoder_type == 'cnn'
            self.feature_encoder = CNNEncoder(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=cnn_num_layers,
                kernel_size=cnn_kernel_size,
                dropout=dropout
            )
        
        # 特征编码器输出的特征维度
        encoder_output_size = self.feature_encoder.output_size
        
        # Dropout层
        self.dropout_layer = nn.Dropout(dropout)
        
        # 分类器
        self.classifier = nn.Linear(encoder_output_size, num_classes)
        
        # 初始化参数
        self._init_parameters()
        
    def _init_parameters(self) -> None:
        """初始化模型参数。"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(
        self, 
        x: torch.Tensor, 
        attention_mask: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """前向传播。
        
        Args:
            x: 输入序列，形状为 [batch_size, seq_len, input_size]
            attention_mask: 注意力掩码，形状为 [batch_size, seq_len]
                1表示有效位置，0表示填充位置
            
        Returns:
            dict: 包含以下键:
                'logits': 分类logits，形状为 [batch_size, num_classes]
        """
        # 特征编码 (LSTM或CNN)
        if self.encoder_type == 'lstm':
            encoder_output, _ = self.feature_encoder(x)
        else:
            encoder_output = self.feature_encoder(x)
        
        # 全局池化
        if attention_mask is not None:
            # 对有效位置进行平均池化
            mask = attention_mask.float().unsqueeze(-1)
            # 计算有效长度（用于平均）
            valid_lengths = torch.sum(mask, dim=1, keepdim=True)
            # 确保 valid_lengths 不为 0，避免除零错误
            valid_lengths = torch.clamp(valid_lengths, min=1.0)
            # 对有效位置求和后除以有效长度
            pooled = torch.sum(encoder_output * mask, dim=1) / valid_lengths
        else:
            # 如果没有mask，对整个序列进行平均
            pooled = torch.mean(encoder_output, dim=1)
        
        # 应用dropout
        pooled = self.dropout_layer(pooled)
        
        # 分类
        logits = self.classifier(pooled)
        
        return {
            'logits': logits
        }
        
    def predict(self, x: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """预测样本的类别。
        
        Args:
            x: 输入序列，形状为 [batch_size, seq_len, input_size]
            attention_mask: 注意力掩码，形状为 [batch_size, seq_len]
        
        Returns:
            torch.Tensor: 预测的类别索引，形状为 [batch_size]
        """
        outputs = self.forward(x, attention_mask)
        return torch.argmax(outputs['logits'], dim=1) 