import torch
import torch.nn as nn
import torch.nn.functional as F
# from flgoasyn.utils.fg import MyGradSampleModule
from flgoasyn.benchmark.idscustom.config import get_model
import os
from flgo.utils.fmodule import _model_add, _model_sub, _model_scale, _model_norm, _model_dot, _model_cossim

class DPModel(nn.Module):
    """支持差分隐私的Transformer入侵检测模型。

    封装了TransformerIDSModel，继承自MyGradSampleModule，实现了差分隐私功能。
    """
    def __init__(self):
        super(DPModel, self).__init__()
        self.ingraph = False
        # 使用config.py中的get_model()函数获取模型实例
        self.model = get_model()
        if hasattr(self.model, 'compute_loss'):
            self.compute_loss = self.model.compute_loss


    def forward(self, data):
        """前向传播方法，处理输入数据并返回结果。

        Args:
            data: 输入数据，可以是以下格式之一：
                - 张量: 直接作为输入特征
                - 字典: 包含'x'键作为输入特征，可选'attention_mask'键
                - 元组/列表: 第一个元素为输入特征，第二个元素(如果存在)为标签
                
        Returns:
            模型输出的logits
        """
        # 处理输入数据，提取特征和attention_mask

        # 处理字典输入
        if isinstance(data, dict):
            x = data['x'] if 'x' in data else data.get('data', None)
            attention_mask = data.get('attention_mask', None)
            
            # 如果x为None但有其他可能的键
            if x is None:
                for key in data:
                    if isinstance(data[key], torch.Tensor) and key != 'attention_mask':
                        x = data[key]
                        break
                        
            if x is None:
                raise ValueError("无法从输入字典中提取特征数据")
                
        # 处理元组或列表输入（通常是dataloader的输出格式）
        elif isinstance(data, (tuple, list)):
            x = data[0]  # 第一个元素通常是特征
            attention_mask = None
            # 检查是否有额外的attention_mask输入
            if len(data) > 2 and isinstance(data[2], torch.Tensor):
                attention_mask = data[2]
                
        # 处理直接的张量输入
        elif isinstance(data, torch.Tensor):
            x = data
            attention_mask = None
            
        else:
            raise TypeError(f"不支持的输入类型: {type(data)}")

        # x, attention_mask = self._extract_inputs(data)
        
        # 调用TransformerIDSModel并返回logits
        output = self.model(x, attention_mask)
        return output['logits']

    def predict(self, data):
        """预测样本的类别。

        Args:
            data: 输入数据，可以是以下格式之一：
                - 张量: 直接作为输入特征
                - 字典: 包含'x'键作为输入特征，可选'attention_mask'键
                - 元组/列表: 第一个元素为输入特征，第二个元素(如果存在)为标签

        Returns:
            torch.Tensor: 预测的类别索引，形状为 [batch_size]
        """
        

        # 处理输入数据，提取特征和attention_mask
        x, attention_mask = self._extract_inputs(data)
        
        # 调用TransformerIDSModel的predict方法
        return self.model.predict(x, attention_mask)
        
    def _extract_inputs(self, data):
        """从不同格式的输入数据中提取特征和attention_mask。
        
        Args:
            data: 输入数据，可以是张量、字典或元组/列表
            
        Returns:
            tuple: (特征张量, attention_mask或None)
        """
        # 处理字典输入
        if isinstance(data, dict):
            x = data['x'] if 'x' in data else data.get('data', None)
            attention_mask = data.get('attention_mask', None)
            
            # 如果x为None但有其他可能的键
            if x is None:
                for key in data:
                    if isinstance(data[key], torch.Tensor) and key != 'attention_mask':
                        x = data[key]
                        break
                        
            if x is None:
                raise ValueError("无法从输入字典中提取特征数据")
                
        # 处理元组或列表输入（通常是dataloader的输出格式）
        elif isinstance(data, (tuple, list)):
            x = data[0]  # 第一个元素通常是特征
            attention_mask = None
            # 检查是否有额外的attention_mask输入
            if len(data) > 2 and isinstance(data[2], torch.Tensor):
                attention_mask = data[2]
                
        # 处理直接的张量输入
        elif isinstance(data, torch.Tensor):
            x = data
            attention_mask = None
            
        else:
            raise TypeError(f"不支持的输入类型: {type(data)}")
            
        return x, attention_mask
    
    # def __class__(self):
    #     return DPModel
    
    def __add__(self, other):
        if isinstance(other, int) and other == 0 : return self
        if not isinstance(other, DPModel): raise TypeError
        return _model_add(self, other)

    def __radd__(self, other):
        return _model_add(self, other)

    def __sub__(self, other):
        if isinstance(other, int) and other == 0: return self
        if not isinstance(other, DPModel): raise TypeError
        return _model_sub(self, other)

    def __mul__(self, other):
        return _model_scale(self, other)

    def __rmul__(self, other):
        return self*other

    def __truediv__(self, other):
        return self*(1.0/other)

    def __pow__(self, power, modulo=None):
        return _model_norm(self, power)

    def __neg__(self):
        return _model_scale(self, -1.0)

    def __sizeof__(self):
        if not hasattr(self, '__size'):
            param_size = 0
            param_sum = 0
            for param in self.parameters():
                param_size += param.nelement() * param.element_size()
                param_sum += param.nelement()
            buffer_size = 0
            buffer_sum = 0
            for buffer in self.buffers():
                buffer_size += buffer.nelement() * buffer.element_size()
                buffer_sum += buffer.nelement()
            self.__size = param_size + buffer_size
        return self.__size

    def norm(self, p=2):
        r"""
        Args:
            p (float): p-norm

        Returns:
            the scale value of the p-norm of vectorized model parameters
        """
        return self**p

    def zeros_like(self):
        r"""
        Returns:
             a new model with the same architecture and all the parameters being set zero
        """
        return self*0

    def dot(self, other):
        r"""
        Args:
            other (Fmodule): the model with the same architecture of self

        Returns:
            the dot value of the two vectorized models
        """
        return _model_dot(self, other)

    def cos_sim(self, other):
        r"""
        Args:
            other (Fmodule): the model with the same architecture of self

        Returns:
            the cosine similarity value of the two vectorized models
        """
        return _model_cossim(self, other)

    def op_with_graph(self):
        self.ingraph = True

    def op_without_graph(self):
        self.ingraph = False

    def load(self, other):
        r"""
        Set the values of model parameters the same as the values of another model
        Args:
            other (Fmodule): the model with the same architecture of self
        """
        self.op_without_graph()
        self.load_state_dict(other.state_dict())
        return

    def freeze_grad(self):
        r"""
        All the gradients of the model parameters won't be computed after calling this method
        """
        for p in self.parameters():
            p.requires_grad = False

    def enable_grad(self):
        r"""
        All the gradients of the model parameters will be computed after calling this method
        """
        for p in self.parameters():
            p.requires_grad = True

    def zero_dict(self):
        r"""
        Set all the values of model parameters to be zero
        """
        self.op_without_graph()
        for p in self.parameters():
            p.data.zero_()

    def normalize(self):
        r"""
        Normalize the parameters of self to enable self.norm(2)=1
        """
        self.op_without_graph()
        self.load_state_dict((self/(self**2)).state_dict())

    def has_nan(self):
        r"""
        Check whether there is nan value in model's parameters
        Returns:
            res (bool): True if there is nan value
        """
        for p in self.parameters():
            if torch.any(torch.isnan(p)).item():
                return True
        return False

    def get_device(self):
        r"""
        Returns:
            the device of the tensors of this model
        """
        return next(self.parameters()).device

    def count_parameters(self, output=True):
        r"""
        Count the parameters for this model

        Args:
            output (bool): whether to output the information to the stdin (i.e. console)
        Returns:
            the number of all the parameters in this model
        """
        # table = pt.PrettyTable(["Modules", "Parameters"])
        total_params = 0
        for name, parameter in self.named_parameters():
            if not parameter.requires_grad:
                # table.add_row([name, 0])
                continue
            params = parameter.numel()
            # table.add_row([name, params])
            total_params += params
        # if output:
        #     print(table)
        #     print(f"TotalTrainableParams: {total_params}")
        return total_params
    


def init_local_module(object):
    """初始化本地模块。"""
    pass

def init_global_module(object):
    """初始化全局模块。"""
    if 'Server' in object.__class__.__name__:
            object.model = DPModel().to(object.device)
# class DPModel(MyGradSampleModule):
#     """支持差分隐私的Transformer入侵检测模型。

#     封装了TransformerIDSModel，继承自MyGradSampleModule，实现了差分隐私功能。
#     """
#     def __init__(self):
#         # 使用config.py中的get_model()函数获取模型实例
#         base_model = get_model()
#         super(DPModel, self).__init__(base_model)
#         self.model = base_model
#         if hasattr(self.model, 'compute_loss'):
#             self.compute_loss = self.model.compute_loss

#     def forward(self, data):
#         """前向传播方法，处理输入数据并返回结果。

#         Args:
#             data: 输入数据，可以是以下格式之一：
#                 - 张量: 直接作为输入特征
#                 - 字典: 包含'x'键作为输入特征，可选'attention_mask'键
#                 - 元组/列表: 第一个元素为输入特征，第二个元素(如果存在)为标签
                
#         Returns:
#             模型输出的logits
#         """
#         # 处理输入数据，提取特征和attention_mask
#         x, attention_mask = self._extract_inputs(data)
        
#         # 调用TransformerIDSModel并返回logits
#         output = self._module(x, attention_mask)
#         return output['logits']

#     def predict(self, data):
#         """预测样本的类别。

#         Args:
#             data: 输入数据，可以是以下格式之一：
#                 - 张量: 直接作为输入特征
#                 - 字典: 包含'x'键作为输入特征，可选'attention_mask'键
#                 - 元组/列表: 第一个元素为输入特征，第二个元素(如果存在)为标签

#         Returns:
#             torch.Tensor: 预测的类别索引，形状为 [batch_size]
#         """
#         # 处理输入数据，提取特征和attention_mask
#         x, attention_mask = self._extract_inputs(data)
        
#         # 调用TransformerIDSModel的predict方法
#         return self._module.predict(x, attention_mask)
        
#     def _extract_inputs(self, data):
#         """从不同格式的输入数据中提取特征和attention_mask。
        
#         Args:
#             data: 输入数据，可以是张量、字典或元组/列表
            
#         Returns:
#             tuple: (特征张量, attention_mask或None)
#         """
#         # 处理字典输入
#         if isinstance(data, dict):
#             x = data['x'] if 'x' in data else data.get('data', None)
#             attention_mask = data.get('attention_mask', None)
            
#             # 如果x为None但有其他可能的键
#             if x is None:
#                 for key in data:
#                     if isinstance(data[key], torch.Tensor) and key != 'attention_mask':
#                         x = data[key]
#                         break
                        
#             if x is None:
#                 raise ValueError("无法从输入字典中提取特征数据")
                
#         # 处理元组或列表输入（通常是dataloader的输出格式）
#         elif isinstance(data, (tuple, list)):
#             x = data[0]  # 第一个元素通常是特征
#             attention_mask = None
#             # 检查是否有额外的attention_mask输入
#             if len(data) > 2 and isinstance(data[2], torch.Tensor):
#                 attention_mask = data[2]
                
#         # 处理直接的张量输入
#         elif isinstance(data, torch.Tensor):
#             x = data
#             attention_mask = None
            
#         else:
#             raise TypeError(f"不支持的输入类型: {type(data)}")
            
#         return x, attention_mask
    


# def init_local_module(object):
#     """初始化本地模块。"""
#     pass

# def init_global_module(object):
#     """初始化全局模块。"""
#     if 'Server' in object.__class__.__name__:
#             object.model = DPModel().to(object.device)