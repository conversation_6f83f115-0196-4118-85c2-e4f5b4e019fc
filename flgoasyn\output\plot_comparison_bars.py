"""
对比柱状图绘制工具
用于绘制两组数据的对比柱状图，每组数据包含四个数值
"""

import matplotlib.pyplot as plt
import numpy as np
from typing import List, Tuple, Optional

def plot_comparison_bars(
    data_group1: List[float],
    data_group2: List[float],
    method1_name: str = "Method 1",
    method2_name: str = "Method 2",
    metric_names: List[str] = None,
    title: str = "Performance Comparison",
    ylabel: str = "Value",
    colors: Tuple[str, str] = ('#1f77b4', '#ff7f0e'),
    figsize: Tuple[int, int] = (6.4, 4.8),
    save_path: Optional[str] = None,
    show_values: bool = True,
    ylim: Optional[Tuple[float, float]] = None
):
    """
    绘制两组数据的对比柱状图

    Args:
        data_group1: 第一组数据（4个数值）
        data_group2: 第二组数据（4个数值）
        method1_name: 第一个方法的名称
        method2_name: 第二个方法的名称
        metric_names: 指标名称列表
        title: 图表标题
        ylabel: y轴标签
        colors: 两组数据的颜色
        figsize: 图表大小
        save_path: 保存路径（可选）
        show_values: 是否在柱子上显示数值
        ylim: y轴范围，格式为(min, max)，如果为None则自动设置
    """

    # 设置全局字体为 Times New Roman
    plt.rcParams['font.family'] = 'Times New Roman'
    plt.rcParams['mathtext.fontset'] = 'stix'
    plt.rcParams['axes.unicode_minus'] = False
    
    # 验证数据
    if len(data_group1) != 4 or len(data_group2) != 4:
        raise ValueError("每组数据必须包含4个数值")
    
    # 默认指标名称
    if metric_names is None:
        metric_names = ['Metric 1', 'Metric 2', 'Metric 3', 'Metric 4']
    
    if len(metric_names) != 4:
        raise ValueError("指标名称列表必须包含4个名称")
    
    # 设置图表参数
    plt.figure(figsize=figsize, dpi=300)
    
    # 设置柱子的位置
    x = np.arange(len(metric_names))
    width = 0.35  # 柱子宽度
    
    # 绘制柱状图
    bars1 = plt.bar(x - width/2, data_group1, width, label=method1_name, 
                    color=colors[0], alpha=0.8, edgecolor='black', linewidth=0.5)
    bars2 = plt.bar(x + width/2, data_group2, width, label=method2_name, 
                    color=colors[1], alpha=0.8, edgecolor='black', linewidth=0.5)
    
    # # 在柱子上显示数值
    # if show_values:
    #     for bar in bars1:
    #         height = bar.get_height()
    #         plt.text(bar.get_x() + bar.get_width()/2., height + max(max(data_group1), max(data_group2)) * 0.01,
    #                 f'{height:.3f}', ha='center', va='bottom', fontsize=9, fontweight='bold')
        
    #     for bar in bars2:
    #         height = bar.get_height()
    #         plt.text(bar.get_x() + bar.get_width()/2., height + max(max(data_group1), max(data_group2)) * 0.01,
    #                 f'{height:.3f}', ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    # 设置图表属性
    plt.xlabel('Suspension Probability', fontsize=16)
    plt.ylabel(ylabel, fontsize=16)
    plt.title(title, fontsize=14, fontweight='bold', pad=20)
    plt.xticks(x, metric_names, fontsize=14)
    plt.yticks(fontsize=14)
    
    # 添加图例
    plt.legend(fontsize=12, loc='upper right')
    
    # 添加网格
    plt.grid(axis='y', alpha=0.3, linestyle='--')
    
    # 设置y轴范围
    if ylim is not None:
        plt.ylim(ylim[0], ylim[1])
    else:
        # 自动设置y轴范围，留出空间显示数值
        y_max = max(max(data_group1), max(data_group2))
        y_min = min(min(data_group1), min(data_group2))
        y_range = y_max - y_min
        plt.ylim(y_min - y_range * 0.05, y_max + y_range * 0.15)
    
    # 使用tight_layout
    plt.tight_layout()
    
    # 保存图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"图表已保存到: {save_path}")
    
    # 显示图表
    plt.show()


def main():
    """
    示例用法
    """
    # 示例数据 CIC
    # method1_data = [0.8312, 0.8499, 0.8302, 0.8146]  # 第一个方法的四个指标
    # method2_data = [0.7339, 0.7524, 0.7507, 0.7336]  # 第二个方法的四个指标

    #  # 示例数据 TON
    # method1_data = [0.7648, 0.7767, 0.7618, 0.7420]  # 第一个方法的四个指标
    # method2_data = [0.7101, 0.7122, 0.7031, 0.6999]  # 第二个方法的四个指标


      # 示例数据 car
    method1_data = [0.8114, 0.8001, 0.7811, 0.7705]  # 第一个方法的四个指标
    method2_data = [0.7716, 0.7608, 0.7554, 0.7061]  # 第二个方法的四个指标
    
    # 指标名称
    metrics = ['0.2', '0.4', '0.6', '0.8']
    
    # 绘制对比图
    plot_comparison_bars(
        data_group1=method1_data,
        data_group2=method2_data,
        method1_name="Proposed",
        method2_name="Fedavgm",
        metric_names=metrics,
        title="",
        ylabel="Test Auucracy",
        colors=('#2E86AB', '#A23B72'),
        figsize=(6.4, 4.8),
        save_path="comparison_chart.png",
        show_values=True,
        ylim=(0.2, 1.0)  # 自定义y轴范围
    )


if __name__ == "__main__":
    main()
