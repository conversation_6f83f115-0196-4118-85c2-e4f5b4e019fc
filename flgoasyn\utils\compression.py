"""
通信压缩模块，用于减少联邦学习中的通信开销。
集成FedLab的压缩器。
"""
from typing import Dict, Tuple, Any, Optional, List, Union
import math
import sys

import torch
import numpy as np

try:
    from fedlab.contrib.compressor.quantization import QSGDCompressor
except ImportError:
    print("警告：无法导入FedLab压缩器，请确认FedLab已正确安装")
    
class CompressionManager:
    """通信压缩管理器，用于管理各种压缩方法。
    
    支持QSGD量化压缩，可以动态计算通信量。
    
    Attributes:
        compression_method (str): 压缩方法，如"qsgd"
        compression_params (dict): 压缩参数
        compressor: 压缩器实例
        original_size (int): 原始数据大小(bytes)
        compressed_size (int): 压缩后数据大小(bytes)
    """
    
    def __init__(
        self, 
        compression_method: str = "none", 
        compression_params: Optional[Dict[str, Any]] = None
    ) -> None:
        """初始化通信压缩管理器。
        
        Args:
            compression_method: 压缩方法，可选值："none", "qsgd"
            compression_params: 压缩参数，对于QSGD，需要包含"bits"参数
        """
        self.compression_method = compression_method.lower()
        self.compression_params = compression_params if compression_params else {}
        self.compressor = None
        self.original_size = 0
        self.compressed_size = 0
        
        # 初始化压缩器
        if self.compression_method == "qsgd":
            bits = self.compression_params.get("bits", 8)
            try:
                self.compressor = QSGDCompressor(bits=bits)
            except NameError:
                print("错误：FedLab库未正确安装，无法使用QSGD压缩")
                self.compression_method = "none"
    
    def compress(self, model_update: Dict[str, torch.Tensor]) -> Dict[str, Any]:
        """压缩模型更新。
        
        Args:
            model_update: 模型更新参数字典
            
        Returns:
            dict: 压缩后的模型更新
        """
        if self.compression_method == "none":
            # 计算原始大小
            self.original_size = self._calculate_model_size(model_update)
            self.compressed_size = self.original_size
            return model_update
        
        # 对于QSGD压缩
        if self.compression_method == "qsgd":
            # 计算原始大小
            self.original_size = self._calculate_model_size(model_update)
            
            # 执行压缩
            compressed_update = {}
            for key, param in model_update.items():
                compressed_param = self.compressor.compress(param)
                compressed_update[key] = compressed_param
            
            # 计算压缩后大小
            self.compressed_size = self._calculate_qsgd_size(compressed_update)
            
            return compressed_update
        
        # 默认情况下不压缩
        return model_update
    
    def decompress(self, compressed_update: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """解压缩模型更新。
        
        Args:
            compressed_update: 压缩后的模型更新
            
        Returns:
            dict: 解压缩后的模型更新
        """
        if self.compression_method == "none":
            return compressed_update
        
        # 对于QSGD解压缩
        if self.compression_method == "qsgd":
            decompressed_update = {}
            for key, compressed_param in compressed_update.items():
                param = self.compressor.decompress(compressed_param)
                decompressed_update[key] = param
            
            return decompressed_update
        
        # 默认情况下直接返回
        return compressed_update
    
    def _calculate_model_size(self, model_update: Dict[str, torch.Tensor]) -> int:
        """计算模型更新的大小（字节）。
        
        Args:
            model_update: 模型更新参数字典
            
        Returns:
            int: 模型大小（字节）
        """
        size_bytes = 0
        for key, param in model_update.items():
            # 每个元素的字节数
            if param.dtype == torch.float32:
                bytes_per_element = 4
            elif param.dtype == torch.float64:
                bytes_per_element = 8
            elif param.dtype == torch.float16:
                bytes_per_element = 2
            else:
                bytes_per_element = sys.getsizeof(param.storage()) / param.nelement() if param.nelement() > 0 else 0
            
            # 总大小 = 元素数量 * 每个元素的字节数
            size_bytes += param.nelement() * bytes_per_element
        
        return size_bytes
    
    def _calculate_qsgd_size(self, compressed_update: Dict[str, Any]) -> int:
        """计算QSGD压缩后的大小（字节）。
        
        Args:
            compressed_update: 压缩后的模型更新
            
        Returns:
            int: 压缩后大小（字节）
        """
        bits = self.compression_params.get("bits", 8)
        size_bytes = 0
        
        for key, compressed_param in compressed_update.items():
            # QSGD压缩后的数据包含shape、norm和signs
            shape = compressed_param[0]  # 张量形状
            norm = compressed_param[1]   # 原始范数
            signs = compressed_param[2]  # 量化后的符号和绝对值
            
            # 计算形状和范数的大小
            size_bytes += sys.getsizeof(shape) + sys.getsizeof(norm)
            
            # 计算压缩后数据的大小
            num_elements = torch.prod(torch.tensor(shape)).item()
            size_bytes += math.ceil(num_elements * bits / 8)  # bits转bytes，向上取整
        
        return size_bytes
    
    def get_compression_statistics(self) -> Dict[str, float]:
        """获取压缩统计信息。
        
        Returns:
            dict: 压缩统计信息，包括原始大小、压缩后大小和压缩率
        """
        stats = {
            'original_size_bytes': self.original_size,
            'compressed_size_bytes': self.compressed_size,
            'compression_ratio': self.original_size / max(1, self.compressed_size),
            'original_size_mb': self.original_size / (1024 * 1024),
            'compressed_size_mb': self.compressed_size / (1024 * 1024),
        }
        
        return stats 