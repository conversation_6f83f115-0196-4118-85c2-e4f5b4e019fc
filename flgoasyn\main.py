"""
车联网入侵检测系统主入口文件。
基于异步联邦学习和差分隐私保护，实现安全、高效的入侵检测。
使用flgo框架进行异步联邦学习训练。
"""
import os
import sys
import argparse
import json
import pickle
import importlib
from typing import Dict, List, Tuple, Any, Optional

# 添加项目根目录到Python搜索路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(ROOT_DIR)

import torch
import numpy as np
import flgoasyn.simulator as simulator

# flgo库导入
import flgo
from flgo.utils.fflow import gen_benchmark, gen_benchmark_from_file
from flgoasyn.utils.myLogger import myLogger
from flgoasyn.utils.simpleLogger import SimpleLogger
from flgoasyn.utils.my_logger import FullLogger
# from flgoasyn.utils.my_logger import FullLogger

# 本地模块导入
# from models.transformer_ids_model import TransformerIDSModel
# from flgoasyn.data import load_data, load_sequence_data
# from flgoasyn.benchmark.idscustom.config import get_model


def parse_arguments() -> argparse.Namespace:
    """解析命令行参数。
    
    Returns:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(description='车联网入侵检测系统')
    
    # 数据集参数
    parser.add_argument('--data_path', type=str, default='D:/experiment/PFL/flgoasyn/benchmark/RawData/carh.csv', #toniot.csv
                        help='数据集路径, cicids2017, carhacking, toniot, carh')
    parser.add_argument('--data_name', type=str, default='carh',
                        help='数据集名称，用于生成文件名和标识数据集，默认从数据路径中提取cicids2017, carhacking, toniot, carh')
    parser.add_argument('--rawdata_path', type=str, default='',
                        help='供flgo使用的原始数据目录路径，用于gen_task')
    parser.add_argument('--seq_len', type=int, default=1,
                        help='序列长度')
    parser.add_argument('--stride', type=int, default=1,
                        help='滑动窗口步长')
    parser.add_argument('--test_size', type=float, default=0.3,
                        help='测试集比例')
    parser.add_argument('--label_col', type=str, default='Label',
                        help='标签列名称')
    
    # 模拟器参数
    parser.add_argument('--simulator', type=str, default='AvailabilityExampleSimulator', #AvailabilityExampleSimulator
                        choices=['StaticUniSimulator', 'DynamicUniSimulator', 
                                'ResponsivenessExampleSimulator', 'CompletenessExampleSimulator', 
                                'AvailabilityExampleSimulator', 'ConnectivityExampleSimulator', 'OtherSimulator'],
                        help='模拟器类型，用于模拟客户端行为。可选项：\n'
                             'StaticUniSimulator: 静态均匀模拟器，客户端响应时间保持不变；\n'
                             'DynamicUniSimulator: 动态均匀模拟器，客户端响应时间每轮更新；\n'
                             'ResponsivenessExampleSimulator: 模拟客户端响应时间；\n'
                             'CompletenessExampleSimulator: 模拟客户端工作完成度；\n'
                             'AvailabilityExampleSimulator: 模拟客户端可用性；\n'
                             'ConnectivityExampleSimulator: 模拟客户端连接质量')
    
    # 模型参数
    parser.add_argument('--input_size', type=int, default=11,  #carhacking 11    toniot 42    cicids2017 78
                        help='输入特征维度')
    parser.add_argument('--hidden_size', type=int, default=64,
                        help='隐藏层大小')
    parser.add_argument('--d_model', type=int, default=128,
                        help='Transformer模型维度')
    parser.add_argument('--nhead', type=int, default=4,
                        help='Transformer注意力头数量')
    parser.add_argument('--num_encoder_layers', type=int, default=2,
                        help='Transformer编码器层数')
    parser.add_argument('--dim_feedforward', type=int, default=256,
                        help='Transformer前馈网络维度')
    parser.add_argument('--num_classes', type=int, default=2,
                        help='分类类别数')
    parser.add_argument('--dropout', type=float, default=0.2,
                        help='Dropout比例')
    parser.add_argument('--k_ratio', type=float, default=0.8,
                        help='注意力层保留的token比例（低于0表示不剪枝）')
    
    # 新增：模型类型和编码器参数
    parser.add_argument('--model_type', type=str, default='contact_transformer',
                        choices=['transformer', 'cnn_lstm', 'sparse_transformer', 'contact_transformer', 'sparse_transformer_original'],
                        help=('模型类型: '\
                              'transformer(LSTM/CNN+Transformer 带位置编码), '\
                              'cnn_lstm(仅CNN或LSTM), '\
                              'sparse_transformer(仅Transformer), '\
                              'contact_transformer(特征编码+稀疏Transformer拼接, 无位置编码), '\
                              'sparse_transformer_original(增强版稀疏Transformer)'))
    parser.add_argument('--encoder_type', type=str, default='lstm',
                        choices=['lstm', 'cnn'],
                        help='特征编码器类型: lstm或cnn')
    parser.add_argument('--cnn_kernel_size', type=int, default=5,
                        help='CNN编码器的卷积核大小')
    parser.add_argument('--cnn_num_layers', type=int, default=2,
                        help='CNN编码器的卷积层数')
    parser.add_argument('--lstm_num_layers', type=int, default=2,
                        help='LSTM编码器的层数')
    parser.add_argument('--bidirectional', type=str, default='True',
                        choices=['True', 'False'],
                        help='LSTM是否双向: True或False')
    # parser.add_argument('--use_position_encoding', type=str, default='False',
    #                     choices=['True', 'False'],
    #                     help='是否使用位置编码: True或False')
    
    # 新增：增强版稀疏Transformer参数
    parser.add_argument('--window_sizes', type=str, default='1,3,5',
                        help='多尺度注意力的窗口大小列表，用逗号分隔，例如：3,5,7')
    parser.add_argument('--use_glu', type=str, default='False',
                        choices=['True', 'False'],
                        help='是否使用门控线性单元: True或False')
    parser.add_argument('--pre_norm', type=str, default='False',
                        choices=['True', 'False'],
                        help='是否使用Pre-LN范式: True或False')
    parser.add_argument('--use_multi_scale', type=str, default='True',
                        choices=['True', 'False'],
                        help='是否使用多尺度注意力: True或False')
    
    # 联邦学习参数
    parser.add_argument('--num_clients', type=int, default=20,
                        help='客户端数量')
    # parser.add_argument('--num_clients_per_round', type=int, default=5,
    #                     help='每轮参与的客户端数量')
    parser.add_argument('--client_sample_ratio', type=float, default=0.5,
                        help='每轮采样的客户端比例，范围[0,1]')
    parser.add_argument('--num_rounds', type=int, default=100,
                        help='全局训练轮数')
    parser.add_argument('--num_epochs', type=int, default=5,
                        help='客户端本地训练的轮数')
    parser.add_argument('--num_steps', type=int, default=-1,
                        help='the number of local steps, which dominate num_epochs when setting num_steps>0')
    parser.add_argument('--batch_size', type=int, default=128,
                        help='训练批次大小')
    parser.add_argument('--test_batch_size', type=int, default=128,
                        help='测试批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.002, #0.08
                        help='学习率')
    parser.add_argument('--optimizer', type=str, default='Adam',
                        choices=['SGD', 'Adam', 'RMSprop', 'Adagrad'],
                        help='优化器类型（SGD, Adam, Adagrad, RMSprop）')
    parser.add_argument('--learning_rate_decay', type=float, default=0.998,
                        help='学习率衰减率, default=0.998')
    parser.add_argument('--momentum', type=float, default=0.0,
                        help='优化器动量参数, default=0.0')
    parser.add_argument('--lr_scheduler', type=str, default='-1',
                        help='学习率调度器类型, default="-1"(不衰减), 0表示按轮次衰减 lr = lr*decay^round')
    parser.add_argument('--algorithm', type=str, default='asyncfl_m',
                        choices=['fedavg', 'fedprox', 'fedbuff', 'fedasync', 'ca2fl','asyncfl','asyncfl_m','asyncfl_1','asyncfl_new2'],
                        help='联邦学习算法')
    parser.add_argument('--aggregate_algorithm', type=str, default='other',
                        help='聚合算法（uniform, weighted_scale，weighted_com等）')
    parser.add_argument('--sample_algorithm', type=str, default='uniform',
                        help='客户端采样算法（uniform, md, full, full_available, uniform_available, md_available...）')
    
    # 添加数据分区器参数
    parser.add_argument('--partitioner_name', type=str, default='CustomDirichletPartitioner',
                        choices=['IIDPartitioner', 'DirichletPartitioner', 'ShardPartitioner', 'DiversityPartitioner', 
                                'CustomDirichletPartitioner', 'SampleNumDirichletPartitioner'],
                        help='数据分区器名称（IIDPartitioner, DirichletPartitioner等）')
    parser.add_argument('--dirichlet_alpha', type=float, default=0.5,
                        help='DirichletPartitioner的alpha参数，控制数据分布的非独立同分布程度。较小的值（如0.1）产生更加非IID的分布')
    parser.add_argument('--diversity', type=float, default=0.5,
                        help='DiversityPartitioner的多样性参数，控制客户端数据分布的多样性程度。取值范围[0,1]，值越大多样性越高')
    parser.add_argument('--num_shards_per_client', type=int, default=2,
                        help='ShardPartitioner的分片参数，控制每个客户端获取的分片数量。较小的值（如1或2）产生更加非IID的分布')
    
    # 异步FL参数
    parser.add_argument('--buffer_size', type=int, default=8,
                        help='异步更新缓冲区大小')
    parser.add_argument('--lr_scheme', type=int, default=1,
                        choices=[0, 1, 2],
                        help='学习率方案（0: 固定, 1: 基于Staleness指数衰减, 2: 指数衰减学习率）')
    
    # 差分隐私参数
    parser.add_argument('--use_dp', action='store_true',
                        help='是否使用差分隐私')
    parser.add_argument('--epsilon', type=float, default=60,
                        help='隐私预算epsilon')
    parser.add_argument('--delta', type=float, default=1e-4,
                        help='隐私松弛参数delta')
    parser.add_argument('--clip_norm', type=float, default=1.0,
                        help='梯度裁剪范数')
    parser.add_argument('--noise_scale', type=float, default=1.0,
                        help='噪声尺度')
    
    # 新增：个性化差分隐私参数
    parser.add_argument('--gamma', type=float, default=0.5,
                        help='噪声自适应因子权重')
    parser.add_argument('--noise_decay_mode', type=str, default='exponential', choices=['exponential', 'hyperbolic'],
                        help='噪声衰减模式: exponential或hyperbolic')
    parser.add_argument('--noise_base_mode', type=str, default='fixed', choices=['fixed', 'dynamic'],
                        help='噪声基准模式: fixed或dynamic')
    parser.add_argument('--initial_eplson', type=float, default=1,
                        help='固定模式下的初始最小预算')
    
    # 预算分配策略参数
    parser.add_argument('--budget_allocation_strategy', type=int, default=0,
                        choices=[0, 1, 2],
                        help='预算分配策略 (0: fixed, 1: uniform, 2: rdp_optimal)')
    parser.add_argument('--max_budget_per_round', type=float, default=20,
                        help='每轮最大预算')
    parser.add_argument('--max_budget_local_round', type=float, default=20,
                        help='每轮最大本地预算')
    parser.add_argument('--expected_total_rounds', type=int, default=100,
                        help='预期总轮数，用于预算分配')
    parser.add_argument('--noise_decay_rate', type=float, default=0.01,
                        help='噪声衰减率')
    parser.add_argument('--hyperbolic_decay_k', type=float, default=0.05,
                        help='双曲衰减系数')
                        
    # 全局学习率参数
    parser.add_argument('--eta', type=float, default=1.0,
                        help='全局学习率，控制服务器端模型更新步长')
    
    # 通信压缩参数
    parser.add_argument('--use_compression', action='store_true',
                        help='是否使用通信压缩')
    parser.add_argument('--compression_method', type=str, default='qsgd',
                        choices=['qsgd'],
                        help='压缩方法')
    parser.add_argument('--compression_bits', type=int, default=8,
                        help='压缩位数（仅QSGD方法有效）')
                        
    # 历史模型管理参数
    parser.add_argument('--history_size', type=int, default=100, 
                        help='最多保存的历史版本数')
    
    # 敏感度计算方法
    parser.add_argument('--sensitivity_method', type=str, default='median',
                        choices=['max', 'median'],
                        help='服务器端敏感度计算方法 (max, median)')

    # 模型复杂度分析参数
    parser.add_argument('--enable_model_analysis',  type=str, default='True',#action='store_true',
                        help='是否启用自动模型复杂度分析（参数数量和FLOPs计算）')
    parser.add_argument('--model_analysis_batch_size', type=int, default=128,
                        help='模型复杂度分析时使用的批次大小，默认为1')

    # 训练设置参数
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子')
    parser.add_argument('--device', type=str, default='',
                        help='计算设备，默认使用可用的GPU，否则使用CPU')
    parser.add_argument('--output_dir', type=str, default=None,
                        help='输出目录路径，默认为项目根目录下的benchmark目录')
    parser.add_argument('--train_holdout', type=float, default=0.0,
                        help='从本地训练数据集中留出验证集的比例，默认为0.0')
    
    # Benchmark参数
    parser.add_argument('--benchmark_name', type=str, default='idscustom',
                        help='benchmark名称，用于FLGO任务配置')
    
    # 新增：检查点参数
    parser.add_argument('--save_checkpoint', type=str, default='flgoasyn/output/asyn',
                        help='保存检查点的路径')
    parser.add_argument('--load_checkpoint', type=str, default='flgoasyn/output/asyn',
                        help='加载检查点的路径')
    parser.add_argument('--check_interval', help='save checkpoints every __ rounds;', type=int, default=100)
    parser.add_argument('--eval_interval', help='evaluate every __ rounds;', type=int, default=1)

    # 新增：任务目录名称参数
    parser.add_argument('--task_dir_name', type=str, default='task_1',
                        help='任务输出目录名称，默认为task_1')

    # 新增：并行参数
    parser.add_argument('--num_parallels', type=int, default=1,
                        help='并行进程/线程数量，默认为1(禁用并行)')
    parser.add_argument('--parallel_type', type=str, default='t', choices=['t', 'p', 'r'],
                        help='并行类型：t(多线程), p(多进程), r(Ray分布式)')
                        
    # # 新增：自适应学习率参数
    # parser.add_argument('--eta_0', type=float, default=0.1, 
    #                     help='基础学习率（方案二）')
    # parser.add_argument('--staleness_exponent', type=float, default=0.5,
    #                     help='Staleness衰减指数（方案二）')
    # parser.add_argument('--gamma_priv', type=float, default=0.5,
    #                     help='隐私预算衰减指数（方案二）')
    # parser.add_argument('--min_lr', type=float, default=1e-5,
    #                     help='最小学习率')

    args = parser.parse_args()
    
    # 参数验证逻辑
    # 验证数值参数的合理范围
    if args.seq_len <= 0:
        raise ValueError("序列长度(seq_len)必须为正整数")
    if args.stride <= 0:
        raise ValueError("滑动窗口步长(stride)必须为正整数")
    if not (0 < args.test_size < 1):
        raise ValueError("测试集比例(test_size)必须在0到1之间")
    if args.client_sample_ratio <= 0 or args.client_sample_ratio > 1:
        raise ValueError("客户端采样比例(client_sample_ratio)必须在0到1之间")
    if args.num_clients <= 0:
        raise ValueError("客户端数量(num_clients)必须为正整数")
    if args.dirichlet_alpha <= 0:
        raise ValueError("DirichletPartitioner的alpha参数必须为正数")
    if not (0 <= args.diversity <= 1):
        raise ValueError("DiversityPartitioner的多样性参数必须在0到1之间")
    if args.num_shards_per_client <= 0:
        raise ValueError("ShardPartitioner的分片参数必须为正整数")
    if args.buffer_size <= 0:
        raise ValueError("异步更新缓冲区大小(buffer_size)必须为正整数")
    if args.learning_rate <= 0:
        raise ValueError("学习率(learning_rate)必须为正数")
    
    # 验证差分隐私参数
    if args.use_dp:
        if args.epsilon <= 0:
            raise ValueError("隐私预算epsilon必须为正数")
        if args.delta <= 0 or args.delta >= 1:
            raise ValueError("隐私松弛参数delta必须在0到1之间")
        if args.clip_norm <= 0:
            raise ValueError("梯度裁剪范数(clip_norm)必须为正数")
        if args.noise_scale < 0:
            raise ValueError("噪声尺度(noise_scale)必须为非负数")
    
    # 验证文件路径
    if args.data_path and not os.path.exists(args.data_path):
        print(f"警告: 指定的数据路径 {args.data_path} 不存在")
    
    return args


def set_seed(seed: int) -> None:
    """设置随机种子以确保实验可重复。
    
    Args:
        seed: 随机种子
    """
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def main() -> None:
    """主函数。"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        
        # 设置随机种子
        set_seed(args.seed)
        
        # 设置设备
        if args.device:
            device = torch.device(args.device)
        else:
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        print(f"使用设备：{device}")
        
        # 设置默认输出目录（如果未指定）
        if args.output_dir is None:
            args.output_dir = os.path.join(ROOT_DIR, 'flgoasyn', 'benchmark')
            print(f"使用默认输出目录: {args.output_dir}")
        
        # 确保所有路径都是相对于根目录的绝对路径
        def ensure_absolute_path(path):
            """确保路径是绝对路径，如果是相对路径则转换为相对于ROOT_DIR的绝对路径"""
            if not path:
                return path
            if os.path.isabs(path):
                return path
            return os.path.abspath(os.path.join(ROOT_DIR, path))
        
        # 转换相关路径为绝对路径
        if args.data_path:
            args.data_path = ensure_absolute_path(args.data_path)
            print(f"数据路径: {args.data_path}")
            
        if args.rawdata_path:
            args.rawdata_path = ensure_absolute_path(args.rawdata_path)
            print(f"原始数据路径: {args.rawdata_path}")
            
        if args.output_dir:
            args.output_dir = ensure_absolute_path(args.output_dir)
            print(f"输出目录: {args.output_dir}")
            
        if args.save_checkpoint:
            args.save_checkpoint = ensure_absolute_path(args.save_checkpoint)
            print(f"检查点保存路径: {args.save_checkpoint}")
            
        if args.load_checkpoint:
            args.load_checkpoint = ensure_absolute_path(args.load_checkpoint)
            print(f"检查点加载路径: {args.load_checkpoint}")
        
        # 创建输出目录
        if not os.path.exists(args.output_dir):
            try:
                os.makedirs(args.output_dir, exist_ok=True)
            except PermissionError:
                print(f"错误：无法创建输出目录 {args.output_dir}，权限被拒绝。请检查目录权限。")
                return
            except Exception as e:
                print(f"错误：创建输出目录时发生异常：{str(e)}")
                return
        
        # 验证rawdata_path
        if args.rawdata_path:
            if os.path.exists(args.rawdata_path):
                print(f"使用原始数据路径：{args.rawdata_path}")
            else:
                print(f"警告：指定的原始数据路径 {args.rawdata_path} 不存在，可能会导致错误。")
        else:
            print("未指定rawdata_path，将使用默认数据加载方式。")
        
        # 如果用户没有提供数据名称，则从数据路径中提取
        if not args.data_name:
            if os.path.exists(args.data_path):
                data_file_name = os.path.basename(args.data_path)
                args.data_name = os.path.splitext(data_file_name)[0]
            else:
                args.data_name = "random"
        
        print(f"使用数据集名称：{args.data_name}")
        
        # 设置环境变量，使benchmark_config.py能够找到正确的数据信息
        # 添加类型检查和安全转换
        env_vars = {
            'IDS_DATA_NAME': str(args.data_name),
            'IDS_DATA_PATH': str(args.data_path),
            'IDS_SEED': str(args.seed),
            'USE_DP': str(args.use_dp),   # 将use_dp参数设置为环境变量
            'IDS_TEST_SIZE': str(float(args.test_size)),
            'IDS_SEQ_LEN': str(int(args.seq_len)),
            'IDS_STRIDE': str(int(args.stride)),
            'IDS_LABEL_COL': str(args.label_col),
            'IDS_INPUT_SIZE': str(int(args.input_size)),
            'IDS_HIDDEN_SIZE': str(int(args.hidden_size)),
            'IDS_D_MODEL': str(int(args.d_model)),
            'IDS_NHEAD': str(int(args.nhead)),
            'IDS_NUM_ENCODER_LAYERS': str(int(args.num_encoder_layers)),
            'IDS_DIM_FEEDFORWARD': str(int(args.dim_feedforward)),
            'IDS_NUM_CLASSES': str(int(args.num_classes)),
            'IDS_DROPOUT': str(float(args.dropout)),
            'IDS_K_RATIO': str(float(args.k_ratio)),
            'IDS_NUM_CLIENTS': str(int(args.num_clients)),
            'IDS_LEARNING_RATE_DECAY': str(float(args.learning_rate_decay)),
            'IDS_MOMENTUM': str(float(args.momentum)),
            'IDS_LR_SCHEDULER': str(args.lr_scheduler),
            # 新增：模型类型和编码器参数
            'IDS_MODEL_TYPE': str(args.model_type),
            'IDS_ENCODER_TYPE': str(args.encoder_type),
            'IDS_CNN_KERNEL_SIZE': str(int(args.cnn_kernel_size)),
            'IDS_CNN_NUM_LAYERS': str(int(args.cnn_num_layers)),
            'IDS_LSTM_NUM_LAYERS': str(int(args.lstm_num_layers)),
            'IDS_BIDIRECTIONAL': str(args.bidirectional),
            # 新增：增强版稀疏Transformer参数
            'IDS_WINDOW_SIZES': str(args.window_sizes),
            'IDS_USE_GLU': str(args.use_glu),
            'IDS_PRE_NORM': str(args.pre_norm),
            'IDS_USE_MULTI_SCALE': str(args.use_multi_scale),
            # 'IDS_USE_POSITION_ENCODING': str(args.use_position_encoding)
        }
        
        # 安全设置环境变量并打印
        for key, value in env_vars.items():
            os.environ[key] = value
            print(f"设置环境变量{key}={value}")
        
        # 使用新位置的配置文件
        benchmark_config_file = os.path.join(ROOT_DIR, 'flgoasyn', 'configs', 'myconfig.py')
        print(f"使用benchmark配置文件：{benchmark_config_file}")
        
        # 检查配置文件是否存在
        if not os.path.exists(benchmark_config_file):
            print(f"错误：找不到benchmark配置文件：{benchmark_config_file}")
            return
        
        # 生成benchmark
        print("生成flgo benchmark...")
        # 使用数据名称生成benchmark名称，确保为有效的Python标识符

        benchmark_name = args.benchmark_name
        benchmark_params = {
            'benchmark': benchmark_name,
            'config_file': benchmark_config_file,
            'target_path': args.output_dir,  # 将benchmark生成到输出目录
            'data_type': 'cv',  # tabular 表格数据 
            'task_type': 'classification',  # 分类任务
            'overwrite': False  #覆盖
        }
        
        # 使用flgo.gen_benchmark创建benchmark
        benchmark_path = None
        if not os.path.exists(benchmark_name):
            print("创建flgo联邦学习benchmark...")
            try:
                benchmark_path = gen_benchmark_from_file(**benchmark_params)
            except Exception as e:
                print(f"错误：创建benchmark时发生异常：{str(e)}")
                import traceback
                traceback.print_exc()
                return
        else:
            print(f"警告：benchmark {benchmark_name} 已存在，跳过创建。")
            benchmark_path = benchmark_name


        # 创建任务配置
        task_config = {
            'benchmark': {'name': benchmark_path},
            'partitioner': {'name': args.partitioner_name}  
        }
        
        # 根据选择的partitioner设置参数
        if args.partitioner_name == 'DirichletPartitioner':
            print(f"使用DirichletPartitioner，设置alpha={args.dirichlet_alpha}")
            task_config['partitioner']['para'] = {
                'num_clients': args.num_clients,
                'alpha': args.dirichlet_alpha,  # 添加alpha参数
                'error_bar': 1e-4 #误差阈值
            }
        elif args.partitioner_name == 'CustomDirichletPartitioner':
            print(f"使用自定义CustomDirichletPartitioner，设置alpha={args.dirichlet_alpha}")
            try:
                # 导入自定义分区器
                from flgoasyn.utils.Dirichlet_partition import CustomDirichletPartitioner
                task_config['partitioner']['name'] = CustomDirichletPartitioner
                task_config['partitioner']['para'] = {
                    'num_clients': args.num_clients,
                    'alpha': args.dirichlet_alpha
                }
            except ImportError as e:
                print(f"错误：无法导入CustomDirichletPartitioner：{str(e)}")
                print("将使用默认分区器。")
                # 回退到默认分区器
                task_config['partitioner']['name'] = 'DirichletPartitioner'
                task_config['partitioner']['para'] = {
                    'num_clients': args.num_clients,
                    'alpha': args.dirichlet_alpha,
                    'error_bar': 1e-4
                }
        elif args.partitioner_name == 'SampleNumDirichletPartitioner':
            print(f"使用自定义SampleNumDirichletPartitioner")
            try:
                # 导入自定义分区器
                from flgoasyn.utils.Dirichlet_partition import SampleNumDirichletPartitioner
                task_config['partitioner']['name'] = SampleNumDirichletPartitioner
                task_config['partitioner']['para'] = {
                    'num_clients': args.num_clients
                }
            except ImportError as e:
                print(f"错误：无法导入SampleNumDirichletPartitioner：{str(e)}")
                print("将使用默认分区器。")
                # 回退到默认分区器
                task_config['partitioner']['name'] = 'DirichletPartitioner'
                task_config['partitioner']['para'] = {
                    'num_clients': args.num_clients,
                    'alpha': args.dirichlet_alpha,
                    'error_bar': 1e-4
                }
        elif args.partitioner_name == 'DiversityPartitioner':
            print(f"使用DiversityPartitioner，设置diversity={args.diversity}")
            task_config['partitioner']['para'] = {
                'num_clients': args.num_clients,
                'diversity': args.diversity  # 添加diversity参数
            }
        elif args.partitioner_name == 'ShardPartitioner':
            print(f"使用ShardPartitioner，设置num_shards_per_client={args.num_shards_per_client}")
            task_config['partitioner']['para'] = {
                'num_clients': args.num_clients,
                'num_shards_per_client': args.num_shards_per_client  # 添加分片参数
            }
            try:
                # 导入自定义分区器到任务配置中
                from flgoasyn.utils.shards_partition import ShardPartitioner
                task_config['partitioner']['name'] = ShardPartitioner
            except ImportError as e:
                print(f"错误：无法导入ShardPartitioner：{str(e)}")
                print("将使用默认分区器。")
                # 回退到默认分区器
                task_config['partitioner']['name'] = args.partitioner_name
        else:
            # 其他分区器只需要设置客户端数量
            task_config['partitioner']['para'] = {'num_clients': args.num_clients}
        
        # 创建任务输出目录
        task_output_dir = os.path.join(args.output_dir, args.task_dir_name)
        print(f"使用任务目录：{task_output_dir}")
        
        # 无论任务目录是否存在，都创建flgo联邦学习任务
        print(f"创建flgo联邦学习任务到 {task_output_dir}...")    
        try:
            # 如果任务目录已存在，保留原目录及其中的 checkpoint，不再删除
            if os.path.exists(task_output_dir):
                # 注释掉原有删除逻辑，防止误删历史检查点
                import shutil
                try:
                    shutil.rmtree(task_output_dir)
                    print(f"已删除原有任务目录")
                    flgo.gen_task(
                    config=task_config,
                    task_path=task_output_dir,  # 指向任务存储路径
                    rawdata_path=args.rawdata_path  # 添加rawdata_path参数
                )
                except Exception as e:
                    print(f"警告：删除原有任务目录时出错：{str(e)}，将尝试直接覆盖")
                # print(f"任务目录已存在，跳过删除并保留: {task_output_dir}")
            else:
                # 使用flgo.gen_task创建任务
                flgo.gen_task(
                    config=task_config,
                    task_path=task_output_dir,  # 指向任务存储路径
                    rawdata_path=args.rawdata_path  # 添加rawdata_path参数
                )
                print("创建flgo联邦学习任务成功")
        except Exception as e:
            print(f"错误：创建联邦学习任务时发生异常：{str(e)}")
            import traceback
            traceback.print_exc()
            return
        
        # 根据args.algorithm查找对应的算法文件
        try:
            # 简化逻辑：直接使用算法名作为文件名
            algorithm_module_name = f"flgoasyn.algorithm.{args.algorithm.lower()}"
            print(f"尝试导入自定义算法模块: {algorithm_module_name}")
            
            # 尝试导入模块
            algorithm_module = importlib.import_module(algorithm_module_name)
            # 使用导入的模块作为method
            method = algorithm_module
            print(f"成功导入自定义算法模块: {algorithm_module_name}")
        except ImportError:
            # 如果没有找到对应的模块，使用flgo内置算法
            flgo_algorithm_name = f"flgo.algorithm.{args.algorithm.lower()}"
            print(f"未找到自定义算法模块，尝试使用flgo内置算法: {flgo_algorithm_name}")
            try:
                # 尝试导入flgo内置算法
                algorithm_module = importlib.import_module(flgo_algorithm_name)
                method = algorithm_module
                print(f"成功导入flgo内置算法模块")
            except ImportError as e:
                # 如果flgo内置算法导入失败，直接报错并退出
                error_msg = f"错误：无法导入算法 '{args.algorithm}'。既找不到自定义算法模块 '{algorithm_module_name}'，也找不到flgo内置算法 '{flgo_algorithm_name}'。"
                print(error_msg)
                print(f"导入错误详情: {str(e)}")
                raise ValueError(error_msg) from e

        if args.algorithm.lower() == 'asyncfl_m':
            # 如果使用自定义的异步联邦学习算法
            print(f"使用自定义异步联邦学习算法: AsyncFL")
        else:
            print(f"使用flgo内置的联邦学习算法: {args.algorithm}")

        # 创建训练选项
        train_option = {
            'learning_rate': args.learning_rate,
            'batch_size': args.batch_size,
            'num_rounds': args.num_rounds,
            'proportion': args.client_sample_ratio,  # 使用新的客户端采样比例参数
            'num_epochs': args.num_epochs,          # 从命令行参数获取本地训练轮数
            'num_steps': args.num_steps,            # 从命令行参数获取本地训练步数
            'sample': args.sample_algorithm,  
            'gpu': 0 if torch.cuda.is_available() else -1,  # 根据可用性设置GPU
            'aggregate': args.aggregate_algorithm,
            'test_batch_size': args.test_batch_size,  # 从命令行参数获取测试批次大小
            'optimizer': args.optimizer,      # 使用用户自定义的优化器
            'learning_rate_decay': args.learning_rate_decay,  # 学习率衰减率
            'momentum': args.momentum,        # 优化器动量参数
            'lr_scheduler': args.lr_scheduler,  # 学习率调度器类型
            'algo_para': [],        # 初始化算法特定参数为空列表
            'check_interval': args.check_interval,  # 添加检查点间隔参数
            'save_checkpoint': args.save_checkpoint,  # 添加保存检查点路径参数
            # 'load_checkpoint': 'D:/experiment\PFL/flgoasyn/benchmark/task_1/checkpoint/asyncfl_m_r100_t1752004168_dp.pt',  # 添加加载检查点路径参数    args.load_checkpoint
            'eval_interval': args.eval_interval,  # 添加评估间隔参数
            'num_parallels': args.num_parallels,     # 设置并行进程/线程数
            'parallel_type': args.parallel_type,    # 设置并行类型
            'train_holdout': args.train_holdout  # 添加train_holdout参数
        }

        # 如果使用异步算法，添加异步相关参数
        if args.algorithm.lower() == 'asyncfl_m':
            print("配置异步联邦学习参数...")

            
            
            # 将字符串参数转换为整数
            
            # 敏感度计算方式转整数: 'max' -> 0, 'median' -> 1
            sensitivity_method_int = 0 if args.sensitivity_method == 'max' else 1
            
            # 压缩方法转整数: 'qsgd' -> 0, 其他方法可以继续编号
            compression_method_int = 0  # 目前只支持'qsgd'
            
            # 噪声衰减模式转整数: 'exponential' -> 0, 'hyperbolic' -> 1
            noise_decay_mode_int = 0 if args.noise_decay_mode == 'exponential' else 1
            
            # 噪声基准模式转整数: 'fixed' -> 0, 'dynamic' -> 1
            noise_base_mode_int = 0 if args.noise_base_mode == 'fixed' else 1
            
            # 布尔值转整数
            use_dp_int = 1 if args.use_dp else 0
            print('args.use_dpaaaaaaaaaaaaaaaa', args.use_dp, use_dp_int)
            # print('use_dp_int', use_dp_int)
            use_compression_int = 1 if args.use_compression else 0

            expected_total_rounds = args.expected_total_rounds
            
            # 按照asyncfl.py中init_algo_para参数的顺序设置列表
            train_option['algo_para'] = [
                int(args.buffer_size),                  # buffer_size
                float(args.client_sample_ratio),        # sample_rate
                use_dp_int,                             # use_dp
                float(args.epsilon),                    # epsilon_total
                float(args.delta),                      # delta
                float(args.clip_norm),                  # clip_norm
                float(args.noise_scale),                # noise_scale
                float(args.eta),                        # eta (全局学习率)
                int(args.lr_scheme),                    # lr_scheme
                use_compression_int,                    # use_compression
                compression_method_int,                 # compression_method
                int(args.compression_bits),             # compression_bits
                int(args.history_size),                 # history_size
                int(args.check_interval),               # check_interval
                sensitivity_method_int,                 # sensitivity_method
                int(args.budget_allocation_strategy),   # budget_allocation_strategy
                float(args.max_budget_per_round),       # max_budget_per_round
                int(expected_total_rounds),        # expected_total_rounds
                float(args.noise_decay_rate),           # noise_decay_rate
                float(args.hyperbolic_decay_k),          # hyperbolic_decay_k
                float(args.gamma),                        # gamma
                noise_decay_mode_int,               # noise_decay_mode
                noise_base_mode_int,                # noise_base_mode
                float(args.initial_eplson),              # initial_max_noise
                float(args.max_budget_local_round),              # max_budget_local_round
                int(args.num_clients)                   # num_clients
            ]
        elif args.algorithm.lower() == 'fedprox':
            print("配置FedProx参数...")
            train_option['algo_para'] = [0.1]

        # 初始化模拟器
        print(f"使用模拟器：{args.simulator}")
        if args.simulator == 'StaticUniSimulator':
            Simulator = simulator.StaticUniSimulator
            print("StaticUniSimulator: 静态均匀模拟器，客户端响应时间在初始化时从10分钟到3小时之间均匀随机分配，之后保持固定")
        elif args.simulator == 'DynamicUniSimulator':
            Simulator = simulator.DynamicUniSimulator
            print("DynamicUniSimulator: 动态均匀模拟器，每次更新客户端响应时间都会重新从10分钟到3小时之间均匀随机分配")
        elif args.simulator == 'ResponsivenessExampleSimulator':
            Simulator = simulator.ResponsivenessExampleSimulator
            print("ResponsivenessExampleSimulator: 响应性示例模拟器，客户端响应时间从5到1000之间随机分配")
        elif args.simulator == 'CompletenessExampleSimulator':
            Simulator = simulator.CompletenessExampleSimulator
            print("CompletenessExampleSimulator: 完整性示例模拟器，模拟客户端工作量的完成程度，基于正态分布")
        elif args.simulator == 'AvailabilityExampleSimulator':
            Simulator = simulator.AvailabilityExampleSimulator
            print("AvailabilityExampleSimulator: 可用性示例模拟器，模拟客户端的可用性，第一轮所有客户端可用，之后以0.9的概率可用或不可用")
        elif args.simulator == 'ConnectivityExampleSimulator':
            Simulator = simulator.ConnectivityExampleSimulator
            print("ConnectivityExampleSimulator: 连接性示例模拟器，模拟客户端连接丢包概率，丢包率在0到5%之间均匀分布")
        else:
            train_option['responsiveness'] = 'UNI'
            Simulator = None
            print(f"警告：未知的模拟器类型'{args.simulator}'，将使用默认模拟器")

        # 初始化flgo runner
        print("初始化flgo runner...")
        try:
            # 初始化flgo runner - 使用导入的method替代args.algorithm
            runner = flgo.init(task=task_output_dir, algorithm=method, Simulator=Simulator, option=train_option, Logger=FullLogger) # Simulator=Simulator, , Logger=myLogger  model=model, SimpleLogger

            # 根据命令行参数决定是否进行模型复杂度分析
            # 处理字符串类型的enable_model_analysis参数
            enable_analysis = False
            if hasattr(args, 'enable_model_analysis'):
                if isinstance(args.enable_model_analysis, str):
                    enable_analysis = args.enable_model_analysis.lower() in ['true', '1', 'yes', 'on']
                else:
                    enable_analysis = bool(args.enable_model_analysis)

            if enable_analysis:
                print("\n" + "="*60)
                print("自动模型复杂度分析")
                print("="*60)
                try:
                    # 导入模型复杂度分析函数
                    from flgoasyn.benchmark.idscustom.config import analyze_model_complexity, get_model_complexity_summary

                    # 获取模型实例（从runner中获取或重新创建）
                    if hasattr(runner, 'model') and runner.model is not None:
                        model = runner.model
                    else:
                        # 如果runner中没有模型，从config中获取
                        from flgoasyn.benchmark.idscustom.config import get_model
                        model = get_model()

                    # 构建输入形状
                    batch_size = args.model_analysis_batch_size  # 使用命令行参数指定的批次大小
                    seq_len = int(args.seq_len)
                    input_size = int(args.input_size)
                    input_shape = (batch_size, seq_len, input_size)

                    print(f"模型类型: {args.model_type}")
                    print(f"输入形状: {input_shape}")

                    # 执行模型复杂度分析
                    analysis_result = analyze_model_complexity(
                        model=model,
                        input_shape=input_shape,
                        model_type='auto',  # 自动检测模型类型
                        verbose=True
                    )

                    # 获取并显示简要摘要
                    summary = get_model_complexity_summary(model, input_shape)
                    print(f"\n📋 模型复杂度摘要: {summary}")

                    # 保存分析结果到文件
                    import json
                    analysis_output_dir = os.path.join(task_output_dir, "model_analysis")
                    os.makedirs(analysis_output_dir, exist_ok=True)

                    # 准备可序列化的分析结果
                    serializable_result = {
                        'model_type': args.model_type,
                        'input_shape': input_shape,
                        'parameter_analysis': {
                            'total_params': analysis_result['parameter_analysis']['total_params'],
                            'param_size_mb': analysis_result['parameter_analysis']['param_size_mb'],
                            'trainable_only': analysis_result['parameter_analysis']['trainable_only']
                        },
                        'summary': summary
                    }

                    # 如果有FLOPs分析结果，也保存
                    if analysis_result['flops_analysis'] is not None:
                        serializable_result['flops_analysis'] = {
                            'total_flops': analysis_result['flops_analysis']['total'],
                            'total_mflops': analysis_result['flops_analysis']['total_mflops']
                        }
                        # 如果是稀疏模型，添加稀疏比例
                        if 'sparsity_ratio' in analysis_result['flops_analysis']:
                            serializable_result['flops_analysis']['sparsity_ratio'] = analysis_result['flops_analysis']['sparsity_ratio']

                    # 保存到JSON文件
                    analysis_file = os.path.join(analysis_output_dir, f"model_complexity_{args.model_type}.json")
                    with open(analysis_file, 'w', encoding='utf-8') as f:
                        json.dump(serializable_result, f, indent=2, ensure_ascii=False)

                    print(f"\n💾 模型复杂度分析结果已保存到: {analysis_file}")

                except Exception as e:
                    print(f"⚠️  模型复杂度分析失败: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    print("继续进行联邦学习训练...")

                print("="*60)

            # 开始训练
            print("开始联邦学习训练...")
            runner.run()

            # from flgo.simulator import visualize_simulator
            # visualize_simulator(runner, save=True, together=False,select=[1])


#             import flgo.experiment.analyzer as fea
# # create the analysis plan
#             records = fea.Selector({'task': task_output_dir, 'header':method }).records[task_output_dir]

#             painter = fea.Painter(records)
#             painter.create_figure(fea.Curve, {
#                 'args':{'x':'communication_round', 'y':'test_accuracy'},
#                 'obj_option':{'color':['r', 'b']},
#                 'fig_option':{'title': 'test_accuracy', 'xlabel':'rounds', 'ylabel':'acc'},
#             })
            

            # # 可视化用户活跃度分布
            # import flgo.experiment.analyzer as analyzer
            # selector = analyzer.Selector({'task':task_output_dir, 'header':[args.algorithm]})
            
            # # from flgo.simulator import visualize_latency
            # # visualize_latency(rec0, save=True)
            # def visualize_latency(rec_data, title = ''):
            #     avl_clients = rec_data['available_clients']
            #     all_points_x = []
            #     all_points_y = []
            #     for round in range(len(avl_clients)):
            #         all_points_x.extend([round + 1 for _ in avl_clients[round]])
            #         all_points_y.extend([cid for cid in avl_clients[round]])
            #     import matplotlib.pyplot as plt
            #     plt.scatter(all_points_x, all_points_y, s=10)
            #     plt.title(title)
            #     plt.xlabel('communication round')
            #     plt.ylabel('client ID')
            #     plt.show()

            # rec0 = selector.records[task_output_dir][0]
            # visualize_latency(rec0.data, rec0.name[rec0.name.find('_SIM')+4:rec0.name.find('_SIM')+16])


            # # 保存全局模型
            # model_path = os.path.join(args.output_dir, 'global_model.pth')
            # torch.save(runner.server.model.state_dict(), model_path)
            # print(f"模型已保存到：{model_path}")
            # 
            # # 保存训练指标
            # metrics_path = os.path.join(args.output_dir, 'metrics.npy')
            # np.save(metrics_path, runner.get_metric_by_round())
        except Exception as e:
            print(f"运行过程中发生错误：{str(e)}")
            import traceback
            traceback.print_exc()
            print("请检查task配置路径和模型设置是否正确。")
        
        print("训练和评估完成！")
    except Exception as e:
        print(f"程序执行过程中发生未处理的异常：{str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main() 



# 示例使用方法：
# python ids/main.py --algorithm asyncFL --data_path data/car_hacking.csv --data_name car_hacking_v1 --use_dp
# python flgoasyn/main.py --benchmark_name my_custom_benchmark --partitioner_name DirichletPartitioner --dirichlet_alpha 0.1 --eval_interval 5 --optimizer adam --num_rounds 200

# 使用不同模拟器的示例：
# python flgoasyn/main.py --data_name toniot --partitioner_name IIDPartitioner --algorithm fedavg --simulator StaticUniSimulator
# python flgoasyn/main.py --data_name toniot --partitioner_name DirichletPartitioner --algorithm fedavg --simulator DynamicUniSimulator --dirichlet_alpha 0.3
# python flgoasyn/main.py --data_name toniot --partitioner_name IIDPartitioner --algorithm fedavg --simulator ResponsivenessExampleSimulator
# python flgoasyn/main.py --data_name toniot --partitioner_name IIDPartitioner --algorithm fedavg --simulator CompletenessExampleSimulator
# python flgoasyn/main.py --data_name toniot --partitioner_name IIDPartitioner --algorithm fedavg --simulator AvailabilityExampleSimulator
# python flgoasyn/main.py --data_name toniot --partitioner_name IIDPartitioner --algorithm fedavg --simulator ConnectivityExampleSimulator