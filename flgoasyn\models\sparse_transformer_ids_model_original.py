"""
稀疏Transformer入侵检测模型。
仅使用稀疏多头注意力Transformer进行序列处理和分类，用于车联网场景的入侵检测。
"""
from typing import Tuple, Optional, Dict, Any, List

import torch
import torch.nn as nn
import torch.nn.functional as F

from .sparse_transformer import SparseMultiheadAttention


class TransformerEncoderLayer(nn.Module):
    """增强版Transformer编码器层，使用稀疏多头注意力机制和增强的前馈网络。
    
    Attributes:
        d_model (int): 模型维度
        nhead (int): 注意力头数
        dim_feedforward (int): 前馈网络隐藏层维度
        dropout (float): Dropout概率
        k_ratio (float): 稀疏注意力的Top-K比例
        self_attn (SparseMultiheadAttention): 稀疏多头自注意力层
        linear1 (nn.Linear): 前馈网络第一层
        dropout1 (nn.Dropout): 自注意力后的Dropout
        linear2 (nn.Linear): 前馈网络第二层
        dropout2 (nn.Dropout): 前馈网络后的Dropout
        norm1 (nn.LayerNorm): 第一个层归一化
        norm2 (nn.LayerNorm): 第二个层归一化
        activation (callable): 激活函数
        use_glu (bool): 是否使用门控线性单元
    """
    
    def __init__(
        self, 
        d_model: int, 
        nhead: int, 
        dim_feedforward: int = 2048, 
        dropout: float = 0.1, 
        k_ratio: float = 0.4, 
        activation: str = "relu",
        use_glu: bool = True,
        pre_norm: bool = True
    ) -> None:
        """初始化增强版Transformer编码器层。
        
        Args:
            d_model: 模型维度
            nhead: 注意力头数
            dim_feedforward: 前馈网络隐藏层维度
            dropout: Dropout概率
            k_ratio: 稀疏注意力的Top-K比例
            activation: 激活函数，'relu'或'gelu'
            use_glu: 是否使用门控线性单元，默认为True
            pre_norm: 是否使用Pre-LN范式，默认为True
        """
        super(TransformerEncoderLayer, self).__init__()
        
        self.d_model = d_model
        self.nhead = nhead
        self.dim_feedforward = dim_feedforward
        self.dropout = dropout
        self.k_ratio = k_ratio
        self.pre_norm = pre_norm
        self.use_glu = use_glu
        
        # 稀疏多头自注意力层
        self.self_attn = SparseMultiheadAttention(
            embed_dim=d_model,
            num_heads=nhead,
            dropout=dropout,
            k_ratio=k_ratio
        )
        
        # 前馈网络
        if use_glu:
            # 使用门控线性单元增强前馈网络
            self.linear1 = nn.Linear(d_model, dim_feedforward * 2)  # 2倍宽度用于GLU
            self.dropout1 = nn.Dropout(dropout)
            self.linear2 = nn.Linear(dim_feedforward, d_model)
        else:
            # 标准前馈网络
            self.linear1 = nn.Linear(d_model, dim_feedforward)
            self.dropout1 = nn.Dropout(dropout)
            self.linear2 = nn.Linear(dim_feedforward, d_model)
        
        self.dropout2 = nn.Dropout(dropout)
        
        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # 激活函数
        self.activation = F.gelu if activation == "gelu" else F.relu
        
    def forward(
        self, 
        src: torch.Tensor, 
        src_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """前向传播。
        
        Args:
            src: 输入序列，形状为 [batch_size, seq_len, d_model]
            src_mask: 源序列掩码，形状为 [batch_size, 1, seq_len, seq_len]
            
        Returns:
            tuple: (输出序列, 注意力权重)
                - 输出序列形状: [batch_size, seq_len, d_model]
                - 注意力权重形状: [batch_size, nhead, seq_len, seq_len]
        """
        # Pre-LN或Post-LN范式
        if self.pre_norm:
            # Pre-LN: 先归一化再注意力
            normed_src = self.norm1(src)
            src2, attn_weights = self.self_attn(normed_src, normed_src, normed_src, src_mask)
            src = src + self.dropout1(src2)
            
            # 先归一化再前馈
            normed_src = self.norm2(src)
            if self.use_glu:
                # 门控线性单元处理
                ffn_output = self.linear1(normed_src)
                ffn_output = F.glu(ffn_output, dim=-1)
                ffn_output = self.dropout1(ffn_output)
                ffn_output = self.linear2(ffn_output)
            else:
                # 标准前馈网络
                ffn_output = self.linear2(self.dropout1(self.activation(self.linear1(normed_src))))
            
            src = src + self.dropout2(ffn_output)
        else:
            # Post-LN: 先注意力再归一化
            src2, attn_weights = self.self_attn(src, src, src, src_mask)
            src = self.norm1(src + self.dropout1(src2))
            
            if self.use_glu:
                # 门控线性单元处理
                ffn_output = self.linear1(src)
                ffn_output = F.glu(ffn_output, dim=-1)
                ffn_output = self.dropout1(ffn_output)
                ffn_output = self.linear2(ffn_output)
            else:
                # 标准前馈网络
                ffn_output = self.linear2(self.dropout1(self.activation(self.linear1(src))))
            
            src = self.norm2(src + self.dropout2(ffn_output))
        
        return src, attn_weights


class MultiScaleAttention(nn.Module):
    """多尺度注意力模块，使用不同窗口大小的自注意力捕获不同尺度的特征。
    
    Attributes:
        d_model (int): 模型维度
        nhead (int): 注意力头数
        window_sizes (list): 注意力窗口大小列表
        dropout (float): Dropout概率
        k_ratio (float): 稀疏注意力的Top-K比例
    """
    
    def __init__(
        self,
        d_model: int,
        nhead: int,
        window_sizes: List[int] = [3, 5, 7],
        dropout: float = 0.1,
        k_ratio: float = 0.6
    ) -> None:
        """初始化多尺度注意力模块。
        
        Args:
            d_model: 模型维度
            nhead: 每个窗口的注意力头数
            window_sizes: 注意力窗口大小列表，默认为[3, 5, 7]
            dropout: Dropout概率
            k_ratio: 稀疏注意力的Top-K比例
        """
        super(MultiScaleAttention, self).__init__()
        
        self.d_model = d_model
        self.nhead = nhead
        self.window_sizes = window_sizes
        
        # 每个窗口大小对应一个自注意力层
        self.attention_layers = nn.ModuleList([
            SparseMultiheadAttention(
                embed_dim=d_model,
                num_heads=nhead,
                dropout=dropout,
                k_ratio=k_ratio
            ) for _ in window_sizes
        ])
        
        # 融合不同尺度的注意力输出
        self.fusion = nn.Linear(d_model * len(window_sizes), d_model)
        self.norm = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def _get_window_mask(self, seq_len: int, window_size: int, device: torch.device) -> torch.Tensor:
        """生成局部窗口注意力掩码。
        
        Args:
            seq_len: 序列长度
            window_size: 窗口大小
            device: 设备
            
        Returns:
            torch.Tensor: 窗口注意力掩码，形状为 [seq_len, seq_len]
        """
        # 创建距离矩阵
        i, j = torch.meshgrid(torch.arange(seq_len, device=device), torch.arange(seq_len, device=device))
        distance = torch.abs(i - j)
        
        # 窗口内为1，窗口外为0
        window_mask = (distance <= window_size // 2).float()
        
        # 转换为注意力掩码格式：窗口内为0，窗口外为-inf
        attention_mask = (1.0 - window_mask) * -10000.0
        
        return attention_mask
    
    def forward(
        self,
        x: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """前向传播。
        
        Args:
            x: 输入序列，形状为 [batch_size, seq_len, d_model]
            attention_mask: 注意力掩码，形状为 [batch_size, 1, seq_len, seq_len]
            
        Returns:
            tuple: (输出序列, 注意力权重列表)
                - 输出序列形状: [batch_size, seq_len, d_model]
                - 注意力权重列表，每个元素形状为 [batch_size, nhead, seq_len, seq_len]
        """
        batch_size, seq_len, _ = x.size()
        device = x.device
        
        # 存储不同窗口大小的注意力输出
        outputs = []
        attention_weights = []
        
        # 对每个窗口大小应用自注意力
        for i, (window_size, attn_layer) in enumerate(zip(self.window_sizes, self.attention_layers)):
            # 创建窗口注意力掩码
            window_mask = self._get_window_mask(seq_len, window_size, device)
            window_mask = window_mask.unsqueeze(0).unsqueeze(1).expand(batch_size, 1, seq_len, seq_len)
            
            # 组合窗口掩码和输入掩码
            if attention_mask is not None:
                combined_mask = window_mask + attention_mask
            else:
                combined_mask = window_mask
            
            # 应用自注意力
            output, attn_weight = attn_layer(x, x, x, combined_mask)
            outputs.append(output)
            attention_weights.append(attn_weight)
        
        # 拼接不同窗口的输出
        concat_output = torch.cat(outputs, dim=2)
        
        # 融合不同尺度的特征
        fused_output = self.fusion(concat_output)
        fused_output = self.dropout(fused_output)
        fused_output = self.norm(fused_output + x)  # 残差连接
        
        return fused_output, attention_weights


class SparseTransformerIDSModel(nn.Module):
    """增强版稀疏Transformer入侵检测模型，使用多层Transformer和多尺度注意力。
    
    改进的入侵检测模型架构：
    数据预处理 → 输入投影 → 多尺度注意力 → 多层Transformer（Pre-LN + GLU + 稀疏注意力）→ 分类
    
    Attributes:
        input_size (int): 输入特征维度
        d_model (int): Transformer模型维度
        nhead (int): 注意力头数
        num_encoder_layers (int): Transformer编码器层数
        dim_feedforward (int): 前馈网络隐藏层维度
        num_classes (int): 分类类别数
        dropout (float): Dropout概率
        k_ratio (float): 稀疏注意力的Top-K比例
        use_glu (bool): 是否使用门控线性单元
        pre_norm (bool): 是否使用Pre-LN范式
        use_multi_scale (bool): 是否使用多尺度注意力
        input_projection (nn.Sequential): 输入投影层
        multi_scale_attention (MultiScaleAttention): 多尺度注意力模块
        transformer_layers (nn.ModuleList): Transformer编码器层列表
        layer_norm (nn.LayerNorm): 输出层归一化
        classifier (nn.Sequential): 分类器
    """
    
    def __init__(
        self, 
        input_size: int, 
        d_model: int = 128, 
        nhead: int = 8,  # 增加头数以捕获更复杂的特征关系
        num_encoder_layers: int = 4,  # 增加层数以提高模型容量
        dim_feedforward: int = 512, 
        num_classes: int = 2, 
        dropout: float = 0.2,  # 增加dropout以减少过拟合
        k_ratio: float = 0.6,  # 增加稀疏注意力比例以保留更多信息
        use_glu: bool = True,  # 使用GLU增强表达能力
        pre_norm: bool = True,  # 使用Pre-LN提高训练稳定性
        use_multi_scale: bool = True,  # 使用多尺度注意力
        window_sizes: List[int] = [3, 5, 7]  # 多尺度注意力的窗口大小
    ) -> None:
        """初始化增强版稀疏Transformer入侵检测模型。
        
        Args:
            input_size: 输入特征维度
            d_model: Transformer模型维度
            nhead: 注意力头数
            num_encoder_layers: Transformer编码器层数
            dim_feedforward: 前馈网络隐藏层维度
            num_classes: 分类类别数
            dropout: Dropout概率
            k_ratio: 稀疏注意力的Top-K比例
            use_glu: 是否使用门控线性单元，默认为True
            pre_norm: 是否使用Pre-LN范式，默认为True
            use_multi_scale: 是否使用多尺度注意力，默认为True
            window_sizes: 多尺度注意力的窗口大小列表，默认为[3, 5, 7]
        """
        super(SparseTransformerIDSModel, self).__init__()
        
        # 确保 d_model 能被 nhead 整除
        assert d_model % nhead == 0, f"d_model({d_model})必须能被nhead({nhead})整除"
        
        self.input_size = input_size
        self.d_model = d_model
        self.nhead = nhead
        self.num_encoder_layers = num_encoder_layers
        self.dim_feedforward = dim_feedforward
        self.num_classes = num_classes
        self.dropout = dropout
        self.k_ratio = k_ratio
        self.use_glu = use_glu
        self.pre_norm = pre_norm
        self.use_multi_scale = use_multi_scale
        
        # 输入投影层，将输入特征维度映射到模型维度
        self.input_projection = nn.Sequential(
            nn.Linear(input_size, d_model),
            nn.LayerNorm(d_model),
            nn.Dropout(dropout)
        )
        
        # 多尺度注意力模块（如果启用）
        self.multi_scale_attention = None
        if use_multi_scale:
            self.multi_scale_attention = MultiScaleAttention(
                d_model=d_model,
                nhead=nhead // 2,  # 减少每个窗口的头数以控制参数量
                window_sizes=window_sizes,
                dropout=dropout,
                k_ratio=k_ratio
            )
        
        # Transformer编码器层
        self.transformer_layers = nn.ModuleList([
            TransformerEncoderLayer(
                d_model=d_model,
                nhead=nhead,
                dim_feedforward=dim_feedforward,
                dropout=dropout,
                k_ratio=k_ratio,
                use_glu=use_glu,
                pre_norm=pre_norm
            ) for _ in range(num_encoder_layers)
        ])
        
        # 输出层归一化
        self.layer_norm = nn.LayerNorm(d_model)
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, num_classes)
        )
        
        # 初始化参数
        self._init_parameters()
        
    def _init_parameters(self) -> None:
        """初始化模型参数。"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(
        self, 
        x: torch.Tensor, 
        attention_mask: Optional[torch.Tensor] = None
    ) -> Dict[str, torch.Tensor]:
        """前向传播。
        
        Args:
            x: 输入序列，形状为 [batch_size, seq_len, input_size]
            attention_mask: 注意力掩码，形状为 [batch_size, seq_len]
                1表示有效位置，0表示填充位置
            
        Returns:
            dict: 包含以下键:
                'logits': 分类logits，形状为 [batch_size, num_classes]
                'attention_weights': 最后一层的注意力权重，
                    形状为 [batch_size, nhead, seq_len, seq_len]
        """
        batch_size, seq_len = x.size(0), x.size(1)
        
        # 转换attention_mask为Transformer需要的格式
        if attention_mask is not None:
            # 将mask从[batch_size, seq_len]转换为[batch_size, 1, 1, seq_len]
            attention_mask = attention_mask.unsqueeze(1).unsqueeze(2)
            # 将0转换为-inf，1保持不变
            attention_mask = (1.0 - attention_mask) * -10000.0
        
        # 输入投影
        src = self.input_projection(x)
        
        # 多尺度注意力处理（如果启用）
        multi_scale_attn_weights = None
        if self.use_multi_scale and self.multi_scale_attention is not None:
            src, multi_scale_attn_weights = self.multi_scale_attention(src, attention_mask)
        
        # 存储每层的特征和注意力权重
        layer_features = [src]
        attention_weights_list = []
        
        # 依次通过Transformer编码器层
        for layer in self.transformer_layers:
            src, attn_weights = layer(src, attention_mask)
            layer_features.append(src)
            attention_weights_list.append(attn_weights)
        
        # 应用输出层归一化
        src = self.layer_norm(src)
        
        # 获取序列的表示（使用平均池化）
        # 如果有attention_mask，只对有效位置进行平均
        if attention_mask is not None:
            # 转换mask为[batch_size, seq_len]，1表示有效，0表示填充
            # 原始的 attention_mask 是 [batch_size, 1, 1, seq_len]，其中 0 表示有效，-10000.0 表示填充
            mask = (attention_mask.squeeze(1).squeeze(1) == 0).float()
            # 计算有效长度（用于平均）
            valid_lengths = torch.sum(mask, dim=1, keepdim=True)
            # 确保 valid_lengths 不为 0，避免除零错误
            valid_lengths = torch.clamp(valid_lengths, min=1.0)
            # 对有效位置求和后除以有效长度
            pooled = torch.sum(src * mask.unsqueeze(-1), dim=1) / valid_lengths
        else:
            # 如果没有mask，对整个序列进行平均
            pooled = torch.mean(src, dim=1)
        
        # 分类
        logits = self.classifier(pooled)
        
        return {
            'logits': logits,
            'attention_weights': attention_weights_list[-1],  # 返回最后一层的注意力权重
            'multi_scale_attention_weights': multi_scale_attn_weights  # 返回多尺度注意力权重（如果有）
        }
        
    def predict(self, x: torch.Tensor, attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """预测样本的类别。
        
        Args:
            x: 输入序列，形状为 [batch_size, seq_len, input_size]
            attention_mask: 注意力掩码，形状为 [batch_size, seq_len]
        
        Returns:
            torch.Tensor: 预测的类别索引，形状为 [batch_size]
        """
        outputs = self.forward(x, attention_mask)
        return torch.argmax(outputs['logits'], dim=1) 