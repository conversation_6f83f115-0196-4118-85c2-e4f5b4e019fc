import os
import argparse
import subprocess
import sys

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='车联网联邦学习入侵检测运行脚本')
    
    parser.add_argument('--generate_data', action='store_true',
                        help='是否生成示例数据')
    parser.add_argument('--num_samples', type=int, default=10000,
                        help='生成的样本数量')
    parser.add_argument('--num_features', type=int, default=10,
                        help='生成的特征数量')
    parser.add_argument('--num_classes', type=int, default=2,
                        help='生成的类别数量')
    
    # 添加main.py的参数
    parser.add_argument('--data_path', type=str, default='data/sample_cicids.csv',
                        help='数据集路径')
    parser.add_argument('--num_vehicles', type=int, default=20,
                        help='车辆数量')
    parser.add_argument('--num_edges', type=int, default=3,
                        help='边缘服务器数量')
    parser.add_argument('--num_rounds', type=int, default=20,
                        help='全局训练轮数')
    parser.add_argument('--gpu', type=int, default=-1,
                        help='GPU ID，-1表示使用CPU')
    parser.add_argument('--edge_aggregation_interval', type=int, default=5,
                        help='边缘服务器全局聚合间隔（轮次，仅对FedBuff生效）')
    
    # 模型参数
    parser.add_argument('--model_type', type=str, default='transformer',
                        choices=['transformer', 'cnn_transformer'],
                        help='全局特征提取器架构类型(transformer: 纯Transformer, cnn_transformer: CNN+Transformer混合)')
    
    # FLGO参数
    parser.add_argument('--partition_type', type=str, default='dirichlet',
                        choices=['dirichlet', 'shards', 'iid'],
                        help='FLGO数据分区类型')
    
    # 算法选择
    parser.add_argument('--algorithm', type=str, default='fedbuff',
                        choices=['fedbuff', 'fedavg'],
                        help='联邦学习算法选择')
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 确保目录存在
    os.makedirs('data', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    os.makedirs('output', exist_ok=True)
    
    # 如果需要生成示例数据
    if args.generate_data:
        print("生成示例数据...")
        data_cmd = [
            sys.executable,
            'data/generate_sample_data.py',
        ]
        subprocess.run(data_cmd, check=True)
    
    # 运行主程序
    print(f"运行车联网联邦学习入侵检测系统... (使用{args.algorithm.upper()}算法, {args.model_type}架构)")
    main_cmd = [
        sys.executable,
        'main.py',
        f'--data_path={args.data_path}',
        f'--num_vehicles={args.num_vehicles}',
        f'--num_edges={args.num_edges}',
        f'--num_rounds={args.num_rounds}',
        f'--gpu={args.gpu}',
        f'--partition_type={args.partition_type}',
        f'--algorithm={args.algorithm}',
        f'--model_type={args.model_type}',
        f'--edge_aggregation_interval={args.edge_aggregation_interval}'
    ]
    
    subprocess.run(main_cmd, check=True)
    
    print("运行完成！")

if __name__ == "__main__":
    main() 




#python run.py --algorithm fedavg --model_type transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50
#python run.py --algorithm fedavg --model_type cnn_transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50
#python run.py --algorithm fedbuff --model_type transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50 --edge_aggregation_interval 10
#python run.py --algorithm fedbuff --model_type cnn_transformer --partition_type=dirichlet --num_vehicles 20 --num_edges 3 --num_rounds 50 --edge_aggregation_interval 10