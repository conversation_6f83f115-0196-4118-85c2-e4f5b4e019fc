import torch
import copy
import numpy as np
from loguru import logger

import flgo.algorithm.fedavg as fedavg
import flgo.algorithm.fedbuff as fedbuff
# import flgo.algorithm.fedasync as fedasync
from flgo.algorithm.fedavg import Server as FedAvgServer
from flgo.algorithm.fedbuff import Server as AsyncServer
from flgo.algorithm.fedbuff import Client

class IOVFedBuffClient(Client):
    """车联网FedBuff客户端 - 基于flgo实现"""
    
    def __init__(self, client_id, model, dataloader, optimizer, criterion, device='cpu'):
        """
        初始化车联网FedBuff客户端
        
        参数:
            client_id: 客户端ID
            model: 本地模型
            dataloader: 数据加载器
            optimizer: 优化器
            criterion: 损失函数
            device: 计算设备
        """
        # 没有直接调用父类的__init__，因为flgo的Client初始化方式与我们的需求不完全一致
        self.client_id = client_id
        self.model = model
        self.dataloader = dataloader
        self.optimizer = optimizer
        self.criterion = criterion
        self.device = device
        
        # 当前轮次
        self.current_round = 0
        
        # 当前边缘服务器
        self.current_edge_server = None
        
        # 数据大小
        self.data_size = len(dataloader.dataset)
        
        logger.info(f"车辆 {client_id} 初始化完成，数据大小={self.data_size}")
    
    def connect_to_edge(self, edge_server):
        """
        连接到边缘服务器
        
        参数:
            edge_server: 边缘服务器
        """
        # 如果已连接到其他边缘服务器，先断开连接
        if self.current_edge_server is not None:
            self.disconnect_from_edge()
            
        # 连接到新的边缘服务器
        self.current_edge_server = edge_server
        edge_server.add_vehicle(self.client_id)
        
        # 获取全局特征提取器
        global_extractor = edge_server.global_model.global_extractor
        self.model.set_global_extractor(global_extractor)
        
        logger.info(f"车辆 {self.client_id} 连接到边缘服务器 {edge_server.edge_id}")
    
    def disconnect_from_edge(self):
        """断开与当前边缘服务器的连接"""
        if self.current_edge_server is not None:
            self.current_edge_server.remove_vehicle(self.client_id)
            logger.info(f"车辆 {self.client_id} 断开与边缘服务器 {self.current_edge_server.edge_id} 的连接")
            self.current_edge_server = None
    
    def train(self, num_epochs=1):
        """
        训练本地模型
        
        参数:
            num_epochs: 训练轮数
            
        返回:
            训练损失和准确率
        """
        if self.current_edge_server is None:
            logger.warning(f"车辆 {self.client_id} 未连接到边缘服务器，无法训练")
            return None, None
            
        self.model.train()
        total_loss = 0
        correct = 0
        total = 0
        
        for epoch in range(num_epochs):
            epoch_loss = 0
            epoch_correct = 0
            epoch_total = 0
            
            for batch_idx, (data, target) in enumerate(self.dataloader):
                data, target = data.to(self.device), target.to(self.device)
                
                # 确保数据是3D的 [batch_size, seq_len, input_dim]
                if len(data.shape) == 2:
                    data = data.unsqueeze(1)  # 添加序列维度
                
                self.optimizer.zero_grad()
                output, gate_weights = self.model(data)
                loss = self.criterion(output, target)
                loss.backward()
                self.optimizer.step()
                
                epoch_loss += loss.item() * data.size(0)
                pred = output.argmax(dim=1, keepdim=True)
                epoch_correct += pred.eq(target.view_as(pred)).sum().item()
                epoch_total += data.size(0)
                
                # 记录门控权重
                local_weight = gate_weights[:, 0].mean().item()
                global_weight = gate_weights[:, 1].mean().item()
                
                if batch_idx % 10 == 0:
                    logger.info(f"车辆 {self.client_id} 训练: 轮次 {epoch+1}/{num_epochs}, 批次 {batch_idx}/{len(self.dataloader)}, "
                               f"损失: {loss.item():.4f}, 准确率: {100. * epoch_correct / epoch_total:.2f}%, "
                               f"门控权重: 本地={local_weight:.4f}, 全局={global_weight:.4f}")
            
            total_loss += epoch_loss
            correct += epoch_correct
            total += epoch_total
            
            logger.info(f"车辆 {self.client_id} 训练: 轮次 {epoch+1}/{num_epochs} 完成, "
                       f"损失: {epoch_loss / epoch_total:.4f}, 准确率: {100. * epoch_correct / epoch_total:.2f}%")
        
        # 更新当前轮次
        self.current_round += 1
        
        return total_loss / total, correct / total
    
    def evaluate(self, test_dataloader):
        """
        评估模型
        
        参数:
            test_dataloader: 测试数据加载器
            
        返回:
            测试损失和准确率
        """
        self.model.eval()
        test_loss = 0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in test_dataloader:
                data, target = data.to(self.device), target.to(self.device)
                
                # 确保数据是3D的 [batch_size, seq_len, input_dim]
                if len(data.shape) == 2:
                    data = data.unsqueeze(1)  # 添加序列维度
                
                output, _ = self.model(data)
                test_loss += self.criterion(output, target).item() * data.size(0)
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += data.size(0)
        
        test_loss /= total
        accuracy = correct / total
        
        logger.info(f"车辆 {self.client_id} 评估: 损失: {test_loss:.4f}, 准确率: {100. * accuracy:.2f}%")
        
        return test_loss, accuracy
    
    def get_model_update(self):
        """
        获取模型更新
        
        返回:
            模型更新 (OrderedDict)
        """
        # 只提取全局特征提取器的参数
        model_update = {}
        
        for name, param in self.model.named_parameters():
            if 'global_extractor' in name and param.requires_grad:
                model_update[name] = param.data.clone().to('cpu')
                
        return model_update
    
    def upload_model(self):
        """
        上传模型到边缘服务器
        
        返回:
            是否触发聚合
        """
        if self.current_edge_server is None:
            logger.warning(f"车辆 {self.client_id} 未连接到边缘服务器，无法上传模型")
            return False
            
        # 获取模型更新
        model_update = self.get_model_update()
        
        # 检查更新是否应该被接受
        if not self.current_edge_server.should_update(self.client_id, model_update):
            logger.info(f"车辆 {self.client_id} 的更新被拒绝，不进行上传")
            return False
            
        # 上传模型更新
        triggered = self.current_edge_server.add_client_update(
            self.client_id, model_update, self.current_round, self.data_size
        )
        
        logger.info(f"车辆 {self.client_id} 上传模型到边缘服务器 {self.current_edge_server.edge_id}")
        
        return triggered

class IOVFedBuffServer(AsyncServer):
    """边缘服务器 - 管理一组车辆客户端，使用FLGO实现"""
    
    def __init__(self, global_model, buffer_size=5, staleness_threshold=3, device='cpu', edge_id=None):
        """
        初始化边缘服务器
        
        参数:
            global_model: 全局模型
            buffer_size: 缓冲区大小
            staleness_threshold: 过时阈值
            device: 计算设备
            edge_id: 边缘服务器ID
        """
        # 调用flgo的Server初始化
        # 由于接口不完全匹配，这里不调用super().__init__
        self.global_model = global_model
        self.buffer_size = buffer_size
        self.staleness_threshold = staleness_threshold
        self.device = device
        self.edge_id = edge_id
        
        # 初始化缓冲区
        self.buffer = []
        self.max_buffer_size = buffer_size
        
        # 当前全局轮次
        self.current_round = 0
        
        # 记录每个客户端的最后一次更新轮次
        self.client_last_round = {}
        
        # 记录每个客户端的数据量
        self.client_data_sizes = {}
        
        # 管理的车辆集合
        self.vehicles = set()
        
        logger.info(f"边缘服务器 {edge_id} 初始化完成，缓冲区大小={buffer_size}，过时阈值={staleness_threshold}")
        
    def add_vehicle(self, vehicle_id):
        """添加车辆到边缘服务器"""
        self.vehicles.add(vehicle_id)
        logger.info(f"边缘服务器 {self.edge_id} 添加车辆 {vehicle_id}，当前管理 {len(self.vehicles)} 辆车")
    
    def remove_vehicle(self, vehicle_id):
        """从边缘服务器移除车辆"""
        if vehicle_id in self.vehicles:
            self.vehicles.remove(vehicle_id)
            logger.info(f"边缘服务器 {self.edge_id} 移除车辆 {vehicle_id}，当前管理 {len(self.vehicles)} 辆车")
    
    def get_managed_vehicles(self):
        """获取当前管理的车辆列表"""
        return list(self.vehicles)
    
    def add_client_update(self, client_id, model_update, client_round, data_size):
        """添加客户端更新到缓冲区"""
        # 调用flgo的Server的接收更新方法
        # 检查更新是否过时
        staleness = self.current_round - client_round
        if staleness > self.staleness_threshold:
            logger.warning(f"客户端 {client_id} 的更新过时 (staleness={staleness})，丢弃更新")
            return False
            
        # 记录客户端的最后一次更新轮次
        self.client_last_round[client_id] = client_round
        
        # 记录客户端的数据量
        self.client_data_sizes[client_id] = data_size
        
        # 添加更新到缓冲区
        self.buffer.append((client_id, model_update, client_round, data_size))
        logger.info(f"添加客户端 {client_id} 的更新到缓冲区，当前缓冲区大小: {len(self.buffer)}/{self.max_buffer_size}")
        
        # 检查是否需要聚合
        if len(self.buffer) >= self.max_buffer_size:
            logger.info(f"缓冲区已满，触发聚合")
            self.aggregate()
            return True
            
        return False
    
    def aggregate(self):
        """聚合缓冲区中的更新"""
        # 调用flgo的Server的聚合方法
        if not self.buffer:
            logger.warning("缓冲区为空，无法聚合")
            return
            
        # 计算权重
        total_data_size = sum(data_size for _, _, _, data_size in self.buffer)
        weights = [data_size / total_data_size for _, _, _, data_size in self.buffer]
        
        # 聚合更新
        aggregated_update = {}
        
        # 初始化聚合更新
        for name, param in self.global_model.named_parameters():
            if param.requires_grad:
                aggregated_update[name] = torch.zeros_like(param.data)
                
        # 加权聚合
        for i, (client_id, model_update, _, _) in enumerate(self.buffer):
            for name, param in model_update.items():
                if name in aggregated_update:
                    aggregated_update[name] += weights[i] * param
        
        # 更新全局模型
        with torch.no_grad():
            for name, param in self.global_model.named_parameters():
                if name in aggregated_update and param.requires_grad:
                    param.data = aggregated_update[name].to(self.device)
        
        # 清空缓冲区
        self.buffer = []
        
        # 更新全局轮次
        self.current_round += 1
        
        logger.info(f"边缘服务器 {self.edge_id} 完成第 {self.current_round} 轮聚合，聚合了 {len(weights)} 个客户端的更新")
    
    def should_update(self, client_id, model_update, similarity_threshold=0.9):
        """
        判断客户端的更新是否应该被接受
        
        参数:
            client_id: 客户端ID
            model_update: 模型更新
            similarity_threshold: 相似度阈值，超过此阈值的更新将被接受
            
        返回:
            是否应该接受更新
        """
        # 如果客户端是第一次更新，直接接受
        if client_id not in self.client_last_round:
            return True
            
        # 计算更新与全局模型的相似度
        similarity = self._compute_update_similarity(model_update)
        
        # 如果相似度低于阈值，接受更新
        if similarity < similarity_threshold:
            logger.info(f"客户端 {client_id} 的更新与全局模型相似度为 {similarity:.4f}，接受更新")
            return True
        else:
            logger.info(f"客户端 {client_id} 的更新与全局模型相似度为 {similarity:.4f}，拒绝更新")
            return False
            
    def _compute_update_similarity(self, model_update):
        """
        计算更新与全局模型的相似度
        
        参数:
            model_update: 模型更新
            
        返回:
            相似度 (0-1)
        """
        # 计算余弦相似度
        dot_product = 0
        global_norm = 0
        update_norm = 0
        
        for name, param in self.global_model.named_parameters():
            if name in model_update and param.requires_grad:
                global_param = param.data.view(-1)
                update_param = model_update[name].view(-1)
                
                dot_product += torch.sum(global_param * update_param).item()
                global_norm += torch.sum(global_param ** 2).item()
                update_norm += torch.sum(update_param ** 2).item()
                
        if global_norm == 0 or update_norm == 0:
            return 0
            
        similarity = dot_product / (np.sqrt(global_norm) * np.sqrt(update_norm))
        return abs(similarity)  # 取绝对值，确保结果在0-1之间

class IOVCloudServer:
    """云服务器 - 管理多个边缘服务器"""
    
    def __init__(self, global_model, device='cpu'):
        self.global_model = global_model
        self.device = device
        self.edge_servers = {}  # 边缘服务器字典
        self.current_round = 0
        
    def add_edge_server(self, edge_id, edge_server):
        """添加边缘服务器"""
        self.edge_servers[edge_id] = edge_server
        logger.info(f"云服务器添加边缘服务器 {edge_id}，当前管理 {len(self.edge_servers)} 个边缘服务器")
    
    def aggregate_edge_models(self):
        """聚合所有边缘服务器的模型"""
        if not self.edge_servers:
            logger.warning("没有边缘服务器，无法聚合")
            return
            
        # 获取每个边缘服务器管理的车辆数量作为权重
        edge_weights = {}
        total_vehicles = 0
        
        for edge_id, edge_server in self.edge_servers.items():
            num_vehicles = len(edge_server.get_managed_vehicles())
            edge_weights[edge_id] = num_vehicles
            total_vehicles += num_vehicles
            
        # 归一化权重
        if total_vehicles > 0:
            for edge_id in edge_weights:
                edge_weights[edge_id] /= total_vehicles
        else:
            # 如果没有车辆，使用均匀权重
            for edge_id in edge_weights:
                edge_weights[edge_id] = 1.0 / len(self.edge_servers)
                
        # 聚合模型
        aggregated_model = {}
        
        # 初始化聚合模型
        for name, param in self.global_model.named_parameters():
            if param.requires_grad:
                aggregated_model[name] = torch.zeros_like(param.data)
                
        # 加权聚合
        for edge_id, edge_server in self.edge_servers.items():
            weight = edge_weights[edge_id]
            for name, param in edge_server.global_model.named_parameters():
                if name in aggregated_model and param.requires_grad:
                    aggregated_model[name] += weight * param.data.to(self.device)
        
        # 更新全局模型
        with torch.no_grad():
            for name, param in self.global_model.named_parameters():
                if name in aggregated_model and param.requires_grad:
                    param.data = aggregated_model[name]
        
        # 更新全局轮次
        self.current_round += 1
        
        logger.info(f"云服务器完成第 {self.current_round} 轮聚合，聚合了 {len(self.edge_servers)} 个边缘服务器的模型")
        
        # 将全局模型分发到各边缘服务器
        self.distribute_global_model()
    
    def distribute_global_model(self):
        """将全局模型分发到各边缘服务器"""
        for edge_id, edge_server in self.edge_servers.items():
            # 只更新全局特征提取器部分
            for name, param in self.global_model.named_parameters():
                if 'global_extractor' in name and param.requires_grad:
                    with torch.no_grad():
                        edge_server.global_model.get_parameter(name).data = param.data.clone()
            
            logger.info(f"将全局模型分发到边缘服务器 {edge_id}")
    
    def get_global_model(self):
        """获取全局模型"""
        return copy.deepcopy(self.global_model) 