"""
LSTM编码器模块，用于特征提取。
"""
from typing import Tuple, Optional

import torch
import torch.nn as nn
from opacus.layers import DPLSTM  # 

class LSTMEncoder(nn.Module):
    """LSTM编码器，用于对输入序列进行特征编码。
    
    对于车联网环境中的时序数据，提取时序特征并转换为向量表示，
    为后续的Transformer模块提供输入。
    
    Attributes:
        input_size (int): 输入特征维度
        hidden_size (int): LSTM隐藏层维度
        num_layers (int): LSTM层数
        dropout (float): Dropout概率
        bidirectional (bool): 是否使用双向LSTM
        lstm (nn.LSTM): LSTM模型
        output_size (int): 输出特征维度
    """
    
    def __init__(
        self, 
        input_size: int, 
        hidden_size: int, 
        num_layers: int = 2, 
        dropout: float = 0.1, 
        bidirectional: bool = True
    ) -> None:
        """初始化LSTM编码器。
        
        Args:
            input_size: 输入特征维度
            hidden_size: LSTM隐藏层维度
            num_layers: LSTM层数
            dropout: Dropout概率
            bidirectional: 是否使用双向LSTM
        """
        super(LSTMEncoder, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.dropout = dropout
        self.bidirectional = bidirectional
        
        # 创建LSTM层
        # 注意：当num_layers=1时，LSTM的dropout参数必须为0，但我们仍然保留dropout值供其他用途
        lstm_dropout = dropout if num_layers > 1 else 0
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=lstm_dropout,
            bidirectional=bidirectional
        )
        
        # 添加输出dropout层（无论num_layers如何都应用）
        self.output_dropout = nn.Dropout(dropout)
        
        # 计算输出特征维度
        self.output_size = hidden_size * 2 if bidirectional else hidden_size
    
    def forward(
        self, 
        x: torch.Tensor, 
        hidden: Optional[Tuple[torch.Tensor, torch.Tensor]] = None
    ) -> Tuple[torch.Tensor, Tuple[torch.Tensor, torch.Tensor]]:
        """前向传播。
        
        Args:
            x: 输入数据，形状为 [batch_size, seq_len, input_size]
            hidden: 初始隐藏状态和单元状态，默认为None
            
        Returns:
            tuple: (输出特征, (最终隐藏状态, 最终单元状态))
                - 输出特征形状: [batch_size, seq_len, output_size]
                - 隐藏状态形状: [num_layers * num_directions, batch_size, hidden_size]
                - 单元状态形状: [num_layers * num_directions, batch_size, hidden_size]
        """
        # 初始化隐藏状态和单元状态（如果未提供）
        if hidden is None:
            batch_size = x.size(0)
            device = x.device
            h0 = torch.zeros(
                self.num_layers * (2 if self.bidirectional else 1),
                batch_size,
                self.hidden_size,
                device=device
            )
            c0 = torch.zeros(
                self.num_layers * (2 if self.bidirectional else 1),
                batch_size,
                self.hidden_size,
                device=device
            )
            hidden = (h0, c0)
        
        # LSTM前向传播
        output, hidden = self.lstm(x, hidden)
        
        # 应用输出dropout
        output = self.output_dropout(output)
        
        return output, hidden 
    

