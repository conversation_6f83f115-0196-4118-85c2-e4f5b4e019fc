"""
车联网入侵检测数据集处理模块。
提供数据加载、预处理和序列化功能，支持差分隐私保护下的联邦学习。
"""
import os
from typing import Tuple, Optional, Dict, List, Union, Any

import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, MinMaxScaler


def fit_normalize_scaler(data: np.ndarray) -> MinMaxScaler:
    """
    使用数据拟合一个归一化器。
    
    Args:
        data: 用于拟合归一化器的数据
        
    Returns:
        拟合好的MinMaxScaler实例
    """
    scaler = MinMaxScaler()
    scaler.fit(data)
    return scaler


def normalize_data(data: np.ndarray, scaler: Optional[MinMaxScaler] = None) -> Tuple[np.ndarray, MinMaxScaler]:
    """
    对数据进行归一化处理，将特征缩放到[0,1]范围内。
    
    Args:
        data: 输入特征数据
        scaler: 可选的预先拟合的归一化器，如果为None则创建新的归一化器
        
    Returns:
        归一化后的特征数据和使用的归一化器
    """
    if scaler is None:
        scaler = MinMaxScaler()
        return scaler.fit_transform(data), scaler
    else:
        return scaler.transform(data), scaler


def fit_standardize_scaler(data: np.ndarray) -> StandardScaler:
    """
    使用数据拟合一个标准化器。
    
    Args:
        data: 用于拟合标准化器的数据
        
    Returns:
        拟合好的StandardScaler实例
    """
    scaler = StandardScaler()
    scaler.fit(data)
    return scaler


def standardize_data(data: np.ndarray, scaler: Optional[StandardScaler] = None) -> Tuple[np.ndarray, StandardScaler]:
    """
    对数据进行标准化处理，使特征均值为0，标准差为1。
    
    Args:
        data: 输入特征数据
        scaler: 可选的预先拟合的标准化器，如果为None则创建新的标准化器
        
    Returns:
        标准化后的特征数据和使用的标准化器
    """
    if scaler is None:
        scaler = StandardScaler()
        return scaler.fit_transform(data), scaler
    else:
        return scaler.transform(data), scaler


def encode_categorical(data: pd.DataFrame, categorical_columns: List[str]) -> pd.DataFrame:
    """
    对分类特征进行独热编码。
    
    Args:
        data: 输入数据框
        categorical_columns: 需要编码的分类特征列名列表
        
    Returns:
        编码后的数据框
    """
    return pd.get_dummies(data, columns=categorical_columns, drop_first=True)


def load_data(
    data_path: str, 
    label_col: str = 'class',
    test_size: float = 0.3,
    valid_size: Optional[float] = None,
    random_state: int = 42,
    normalize: bool = True,
    standardize: bool = False,
    categorical_columns: Optional[List[str]] = None,
    convert_to_3d: bool = True
) -> Dict[str, Any]:
    """
    加载并处理入侵检测数据集，返回训练集、测试集（和可选的验证集）。
    结果数据自动转换为三维形式[数据大小, 1, 特征维度]，以便与序列数据兼容。
    
    Args:
        data_path: 数据集路径
        label_col: 标签列名
        test_size: 测试集比例
        valid_size: 验证集比例，如果为None则不划分验证集。验证集将从训练集中划分。
        random_state: 随机种子
        normalize: 是否进行归一化处理
        standardize: 是否进行标准化处理（如果normalize和standardize都为True，则先标准化后归一化）
        categorical_columns: 需要进行独热编码的分类特征列名列表
        convert_to_3d: 是否将数据转换为三维格式
        
    Returns:
        包含训练和测试数据的字典，格式为:
        {
            'X_train': np.ndarray, 形状为[n_samples, 1, n_features]或[n_samples, n_features]
            'y_train': np.ndarray, 
            'X_test': np.ndarray, 形状为[n_samples, 1, n_features]或[n_samples, n_features]
            'y_test': np.ndarray,
            'feature_names': List[str] (可选)
        }
        如果valid_size不为None，则还包含'X_valid'和'y_valid'
        
    Raises:
        FileNotFoundError: 如果数据文件不存在
        ValueError: 如果读取CSV文件出错
    """
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    print(f"加载数据集: {data_path}")
    
    # 读取CSV文件
    try:
        df = pd.read_csv(data_path)

        # 处理可能的无穷大或超出 float64 上限的数值
        # 1) 将正负无穷替换为 NaN
        df.replace([np.inf, -np.inf], np.nan, inplace=True)

        # 2) 将超过 float64 上限 (≈1.79e308) 的极端值裁剪为可表示的最大值
        float64_max: float = np.finfo(np.float64).max
        df = df.applymap(
            lambda x: float64_max if isinstance(x, (int, float, np.floating)) and x > float64_max else
            (-float64_max if isinstance(x, (int, float, np.floating)) and x < -float64_max else x)
        )

        # 3) 对仍存在的 NaN 行进行删除，避免后续 scaler 报错
        if df.isna().any().any():
            before_drop: int = len(df)
            df.dropna(inplace=True)
            print(f"已移除包含 NaN/Inf 的行: {before_drop - len(df)} 条 (剩余 {len(df)})")

        print(f"数据集形状: {df.shape}")
    except Exception as e:
        raise ValueError(f"读取CSV文件时出错: {str(e)}")
    
    # 处理分类特征
    if categorical_columns is not None:
        df = encode_categorical(df, categorical_columns)
        print(f"对分类特征进行独热编码后的数据形状: {df.shape}")
    
    # 保存特征名称
    feature_names = list(df.columns)
    if label_col in feature_names:
        feature_names.remove(label_col)
    
    # 分离特征和标签
    if label_col in df.columns:
        X = df.drop(label_col, axis=1).values
        y = df[label_col].values
    else:
        # 假设最后一列是标签
        X = df.iloc[:, :-1].values
        y = df.iloc[:, -1].values
        label_col = df.columns[-1]
        feature_names = list(df.columns[:-1])
    
    print(f"特征形状: {X.shape}, 标签形状: {y.shape}")
    print(f"标签分布: {np.unique(y, return_counts=True)}")
    
    # 数据集划分
    result_dict = {'feature_names': feature_names}
    
    # 首先划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_size, random_state=random_state, stratify=y
    )
    
    # 如果需要验证集，从训练集中划分验证集
    if valid_size is not None:
        # 计算验证集在训练集中的比例
        # valid_size原本是整体数据的比例，需要转换为训练集的比例
        valid_ratio = valid_size / (1 - test_size)
        X_train, X_valid, y_train, y_valid = train_test_split(
            X_train, y_train, test_size=valid_ratio, random_state=random_state, stratify=y_train
        )
    
    # 数据预处理 - 使用训练集拟合标准化器和归一化器，然后转换所有数据集
    if standardize:
        # 使用训练集拟合标准化器
        std_scaler = fit_standardize_scaler(X_train)
        # 转换训练集
        X_train, _ = standardize_data(X_train, std_scaler)
        # 转换测试集
        X_test, _ = standardize_data(X_test, std_scaler)
        # 如果有验证集，也进行转换
        if valid_size is not None:
            X_valid, _ = standardize_data(X_valid, std_scaler)
        print("已对数据进行标准化处理 (使用训练集拟合的标准化器)")
    
    if normalize:
        # 使用训练集拟合归一化器
        norm_scaler = fit_normalize_scaler(X_train)
        # 转换训练集
        X_train, _ = normalize_data(X_train, norm_scaler)
        # 转换测试集
        X_test, _ = normalize_data(X_test, norm_scaler)
        # 如果有验证集，也进行转换
        if valid_size is not None:
            X_valid, _ = normalize_data(X_valid, norm_scaler)
        print("已对数据进行归一化处理 (使用训练集拟合的归一化器)")
    
    # 将数据转换为三维结构 [n_samples, 1, n_features]，以便与序列数据保持一致
    if convert_to_3d:
        X_train = np.expand_dims(X_train, axis=1)
        X_test = np.expand_dims(X_test, axis=1)
        if valid_size is not None:
            X_valid = np.expand_dims(X_valid, axis=1)
        print(f"已将数据转换为三维结构，新的形状: X_train={X_train.shape}")
    
    # 更新结果字典
    if valid_size is not None:
        print(f"训练集: {X_train.shape}, {y_train.shape}")
        print(f"验证集: {X_valid.shape}, {y_valid.shape}")
        print(f"测试集: {X_test.shape}, {y_test.shape}")
        
        result_dict.update({
            'X_train': X_train, 
            'y_train': y_train,
            'X_valid': X_valid, 
            'y_valid': y_valid, 
            'X_test': X_test, 
            'y_test': y_test
        })
    else:
        print(f"训练集: {X_train.shape}, {y_train.shape}")
        print(f"测试集: {X_test.shape}, {y_test.shape}")
        
        result_dict.update({
            'X_train': X_train, 
            'y_train': y_train,
            'X_test': X_test, 
            'y_test': y_test
        })
    
    return result_dict


def create_sequence_data(
    X: np.ndarray, 
    y: np.ndarray, 
    seq_length: int, 
    stride: int = 1
) -> Tuple[np.ndarray, np.ndarray]:
    """
    将原始数据转换为序列数据，用于时序模型（如LSTM或Transformer）。
    
    Args:
        X: 输入特征，形状为 [n_samples, n_features]
        y: 输入标签，形状为 [n_samples]
        seq_length: 序列长度
        stride: 滑动窗口步长
        
    Returns:
        X_seq: 序列特征数据，形状为 [n_sequences, seq_length, n_features]
        y_seq: 序列标签数据，形状为 [n_sequences]（取每个序列的最后一个标签）
    """
    # 确保输入是二维的
    if len(X.shape) > 2:
        # 如果是三维的 [n_samples, 1, n_features]，转换为二维 [n_samples, n_features]
        X = X.reshape(X.shape[0], -1)
    
    n_samples, n_features = X.shape
    n_sequences = (n_samples - seq_length) // stride + 1
    
    X_seq = np.zeros((n_sequences, seq_length, n_features))
    y_seq = np.zeros(n_sequences)
    
    for i in range(n_sequences):
        start_idx = i * stride
        end_idx = start_idx + seq_length
        X_seq[i] = X[start_idx:end_idx]
        # 使用序列最后一个时间步的标签
        y_seq[i] = y[end_idx - 1]
    
    print(f"序列数据形状: X_seq={X_seq.shape}, y_seq={y_seq.shape}")
    return X_seq, y_seq


def load_sequence_data(
    data_path: str, 
    label_col: str = 'class',
    seq_length: int = 10,
    stride: int = 1,
    test_size: float = 0.3,
    valid_size: Optional[float] = None,
    random_state: int = 42,
    normalize: bool = True,
    standardize: bool = False,
    categorical_columns: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    加载并处理入侵检测数据集，将其转换为序列数据，适用于时序模型。
    
    Args:
        data_path: 数据集路径
        label_col: 标签列名
        seq_length: 序列长度
        stride: 滑动窗口步长
        test_size: 测试集比例
        valid_size: 验证集比例，如果为None则不划分验证集。验证集将从训练集中划分。
        random_state: 随机种子
        normalize: 是否进行归一化处理
        standardize: 是否进行标准化处理
        categorical_columns: 需要进行独热编码的分类特征列名列表
        
    Returns:
        包含序列训练和测试数据的字典，格式为:
        {
            'X_train': np.ndarray, 
            'y_train': np.ndarray, 
            'X_test': np.ndarray, 
            'y_test': np.ndarray,
            'feature_names': List[str] (可选)
        }
        如果valid_size不为None，则还包含'X_valid'和'y_valid'
    """
    # 首先加载原始数据，但不转换为三维
    data_dict = load_data(
        data_path=data_path,
        label_col=label_col,
        test_size=test_size,
        valid_size=valid_size,
        random_state=random_state,
        normalize=normalize,
        standardize=standardize,
        categorical_columns=categorical_columns,
        convert_to_3d=False
    )
    
    # 为每个数据集创建序列
    result_dict = {}
    
    # 保留特征名称
    if 'feature_names' in data_dict:
        result_dict['feature_names'] = data_dict['feature_names']
    
    # 转换训练和测试数据
    X_train_seq, y_train_seq = create_sequence_data(
        data_dict['X_train'], data_dict['y_train'], seq_length, stride
    )
    X_test_seq, y_test_seq = create_sequence_data(
        data_dict['X_test'], data_dict['y_test'], seq_length, stride
    )
    
    result_dict.update({
        'X_train': X_train_seq,
        'y_train': y_train_seq,
        'X_test': X_test_seq,
        'y_test': y_test_seq
    })
    
    # 如果有验证集，也进行转换
    if 'X_valid' in data_dict and 'y_valid' in data_dict:
        X_valid_seq, y_valid_seq = create_sequence_data(
            data_dict['X_valid'], data_dict['y_valid'], seq_length, stride
        )
        result_dict.update({
            'X_valid': X_valid_seq,
            'y_valid': y_valid_seq
        })
    
    return result_dict
