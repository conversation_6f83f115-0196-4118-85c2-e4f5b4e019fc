from opacus import PrivacyEngine
from opacus.accountants import RDPAccountant
from typing import Optional, Callable
import torch
from torch.utils.data import DataLoader
from opacus.validators import ModuleValidator
from typing import Dict, List, Any, Optional, Union, Tuple
import logging
import torch.nn as nn
import math
from opacus.grad_sample.grad_sample_module import GradSampleModule


logger = logging.getLogger(__name__)

class CustomPrivacyEngine(PrivacyEngine):
    def __init__(
            self,
            *,
            accountant="rdp",
            secure_mode=False,
            config: dict = None,
            noise_scheduler: Optional[Callable[[int, float, float], float]] = None
    ):
        """
        自定义隐私引擎支持动态噪声
        
        参数:
            accountant: 隐私预算会计类型，默认为'rdp'
            secure_mode: 是否启用安全模式，默认为False
            noise_scheduler: 函数 (step) -> noise_multiplier，用于动态调整噪声
            **kwargs: 传递给PrivacyEngine的其他参数
        """
        # 调用父类初始化方法，传递所有必要参数
        super().__init__(
            accountant=accountant,
            secure_mode=secure_mode
        )

        self.epsilon_total = config.get('epsilon_total', 5.0)
        self.delta = config.get('delta', 1e-5)
        self.clip_norm = config.get('clip_norm', 1.0)
        
        # 注意: noise_scale在配置中可能表示标准差，但我们将其转换为噪声乘数
        self.noise_multiplier = config.get('noise_scale', 1.0)

        self.gamma = config.get('gamma', 0.5)
        self.noise_base_mode = config.get('noise_base_mode', 'dynamic')
        self.initial_max_noise = config.get('initial_max_noise', 2.0)
        self.noise_decay_mode = config.get('noise_decay_mode', 'exponential')
        self.noise_decay_rate = config.get('noise_decay_rate', 0.01)
        self.hyperbolic_decay_k = config.get('hyperbolic_decay_k', 0.05)
        self.batch_size = config.get('batch_size', 128)
        
        # 保存初始噪声乘数和梯度裁剪范数
        # self.initial_noise_multiplier = self.noise_multiplier 
        self.max_grad_norm =self.clip_norm
        self.current_noise = None
        # 参考损失值，将保持为本地最大损失值
        self.loss_ref = None
        
        
        # 设置噪声调度器，如果没有提供则使用固定噪声
        self.noise_scheduler = noise_scheduler or (lambda _, loss_values, grad_similarity: self.noise_multiplier)
        
        # 初始化RDP核算器
        self.base_accountant = RDPAccountant()
        self.current_step = 0
        self.sample_rate = 0.


        self.dp_module = None
        self.dp_optimizer = None
        self.dp_dataloader = None
        
        logger.info(f"初始化CustomPrivacyEngine: noise_multiplier={self.noise_multiplier}, "
                   f"max_grad_norm={self.max_grad_norm }")

    def make_private(
            self,
            module: torch.nn.Module,
            optimizer: torch.optim.Optimizer,
            data_loader: torch.utils.data.DataLoader,
            noise_multiplier: Optional[float] = None,
            max_grad_norm: Optional[float] = None,
            # noise_scheduler: Optional[Callable[[int, float, float], float]] = None,
            sample_rate: Optional[float] = None,
            poisson_sampling: bool = False,
            target_epsilon: Optional[float] = None,
            target_delta: Optional[float] = None,
            epochs: Optional[int] = None, 
            **kwargs
    ):
        """
        将模型、优化器和数据加载器转换为差分隐私版本。
        
        参数:
            module: 待保护的模型
            optimizer: 优化器
            data_loader: 数据加载器
            noise_multiplier: 噪声乘数，如果为None则使用初始化时的值
            max_grad_norm: 梯度裁剪范数，如果为None则使用初始化时的值
            noise_scheduler: 噪声调度函数，如果提供则覆盖初始化时的值
            **kwargs: 其他参数，传递给父类的make_private方法
        
        返回:
            转换后的模型、优化器和数据加载器
        """

        self.module = module
        self.optimizer = optimizer
        self.data_loader = data_loader
        self.max_grad_norm = max_grad_norm
        self.sample_rate = sample_rate


        self.noise_multiplier = noise_multiplier


        # 计算采样率
        if poisson_sampling:
            self.sample_rate = 1 / len(data_loader)
        else:
            self.sample_rate = self.batch_size / len(data_loader)


        # # # 设置初始噪声
        # if self.noise_scheduler:
        #     self.current_noise = noise_scheduler()
        # else:
        #     self.current_noise = lambda _, loss_value, grad_similarity: self.noise_multiplier

    
        # print('c', module)
        # # 处理模型，确保其与差分隐私兼容
        # self.module = self.private_dp(module)
        # print('asdasdasdasdasdasdas', self.module)
        # print('=========================self.module', type(self.module.parameters()))
        # self.optimizer = type(optimizer)(self.module.parameters(), **optimizer.defaults)

        if target_epsilon is not None and target_delta is not None and epochs is not None:

            dp_module, dp_optimizer, dp_dataloader = super().make_private_with_epsilon(
                module=self.module,
                optimizer=self.optimizer,
                data_loader=self.data_loader,
                # noise_multiplier=self.noise_multiplier,
                max_grad_norm=self.max_grad_norm,
                poisson_sampling = poisson_sampling,
                target_epsilon=target_epsilon,
                target_delta=target_delta,
                epochs=epochs,
                **kwargs
            )


        else:
            # 调用父类方法
            dp_module, dp_optimizer, dp_dataloader = super().make_private(
                module=self.module,
                optimizer=self.optimizer,
                data_loader=self.data_loader,
                noise_multiplier=self.noise_multiplier,
                max_grad_norm=self.max_grad_norm,
                poisson_sampling = poisson_sampling,
                **kwargs
            )


        # if not isinstance(dp_module, GradSampleModule):
        #     logger.info(f"客户端 {self.name}: 首次训练，将模型转换为GradSampleModule类型")
                        
        #     # 保存原始模型的自定义方法
        #     original_methods = {}
        #     custom_methods = ['get_device', '__add__', '__radd__', '__sub__', '__mul__', '__rmul__', 
        #                     '__truediv__', '__pow__', '__neg__', 'norm', 'zeros_like', 
        #                     'dot', 'cos_sim', 'op_with_graph', 'op_without_graph', 
        #                     'load', 'freeze_grad', 'enable_grad', 'zero_dict', 
        #                     'normalize', 'has_nan', 'count_parameters']
                        
        #     for method_name in custom_methods:
        #         if hasattr(dp_module, method_name):
        #             original_methods[method_name] = getattr(dp_module, method_name)
                        
        #     dp_module = self.private_dp(dp_module)

        #     # 转换为GradSampleModule
        #     dp_module = GradSampleModule(dp_module)

        #     # 检查原始模型是否有ingraph属性，如果有则继承，否则设为False
        #     dp_module.ingraph = dp_module._module.ingraph if hasattr(dp_module._module, 'ingraph') else False
                        
        #     # 将原始方法添加到GradSampleModule实例
        #     for method_name, method in original_methods.items():
        #         if not hasattr(dp_module, method_name):
        #             import types
        #             if method_name == 'get_device':
        #                 # 特殊处理get_device方法，确保它返回正确的设备
        #                 dp_module.get_device = lambda: dp_module._module.get_device() if hasattr(dp_module._module, 'get_device') else next(dp_module._module.parameters()).device
        #             else:
        #                 # 为其他方法创建包装器
        #                 def create_method_wrapper(name, orig_method):
        #                     def wrapper(self, *args, **kwargs):
        #                         if hasattr(self._module, name):
        #                             method = getattr(self._module, name)
        #                             return method(*args, **kwargs)
        #                         else:
        #                             return orig_method(*args, **kwargs)
        #                     return wrapper
                                    
        #                 wrapped_method = create_method_wrapper(method_name, method)
        #                 setattr(dp_module, method_name, types.MethodType(wrapped_method, dp_module))
                        
        #     logger.info(f"客户端 {self.name}: 模型类型已转换为 {type(dp_module)}，并保留了原始自定义方法")


        #保存原始模型的自定义方法到GradSampleModule
        if hasattr(dp_module, '_module'):
            # 保存get_device方法
            if hasattr(dp_module._module, 'get_device') and not hasattr(dp_module, 'get_device'):
                logger.info("将原始模型的get_device方法添加到GradSampleModule")
                dp_module.get_device = lambda: dp_module._module.get_device()
                    
            # 保存其他可能需要的自定义方法
            custom_methods = [ 'predict', '_extract_inputs',  '__add__', '__radd__', '__sub__', '__mul__', '__rmul__', 
                            '__truediv__', '__pow__', '__neg__', 'norm', 'zeros_like', 
                            'dot', 'cos_sim', 'op_with_graph', 'op_without_graph', 
                            'load', 'freeze_grad', 'enable_grad', 'zero_dict', 
                            'normalize', 'has_nan', 'count_parameters']
            
            # 检查原始模型是否有ingraph属性，如果有则继承，否则设为False
            dp_module.ingraph = dp_module._module.ingraph if hasattr(dp_module._module, 'ingraph') else False
                    
            for method_name in custom_methods:
                if hasattr(dp_module._module, method_name) and not hasattr(dp_module, method_name):
                    logger.debug(f"将原始模型的{method_name}方法添加到GradSampleModule")
                    # 使用闭包保存方法名
                    def create_method_wrapper(name):
                        def wrapper(self, *args, **kwargs):
                            method = getattr(self._module, name)
                            return method(*args, **kwargs)
                        return wrapper
                            
                    import types
                    method = create_method_wrapper(method_name)
                    setattr(dp_module, method_name, types.MethodType(method, dp_module))


        # # # 自定义噪声调度器
        # if noise_scheduler is not None:
        #     self.noise_scheduler = noise_scheduler

        # 保存关键对象
        self.dp_module = dp_module
        self.dp_optimizer = dp_optimizer
        self.dp_dataloader = dp_dataloader

        # 重置会计
        # self.base_accountant = RDPAccountant()

        # return dp_module, dp_optimizer, dp_dataloader

    
    def update_noise(self, loss_value, grad_similarity) -> float:
        # 更新当前步数
        self.current_step += 1

        # 更新噪声
        
        # 获取当前噪声乘数 - 正确调用噪声调度函数
        self.current_noise = self.noise_scheduler(self.current_step, loss_value, grad_similarity)
            
        # 确保current_noise是一个浮点数而不是函数
        # print(f'self.current_noise={self.current_noise}')
        if callable(self.current_noise):
            logger.info("噪声调度器返回了一个函数而不是浮点数，尝试调用它")
            try:
                self.current_noise = self.current_noise()
            except Exception as e:
                logger.error(f"调用噪声调度器函数失败: {e}")
                self.current_noise = self.noise_multiplier  # 回退到默认值
        
        # # 更新所有DP模块的噪声乘数
        # for module in self.dp_module.modules():
        #     # 更新标准模块
        #     if hasattr(module, 'noise_multiplier'):
        #         module.noise_multiplier = self.current_noise
            
        #     # 更新RNN等特殊层
        #     if hasattr(module, '_module') and hasattr(module._module, 'noise_multiplier'):
        #         module._module.noise_multiplier = self.current_noise
        
        # 更新优化器的噪声乘数
        if hasattr(self.dp_optimizer, 'noise_multiplier'):
            self.dp_optimizer.noise_multiplier = self.current_noise
            # print('dp_optimizer中有noise_multiplier')
        # else:
        #     # 更新所有层的噪声乘数
        #     for module in self.dp_module.modules():
        #         if hasattr(module, 'noise_multiplier'):
        #             module.noise_multiplier = self.current_noise
        #         # 对于RNN等特殊层
        #         if hasattr(module, '_module') and hasattr(module._module, 'noise_multiplier'):
        #             module._module.noise_multiplier = self.current_noise
            

        # if hasattr(self.dp_optimizer, 'step') and callable(self.dp_optimizer.step):
        #     # 对于DPOptimizer，会计由优化器的step方法处理
        #     return

        # # 记录隐私会计
        # sample_rate = self.sample_rate
        # self.base_accountant.step(
        #     noise_multiplier=self.current_noise,
        #     sample_rate=sample_rate
        # )

        # 返回当前噪声值（可选）
        return self.current_noise

    def create_adaptive_noise_scheduler(self, 
                                current_round: int, 
                                ):
        
        """计算自适应噪声乘数。
        
        基于当前轮次、损失值和梯度方向余弦相似度动态调整噪声乘数。
        注意：内部首先计算噪声标准差(σ)，然后将其转换为噪声乘数(z = σ/C)返回。
        
        支持两种噪声衰减模式:
        1. 指数衰减: σ_k = σ_{initial_base} · sqrt(β_k) · e^(-0.5 λ_{noise} t_{global})
        2. 双曲衰减: σ_k = σ_{initial_base} · sqrt(β_k) / sqrt(1 + k · t_{global})
        
        其中:
        - β_k = γ·(1-sim_k) + (1-γ)·L̂_k 是自适应因子
        - 返回的噪声乘数 z_k = σ_k / C，其中C是梯度裁剪范数
        
        Args:
            current_round: 当前训练轮次
            
        Returns:
            function: 返回一个噪声调度函数，接受step, loss_value, grad_similarity参数
        """
        
        
        # 使用传入的参数或默认值
        noise_decay_rate =  self.noise_decay_rate
        hyperbolic_decay_k = self.hyperbolic_decay_k
        
        # 根据噪声基准模式计算σ_{initial_base}
        if self.noise_base_mode == 'fixed':
            # 固定初始噪声模式：使用initial_max_noise作为σ_{initial_base}
            initial_base = self.initial_max_noise
            logger.info(f"使用固定噪声基准值: σ_{{initial_base}}={initial_base:.4f}")
        else:
            # 动态噪声模式：使用current_noise_multiplier作为σ_{initial_base}
            initial_base = self.noise_multiplier
            logger.info(f"使用动态噪声基准值: σ_{{initial_base}}={initial_base:.4f}")
        
        # 计算基于损失和梯度相似度的自适应因子β_k
        
        def adaptive_scheduler(step, loss_value=None, grad_similarity=None):
            """自适应噪声调度函数，确保参数有默认值，防止调用时缺少参数"""

            # 更新本地最大损失值L_ref
            if loss_value is not None:
                self._update_loss_ref(loss_value)


            # 初始化β_k为1.0（默认无调整）
            beta_k = 1.0  
            # 计算基于损失的组件
            loss_component = 1.0
            if loss_value is not None and self.loss_ref is not None:
                # 归一化损失值，使用本地最大损失值作为参考: L̂_k = L_k / L_{ref}
                norm_loss = loss_value / self.loss_ref
                # 限制在[0, 1]范围内，符合归一化的定义
                norm_loss = min(max(norm_loss, 0.0), 1.0)
                loss_component = norm_loss
                logger.info(f"损失归一化: 原始损失={loss_value:.4f}, 本地最大损失={self.loss_ref:.4f}, 归一化结果={norm_loss:.4f}")
            
            # 计算基于梯度相似度的组件
            similarity_component = 0.0
            # print(f'grad_similarity={grad_similarity}')
            if  grad_similarity != -100000.0:
                # 将相似度从[-1,1]映射到[0,1]
                sim_k = (1 + grad_similarity) / 2
                similarity_component = 1.0 - sim_k  # 相似度越高，噪声越小
                logger.info(f"梯度相似度组件: 原始相似度={grad_similarity:.4f}, 归一化相似度={sim_k:.4f}, 组件值={similarity_component:.4f}")
            else:
                similarity_component = 0.0


            # 计算自适应因子β_k = γ·(1-sim_k) + (1-γ)·L̂_k
            # 如果是初始轮次且没有梯度相似度，则更多依赖损失组件
            if grad_similarity == -100000.0:
                # 初始轮次中，更多依赖损失组件
                beta_k = loss_component
                logger.info(f"初始轮次中，仅使用损失组件计算自适应因子: β_k={beta_k:.4f}")
            else:
                # 正常计算自适应因子
                beta_k = self.gamma * similarity_component + (1 - self.gamma) * loss_component
            
            # 确保β_k >= 0
            beta_k = max(0.0, beta_k)
            logger.info(f"自适应因子β_k计算: γ={self.gamma:.4f}, 相似度组件={similarity_component:.4f}, 损失组件={loss_component:.4f}, β_k={beta_k:.4f}")
            
            # 根据噪声衰减模式计算最终的噪声标准差
            if self.noise_decay_mode == 'hyperbolic':
                # 双曲衰减模式: σ_k = σ_{initial_base} · sqrt(β_k) / sqrt(1 + k · t_{global})
                decay_factor = 1.0 / math.sqrt(1.0 + hyperbolic_decay_k * current_round)
                final_noise = initial_base * math.sqrt(beta_k) * decay_factor
                logger.debug(f"使用双曲衰减模式: 衰减系数k={hyperbolic_decay_k:.4f}, "
                            f"当前轮次={current_round}, 衰减因子={decay_factor:.4f}")
            else:
                # 指数衰减模式（默认）: σ_k = σ_{initial_base} · sqrt(β_k) · e^(-0.5 λ_{noise} t_{global})
                decay_factor = math.exp(-0.5 * noise_decay_rate * current_round)
                final_noise = initial_base * math.sqrt(beta_k) * decay_factor
                logger.debug(f"使用指数衰减模式: 衰减率λ={noise_decay_rate:.4f}, "
                            f"当前轮次={current_round}, 衰减因子={decay_factor:.4f}")
            
            # 设置最小噪声值下限，确保差分隐私的有效性
            final_noise = max(final_noise, 0.01)
            
            # print(' self.dp_optimizer.max_grad_norm=', self.dp_optimizer.max_grad_norm)
            
            # 更新当前噪声乘数（但不增加步数）
            current_noise_multiplier = final_noise /  self.max_grad_norm  # self.dp_optimizer.max_grad_norm   # 将标准差转换为乘数  
            
            logger.info(f"计算自适应噪声: 基础噪声模式={self.noise_base_mode}, 衰减模式={self.noise_decay_mode}, "
                        f"initial_base={initial_base:.4f}, beta_k={beta_k:.4f}, 噪声标准差sigma={final_noise:.4f}, "
                        f"噪声乘数z={current_noise_multiplier:.4f}, 裁剪范数C={self.dp_optimizer.max_grad_norm:.4f}")
            
            return current_noise_multiplier 
        
        return adaptive_scheduler

    def _update_loss_ref(self, loss_value):
        """更新参考损失值L_ref。
        
        根据a1.md中的定义，L_ref是本地最大损失值。
        这个方法会在首次收到损失值时初始化L_ref，之后会保持L_ref为所有观察到的损失值中的最大值。
        
        Args:
            loss_value: 当前损失值
        """
            
        #如果是首次收到损失值，初始化L_ref
        if self.loss_ref is None:
            self.loss_ref = loss_value
            logger.info(f"初始化本地最大损失值L_ref={self.loss_ref:.4f}")
        # 否则，更新L_ref为最大损失值
        elif loss_value > self.loss_ref:
            old_ref = self.loss_ref
            self.loss_ref = loss_value
            logger.info(f"更新本地最大损失值L_ref: {old_ref:.4f} -> {self.loss_ref:.4f}")
        
        # 
    
    
    
    
    def get_privacy_spent(self, delta: float = 1e-5, **kwargs):
        """获取当前隐私开销"""
        return self.base_accountant.get_privacy_spent(delta=delta, **kwargs)

    def get_current_noise(self) -> float:
        """获取当前噪声乘数"""
        return self.noise_scheduler(self.current_step)

    def private_dp(self, model: Any) -> Any:
        try:
            # 检查模型是否有不兼容的模块
            if not ModuleValidator.is_valid(model):
                logger.info("模型包含不支持差分隐私计算的层，尝试修复...")
                # 替换不兼容的模块（如LSTM）
                model = ModuleValidator.fix(model)
                logger.info("已将不兼容的模块替换为DP兼容版本")

                return model
        except Exception as e:
            logger.error(f"替换不兼容模块时发生错误: {str(e)}")
            # 尝试手动替换LSTM
            try:
                # 尝试不同的导入路径，因为Opacus的API可能随版本变化
                try:  # 旧版本可能的路径
                    from opacus.layers import DPLSTM
                except ImportError:
                    logger.error("无法导入DPLSTM，请确认Opacus版本和模块路径")
                    raise ImportError("无法找到Opacus中的DPLSTM模块")

                    # 递归替换模型中的所有LSTM层

                def replace_lstm(module):
                    for name, child in module.named_children():
                        if isinstance(child, nn.LSTM):
                            # 获取LSTM的参数
                            input_size = child.input_size
                            hidden_size = child.hidden_size
                            num_layers = child.num_layers
                            bias = child.bias
                            batch_first = child.batch_first
                            dropout = child.dropout
                            bidirectional = child.bidirectional

                            # 创建DPLSTM替代
                            dp_lstm = DPLSTM(
                                input_size=input_size,
                                hidden_size=hidden_size,
                                num_layers=num_layers,
                                bias=bias,
                                batch_first=batch_first,
                                dropout=dropout,
                                bidirectional=bidirectional
                            )

                            # 如果可能，复制权重
                            try:
                                dp_lstm.load_state_dict(child.state_dict())
                            except Exception as e:
                                logger.info(f"无法复制LSTM权重到DPLSTM: {str(e)}")

                            # 替换模块
                            setattr(module, name, dp_lstm)
                            logger.info(f"已将{name}从nn.LSTM替换为DPLSTM")
                        else:
                            # 递归处理子模块
                            replace_lstm(child)

                # 执行替换
                replace_lstm(model)
                return model
            except Exception as e2:
                logger.error(f"手动替换LSTM失败: {str(e2)}")
                return model
            
from opacus.optimizers import DPOptimizer
class DynamicMedianClippingOptimizer(DPOptimizer):
    """
    自定义优化器：使用当前批次的梯度中值作为裁剪阈值
    工作原理：
    1. 计算当前批次中每个样本的梯度总范数
    2. 计算这些范数的中位数作为本批次的裁剪阈值
    3. 基于此阈值裁剪所有样本梯度
    """
    def __init__(
            self,  
            *,
            optimizer: DPOptimizer,
            verbose: bool = True,
    ):
        """
        自定义隐私引擎支持动态噪声
        
        参数:
            optimizer: PyTorch优化器
            verbose: 是否打印裁剪信息，默认为True
            noise_multiplier: 噪声乘数
            max_grad_norm: 初始梯度裁剪阈值（会被覆盖）
            expected_batch_size: 预期批次大小
        """
        # 调用父类初始化方法，但不传递verbose参数
        super().__init__(
            optimizer=optimizer
        )

        self.current_clip_value = 1.0
        self.batch_grad_norms = []
        self.verbose = verbose


    def _clip_gradients(self, model):
        """重写梯度裁剪方法：使用当前批次的梯度中值作为阈值"""
        # 1. 收集当前批次所有样本的梯度范数
        self.batch_grad_norms = []
        
        with torch.no_grad():
            # 遍历模型的所有参数
            for param in model.parameters():
                if param.grad_sample is not None:
                    # 获取当前参数的每个样本的梯度 (batch_size x ...)
                    grad_sample = param.grad_sample
                    
                    # 计算每个样本的梯度向量范数 (batch_size,)
                    per_sample_norms = torch.norm(
                        grad_sample.view(grad_sample.shape[0], -1), 
                        p=2, 
                        dim=1
                    )
                    self.batch_grad_norms.append(per_sample_norms)
            
        if not self.batch_grad_norms:
            # 没有梯度则跳过
            return
            
        # 2. 组合所有参数的梯度范数 (batch_size x num_params)
        all_per_sample_norms = torch.stack(self.batch_grad_norms, dim=1)
        
        # 3. 计算每个样本的总梯度范数 (batch_size,)
        # 对每个样本的所有参数梯度范数求L2范数
        total_per_sample_norms = torch.norm(all_per_sample_norms, p=2, dim=1)
        
        # 4. 计算当前批次的梯度中值
        current_median = torch.median(total_per_sample_norms).item()
        
        # 5. 防止零值或过小值导致问题
        if current_median < 1e-6:
            current_median = 1e-6
            
        # 6. 记录当前裁剪值（用于监控和调试）
        self.current_clip_value = current_median
        self.batch_grad_norms = total_per_sample_norms.detach().cpu().numpy()
        
        with torch.no_grad():
            # 7. 应用裁剪：对每个参数的每个样本梯度进行裁剪
            for param in model.parameters():
                if param.grad_sample is not None:
                    # 重新获取当前参数的每个样本梯度
                    grad_sample = param.grad_sample
                    
                    # 计算每个样本梯度的缩放因子
                    per_sample_norms = torch.norm(
                        grad_sample.view(grad_sample.shape[0], -1), 
                        p=2, 
                        dim=1
                    ) + 1e-6  # 防止除以零
                    
                    # 计算每个样本的裁剪因子
                    # max(1, norm/s) => min(1, s/norm)
                    clip_factors = torch.clamp_max(
                        current_median / per_sample_norms, 
                        1.0
                    )
                    
                    # 调整维度以匹配梯度形状
                    view_shape = (-1,) + (1,) * (grad_sample.dim() - 1)
                    clip_factors = clip_factors.view(*view_shape)
                    
                    # 应用裁剪
                    param.grad_sample.mul_(clip_factors)
        
        # 8. 可选：打印裁剪信息
        if self.verbose:
            min_norm = total_per_sample_norms.min().item()
            max_norm = total_per_sample_norms.max().item()
            mean_norm = total_per_sample_norms.mean().item()
            print(f"Clip norm: {current_median:.4f} | "
                  f"Batch gradients: min={min_norm:.4f}, "
                  f"median={current_median:.4f}, "
                  f"mean={mean_norm:.4f}, "
                  f"max={max_norm:.4f}")

    def get_clipping_stats(self):
        """获取当前批次的裁剪统计信息"""
        return {
            "clip_value": self.current_clip_value,
            "grad_norms": self.batch_grad_norms
        }