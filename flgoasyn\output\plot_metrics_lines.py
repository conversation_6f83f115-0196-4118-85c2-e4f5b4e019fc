#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
绘制多个方法的准确率比较折线图，不带标记。
从pkl文件中读取不同方法的metrics数据，并绘制准确率随轮次变化的折线图。
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import argparse


def load_metrics_from_pkl(file_path: str) -> List[Dict]:
    """
    从pkl文件中加载metrics数据。
    
    参数:
        file_path: pkl文件的路径
        
    返回:
        包含metrics的列表，每个元素是一个轮次的metrics字典
    """
    try:
        with open(file_path, 'rb') as f:
            metrics_data = pickle.load(f)
        
        # 确保返回的是列表
        if not isinstance(metrics_data, list):
            metrics_data = [metrics_data]
            
        return metrics_data
    except Exception as e:
        print(f"加载文件 {file_path} 时出错: {e}")
        return []


def extract_accuracy_from_metrics(metrics_list: List[Dict]) -> List[float]:
    """
    从metrics列表中提取准确率数据。
    
    参数:
        metrics_list: 包含metrics的列表，每个元素是一个轮次的metrics字典
        
    返回:
        准确率列表
    """
    accuracy_list = []
    for metrics in metrics_list:
        if isinstance(metrics, dict) and 'accuracy' in metrics:
            accuracy_list.append(metrics['accuracy'])
    return accuracy_list


def plot_accuracy_lines(
    methods_data: Dict[str, List[float]],
    title: str = "Methods Accuracy Comparison",
    xlabel: str = "Rounds",
    ylabel: str = "Test Accuracy",
    figsize: Tuple[float, float] = (6.4, 4.8),  # matplotlib默认大小
    save_path: Optional[str] = None,
    ylim: Optional[Tuple[float, float]] = (0.0, 1.0),
    rounds_step: int = 20,
    max_rounds: int = 100,
    start_from_zero: bool = True,  # 添加参数控制是否从0开始
    linewidth: float = 2.0  # 线宽
) -> None:
    """
    绘制多个方法的准确率比较折线图，不带标记。
    
    参数:
        methods_data: 字典，格式为 {方法名: 准确率列表}
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        figsize: 图表大小，默认为 matplotlib 的标准大小 (6.4, 4.8)
        save_path: 保存路径，如果为None则不保存
        ylim: y轴范围，格式为(ymin, ymax)
        rounds_step: 轮次标签的步长
        max_rounds: 最大轮次数
        start_from_zero: 是否从0开始显示x轴，默认为True
        linewidth: 线宽，默认为2.0
    """
    # 设置全局字体为 Times New Roman
    plt.rcParams['font.family'] = 'Times New Roman'
    plt.rcParams['mathtext.fontset'] = 'stix'
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    plt.figure(figsize=figsize)
    
    # 定义线型和颜色
    linestyles = ['-', '--', '-.', ':']
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    # 定义方法的显示顺序
    display_order = ['Proposed', 'Fedbuff']  #["Clients=20", "Clients=40", "Clients=60", "Clients=80"]
    
    # 按照指定顺序对方法进行排序
    ordered_methods = []
    for method_name in display_order:
        if method_name in methods_data:
            ordered_methods.append(method_name)
    
    # 添加任何不在display_order中但在methods_data中的方法
    for method_key in methods_data.keys():
        if method_key not in ordered_methods:
            ordered_methods.append(method_key)
    
    # 绘制每个方法的折线
    for i, method_key in enumerate(ordered_methods):
        accuracy_list = methods_data[method_key]
        
        # 确保数据长度不超过最大轮次
        data_length = min(len(accuracy_list), max_rounds)
        
        # 生成x轴值（轮次）
        if start_from_zero:
            # 如果需要从0开始，则添加一个0值
            rounds = list(range(0, data_length + 1))
            plot_data = [0.0] + accuracy_list[:data_length]
        else:
            rounds = list(range(1, data_length + 1))
            plot_data = accuracy_list[:data_length]
        
        # # 为Proposed和CLDP指定固定颜色，并互换它们的颜色
        # if method_key == "Proposed":
        #     color = colors[3]  # CLDP的颜色
        # elif method_key == "CLDP":
        #     color = colors[1]  # Proposed的颜色
        # else:
        color = colors[i % len(colors)]
        
        # 绘制折线，不带标记
        plt.plot(rounds, plot_data, 
                 label=method_key,
                 linestyle=linestyles[i % len(linestyles)],
                 color=color,
                 linewidth=linewidth)
    
    # 设置图表属性
    plt.title(title, fontsize=18, family='Times New Roman')
    plt.xlabel(xlabel, fontsize=16, family='Times New Roman')
    plt.ylabel(ylabel, fontsize=16, family='Times New Roman')
    
    # 设置x轴刻度，步长为rounds_step
    if start_from_zero:
        x_ticks = list(range(0, max_rounds + 1, rounds_step))
    else:
        x_ticks = list(range(1, max_rounds + 1, rounds_step))
    plt.xticks(x_ticks, fontsize=14, family='Times New Roman')
    
    # 添加 x 轴子刻度
    minor_x_ticks = list(range(0 if start_from_zero else 1, max_rounds + 1, 10))  # 子刻度增幅为10
    # 过滤掉与主刻度重叠的子刻度
    minor_x_ticks = [x for x in minor_x_ticks if x not in x_ticks]
    plt.gca().set_xticks(minor_x_ticks, minor=True)
    plt.gca().set_xticklabels([str(x) for x in minor_x_ticks], minor=True, fontsize=14, family='Times New Roman')
    plt.gca().tick_params(axis='x', which='minor', length=4, color='gray', width=1.0)
    
    # 设置x轴范围，从0开始顶头
    if start_from_zero:
        plt.xlim(0, max_rounds)
    
    # 设置y轴范围和刻度
    if ylim:
        plt.ylim(ylim)
        # 修改y轴刻度增幅为0.2
        y_ticks = np.arange(ylim[0], ylim[1] + 0.01, 0.2)
        plt.yticks(y_ticks, fontsize=14, family='Times New Roman')
        
        # 添加 y 轴子刻度
        minor_y_ticks = np.arange(ylim[0], ylim[1] + 0.01, 0.1)  # 子刻度间隔为0.1
        # 过滤掉与主刻度重叠的子刻度
        minor_y_ticks = [y for y in minor_y_ticks if not any(abs(y - main_y) < 1e-6 for main_y in y_ticks)]
        plt.gca().set_yticks(minor_y_ticks, minor=True)
        plt.gca().set_yticklabels([f"{y:.1f}" for y in minor_y_ticks], minor=True, fontsize=14, family='Times New Roman')
        plt.gca().tick_params(axis='y', which='minor', length=4, color='gray', width=1.0)
    
    # 添加网格线（横向和纵向）
    plt.grid(True, linestyle='--', alpha=0.7)
    # 添加次要网格线
    plt.grid(True, which='minor', linestyle=':', alpha=0.3)
    
    # 添加图例，放在右下角
    legend = plt.legend(fontsize=12, loc='lower right', prop={'family': 'Times New Roman'})
    legend.get_frame().set_linewidth(1.0)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存至: {save_path}")
    
    # 显示图表
    plt.show()


def check_directory_and_update_mapping(data_dir):
    """检查目录并更新映射关系"""
    if not os.path.exists(data_dir):
        print(f"目录 {data_dir} 不存在")
        return {}
    
    pkl_files = [os.path.splitext(f)[0] for f in os.listdir(data_dir) if f.endswith('.pkl')]
    
    if not pkl_files:
        print(f"在目录 {data_dir} 中未找到pkl文件")
        return {}
    
    print(f"找到以下文件: {pkl_files}")
    
    #根据找到的文件创建映射
    # custom_names = {
    #     "pro_all_metrics_buffer6": "K=6",
    #     "pro_all_metrics_buffer7": "K=7",
    #     "pro_all_metrics_buffer8": "K=8",
    #     "pro_all_metrics_buffer9": "K=9"
    # }
    # custom_names = {
    #     "pro_all_metrics_clip1": "C=1",
    #     "pro_all_metrics_clip2": "C=2",
    #     "pro_all_metrics_clip3": "C=3",
    #     "pro_all_metrics_clip5": "C=5"
    # }

    # custom_names = {
    #     "pro_all_metrics_intial2": "$\sigma_{0}$=2",
    #     "pro_all_metrics_intial3": "$\sigma_{0}$=3",
    #     "pro_all_metrics_intial4": "$\sigma_{0}$=4",
    #     "pro_all_metrics_intial5": "$\sigma_{0}$=5"
    # }

    # custom_names = {
    #     "pro_all_metrics_clients20": "N=20",
    #     "pro_all_metrics_clients40": "N=40",
    #     "pro_all_metrics_clients60": "N=60",
    #     "pro_all_metrics_clients80": "N=80"
    # }

    
    custom_names = {
            "pro_all_metricspro": "Proposed",
            "pro_all_metrics_asynfedbuff": "Fedbuff",
            "pro_all_metrics_avgm": "Fedavgm"
    }
    
    
    

    
    # 过滤掉不存在的文件
    filtered_names = {k: v for k, v in custom_names.items() if k in pkl_files}
    
    print("使用的映射关系:")
    for key, value in filtered_names.items():
        print(f"  - {key} -> {value}")
    
    return filtered_names


def plot_from_directory(data_dir,
                       output="accuracy_lines.png",
                       title="",
                       ymin=0.0, ymax=1.0,
                       rounds_step=20, max_rounds=100,
                       linewidth=2.0):
    """从指定目录读取pkl文件并绘制比较图"""
    # 获取目录中的所有pkl文件
    if not os.path.exists(data_dir):
        print(f"目录 {data_dir} 不存在")
        return
        
    pkl_files = [f for f in os.listdir(data_dir) if f.endswith('.pkl')]
    
    if not pkl_files:
        print(f"在目录 {data_dir} 中未找到pkl文件")
        return
    
    # 获取自定义名称映射
    custom_names = check_directory_and_update_mapping(data_dir)
    
    # 打印找到的文件和自定义名称映射，用于调试
    print("找到的pkl文件:")
    for file in pkl_files:
        print(f"  - {file}")
    
    if custom_names:
        print("\n自定义方法名称映射:")
        for key, value in custom_names.items():
            print(f"  - {key} -> {value}")
    
    # 读取每个方法的数据
    methods_data = {}
    for file_name in pkl_files:
        file_path = os.path.join(data_dir, file_name)
        method_name = os.path.splitext(file_name)[0]  # 使用文件名作为方法名
        
        # 加载metrics数据
        metrics_list = load_metrics_from_pkl(file_path)
        
        # 提取准确率数据
        accuracy_list = extract_accuracy_from_metrics(metrics_list)
        
        if accuracy_list:
            # 如果存在自定义名称映射，则使用映射后的名称作为键
            if custom_names and method_name in custom_names:
                display_name = custom_names[method_name]
                methods_data[display_name] = accuracy_list
                print(f"成功加载 {method_name} 的数据 (显示为 {display_name})，共 {len(accuracy_list)} 个数据点")
            else:
                methods_data[method_name] = accuracy_list
                print(f"成功加载 {method_name} 的数据，共 {len(accuracy_list)} 个数据点")
    
    if not methods_data:
        print("未能从pkl文件中提取到有效的准确率数据")
        return
    
    # 绘制比较图
    save_path = os.path.join(data_dir, output)
    plot_accuracy_lines(
        methods_data=methods_data,
        title=title,
        save_path=save_path,
        ylim=(ymin, ymax),
        rounds_step=rounds_step,
        max_rounds=max_rounds,
        linewidth=linewidth
    )


def main():
    """主函数，处理命令行参数并执行绘图"""
    parser = argparse.ArgumentParser(description='绘制多个方法的准确率比较折线图，不带标记')
    parser.add_argument('--data_dir', type=str, default="C:\\Users\\<USER>\\Desktop\\result\\CICDIS17\\draw",
                        help='包含pkl文件的目录路径')
    parser.add_argument('--output', type=str, default="accuracy_lines.png",
                        help='输出图表的文件名')
    parser.add_argument('--title', type=str, default="",
                        help='图表标题')
    parser.add_argument('--ymin', type=float, default=0.2,
                        help='y轴最小值')
    parser.add_argument('--ymax', type=float, default=0.9,
                        help='y轴最大值')
    parser.add_argument('--rounds_step', type=int, default=20,
                        help='轮次标签的步长')
    parser.add_argument('--max_rounds', type=int, default=100,
                        help='最大轮次数')
    parser.add_argument('--linewidth', type=float, default=2.0,
                        help='线宽')
    
    args = parser.parse_args()
    
    # 从文件读取数据并绘图
    plot_from_directory(
        data_dir=args.data_dir,
        output=args.output,
        title=args.title,
        ymin=args.ymin,
        ymax=args.ymax,
        rounds_step=args.rounds_step,
        max_rounds=args.max_rounds,
        linewidth=args.linewidth
    )


if __name__ == "__main__":
    main() 