import os
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import torch
from torch.utils.data import Dataset, DataLoader
from loguru import logger

import flgo.benchmark.base as fbp
import flgo.benchmark.toolkits.partition as fpart

class CICIDSDataset(Dataset):
    """CIC-IDS2017数据集加载类"""
    
    def __init__(self, features, labels):
        self.features = torch.FloatTensor(features)
        self.labels = torch.LongTensor(labels)
        
    def __len__(self):
        return len(self.labels)
    
    def __getitem__(self, idx):
        return self.features[idx], self.labels[idx]

class CICIDSBenchmark(fbp.Benchmark):
    """基于flgo的CIC-IDS2017基准数据集"""
    
    def __init__(self, data_path, test_size=0.2, random_state=42):
        self.data_path = data_path
        self.test_size = test_size
        self.random_state = random_state
        self.X_train = None
        self.y_train = None
        self.X_test = None
        self.y_test = None
        self.input_dim = None
        self.num_classes = None
        
        # 加载数据
        self._load_data()
        
    def _load_data(self):
        """加载和预处理数据"""
        logger.info(f"加载数据集: {self.data_path}")
        
        # 加载数据集
        try:
            df = pd.read_csv(self.data_path)
        except Exception as e:
            logger.error(f"加载数据集失败: {e}")
            raise
        
        # 数据预处理
        # 移除NaN值
        df = df.dropna()
        
        # 将标签转换为数值
        if 'Label' in df.columns:
            label_col = 'Label'
        elif 'label' in df.columns:
            label_col = 'label'
        else:
            # 假设最后一列是标签
            label_col = df.columns[-1]
        
        # 将非数值列转换为数值
        for col in df.columns:
            if df[col].dtype == 'object':
                if col == label_col:
                    # 对标签进行编码
                    unique_labels = df[col].unique()
                    label_map = {label: i for i, label in enumerate(unique_labels)}
                    df[col] = df[col].map(label_map)
                    logger.info(f"标签映射: {label_map}")
                else:
                    # 对分类特征进行编码
                    df[col] = pd.factorize(df[col])[0]
        
        # 分离特征和标签
        X = df.drop(label_col, axis=1).values
        y = df[label_col].values
        
        # 特征标准化
        scaler = StandardScaler()
        X = scaler.fit_transform(X)
        
        # 划分训练集和测试集
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            X, y, test_size=self.test_size, random_state=self.random_state, stratify=y
        )
        
        # 设置特征维度和类别数
        self.input_dim = X.shape[1]
        self.num_classes = len(np.unique(y))
        
        logger.info(f"数据集加载完成: 特征维度={self.input_dim}, 类别数={self.num_classes}")
        logger.info(f"训练集: {self.X_train.shape}, 测试集: {self.X_test.shape}")
        
    def get_data_loader(self, batch_size=64, shuffle=True):
        """获取数据加载器"""
        train_dataset = CICIDSDataset(self.X_train, self.y_train)
        test_dataset = CICIDSDataset(self.X_test, self.y_test)
        
        train_loader = DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=0,
            pin_memory=True
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0,
            pin_memory=True
        )
        
        return train_loader, test_loader
    
    def get_client_data(self, num_clients, alpha=0.5, partition_type='dirichlet'):
        """获取客户端数据分区"""
        logger.info(f"创建{partition_type}分区，客户端数量={num_clients}, alpha={alpha}")
        
        # 使用flgo的分区工具进行数据划分
        if partition_type == 'dirichlet':
            # Dirichlet分布的非独立同分布划分
            partitioner = fpart.DirPartitioner(
                targets=self.y_train,
                num_clients=num_clients,
                alpha=alpha,
                seed=self.random_state
            )
        elif partition_type == 'shards':
            # Shards分区方法
            partitioner = fpart.ShardsPartitioner(
                targets=self.y_train,
                num_clients=num_clients,
                seed=self.random_state
            )
        elif partition_type == 'iid':
            # 独立同分布划分
            partitioner = fpart.IIDPartitioner(
                targets=self.y_train,
                num_clients=num_clients,
                seed=self.random_state
            )
        else:
            raise ValueError(f"不支持的分区类型: {partition_type}")
        
        # 获取分区索引
        client_idxs = partitioner.client_dict
        
        # 创建客户端数据字典
        client_data = {}
        for client_id, idxs in client_idxs.items():
            client_data[client_id] = {
                'x': self.X_train[idxs],
                'y': self.y_train[idxs]
            }
            
            # 记录每个客户端的数据分布
            unique, counts = np.unique(client_data[client_id]['y'], return_counts=True)
            distribution = dict(zip(unique, counts))
            logger.info(f"客户端 {client_id} 数据分布: {distribution}, 总样本数: {len(idxs)}")
            
        return client_data
    
def get_dataloader(features, labels, batch_size=64, shuffle=True):
    """创建PyTorch数据加载器"""
    dataset = CICIDSDataset(features, labels)
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=0,
        pin_memory=True
    )
    return dataloader 