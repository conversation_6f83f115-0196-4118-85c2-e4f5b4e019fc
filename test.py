# import numpy as np
# import matplotlib.pyplot as plt
#
# # 数据
# labels = ['PATG', 'FGSM']
# AT_before = [0.8846, 0.9431]
# AT_after = [0.3475, 0.3839]
# FS_before = [0.8846, 0.9431]
# FS_after = [0.5016, 0.6151]
#
# # 设置柱状图位置
# x = np.arange(len(labels))
# width = 0.2
#
# # 绘制图形
# fig, ax = plt.subplots(figsize=(10, 6))
#
# # 绘制四组柱状图
# ax.bar(x - width*1.5, AT_before, width, label='AT Before Defense', color='#FF9999')
# ax.bar(x - width*0.5, AT_after, width, label='AT After Defense', color='#FF6666')
# ax.bar(x + width*0.5, FS_before, width, label='FS Before Defense', color='#87CEEB')
# ax.bar(x + width*1.5, FS_after, width, label='FS After Defense', color='#003366')
#
# # 设置标签和标题
# # ax.set_xlabel('Attack Method', fontsize=18)
# ax.set_ylabel('EIR', fontsize=18)
# # ax.set_title('Defense Robustness Against Different Adversarial Attack Types', fontsize=18)
# ax.set_xticks(x)
#
# ax.set_ylim(0, 1)
# ax.set_xticklabels(labels,fontsize=18)
# ax.legend(fontsize=15, framealpha=0.3)
#
# ax.tick_params(axis='y', labelsize=18)
#
# ax.grid(linestyle='--', alpha=0.7)
#
# # 调整布局
# plt.tight_layout()
#
# # 保存图像
# plt.savefig('defense_robustness_comparison.png', dpi=300)
# plt.show()


# dict1 = {"a": {"x": 1, "y": 2}, "b": {"z": 3}}
# dict1["a"].update({"x": {"xz": 10, "w": 5}})
# print(dict1)
#
#
# my_dict = {"user": {"name": "John", "age": 30}}
# # my_dict["settings"] = {"theme": "dark", "notifications": True}
# user_updates = {"phone": "************", "age": 32}
# my_dict["user"].update(user_updates)
# print(my_dict)
#
# import torch
#
# print("PyTorch 版本：", torch.__version__)
# print("CUDA 是否可用：", torch.cuda.is_available())
# if torch.cuda.is_available():
#     print("CUDA 版本：", torch.version.cuda)
#     print("GPU 名称：", torch.cuda.get_device_name(0))
#     print("GPU 数量：", torch.cuda.device_count())
# else:
#     print("未检测到可用的 CUDA GPU")
from typing import Tuple
from opacus.accountants.rdp import RDPAccountant
import numpy as np
def find_optimal_noise_with_order(
        target_epsilon: float,
        target_delta: float,
        sample_rate: float,
        sensitivity: float,
        noise_min: float = 0.01,
        noise_max: float = 10.0,
        tolerance: float = 0.01,
        max_iter: int = 20
) -> Tuple[float, float]:
    """
    寻找最优噪声系数并返回最优阶数（使用与opacus一致的转换公式）
    """
    # if orders is None:
    orders =  [1 + x / 10.0 for x in range(1, 100)] + list(range(12, 64))  #list(np.linspace(1.1, 18, 20)) + list(np.linspace(18, 100, 10))

    low, high = noise_min, noise_max
    best_order = None

    for i in range(max_iter):
        mid = (low + high) / 2.0
        # 创建accountant并添加训练步骤
        accountant = RDPAccountant()
        accountant.step(noise_multiplier=mid, sample_rate=sample_rate)

        # 4. 获取当前隐私预算
        current_epsilon = accountant.get_epsilon(delta=target_delta)

        # 5. 计算实际噪声标准差
        effective_noise = mid * sensitivity

        # 6. 寻找最优阶数
        min_eps = float('inf')
        for order in orders:
            # 获取阶数对应的 RDP 值
            try:
                # 创建临时会计对象
                temp_accountant = RDPAccountant([order])
                print(*****************)
                temp_accountant.step(noise_multiplier=mid, sample_rate=sample_rate)

                # 计算该阶数下的隐私损失
                eps = temp_accountant.get_epsilon(delta=target_delta)
                print('sdsdsdsd',eps, order )

                if eps < min_eps:
                    min_eps = eps
                    best_order = order
            except:
                continue

        print(f"Iter {i + 1}: noise={mid:.4f}, ε={current_epsilon:.6f}, best α={best_order}, σ={effective_noise:.4f}")

        # 7. 检查是否满足条件
        if abs(current_epsilon - target_epsilon) <= tolerance:
            return mid, best_order

        # 8. 调整搜索范围
        if current_epsilon > target_epsilon:
            low = mid  # 隐私消耗过高，需增加噪声
        else:
            high = mid  # 隐私消耗过低，可减少噪声

        # 9. 达到最大迭代次数返回中间值
    final_noise = (low + high) / 2.0
    return final_noise, best_order



# noise, alpha = find_optimal_noise_with_order(target_epsilon=10.0,target_delta = 1e-5, sample_rate = 0.25, sensitivity=1.2)


# print(noise, alpha)

import math
# temp_accountant = RDPAccountant()
# alpha = 10
# temp_accountant.step(noise_multiplier=math.sqrt(alpha / (2 * 3)), sample_rate=0.25)
# eps, best_alpha = temp_accountant.get_privacy_spent(delta=1e-5)
#
# print(eps, best_alpha)



def find_self_consistent_alpha(
    sample_rate: float,
    eps_budget: float,
    delta: float,
    alpha_min: float = 1.1,
    alpha_max: float = 100.0,
    num_candidates: int = 100
):
    """
    在 [alpha_min, alpha_max] 范围内以线性扫描方式，
    找到一个 alpha，使得 best_alpha ≈ alpha。
    """
    best_pair = None  # (alpha_input, best_alpha, |diff|)
    min_diff = float("inf")

    # 构造候选 alpha 列表  list(np.linspace(1.1, 18, 20)) + list(np.linspace(18, 100, 10))  # [1 + x / 10.0 for x in range(1, 100)] + list(range(12, 64))#
    alphas = np.linspace(alpha_min, alpha_max, num_candidates)
    for alpha in alphas:
        # 由 eps_budget 反解 noise_multiplier
        # noise_multiplier = sqrt(alpha / (2 * eps_budget))
        sigma = math.sqrt(alpha / (2 * eps_budget))

        acct = RDPAccountant()
        acct.step(noise_multiplier=sigma, sample_rate=sample_rate)
        eps, best_alpha = acct.get_privacy_spent(delta=delta)

        diff = abs(best_alpha - alpha)
        if diff < min_diff:
            min_diff = diff
            best_pair = (alpha, best_alpha, diff, eps, sigma)

    return best_pair



# best_pair = find_self_consistent_alpha(sample_rate=0.5, eps_budget=10, delta=1e-4)
# print(best_pair)


# temp_accountant = RDPAccountant()
# # alpha = 10
# temp_accountant.step(noise_multiplier=1, sample_rate=0.25)
# eps, best_alpha = temp_accountant.get_privacy_spent(delta=1e-5)
# print(eps, best_alpha )

from torch import func


def find_optimal_noise_multiplier(
        epochs: int,
        # max_grad_norm: float,
        target_epsilon: float,
        target_delta: float,
        sample_rate: float,
        dataset_size: int,
        batch_size: int,
        alphas: list = None,
        sigma_min: float = 0.01,
        sigma_max: float = 10.0,
        tolerance: float = 0.01,
        max_iter: int = 100
) -> float:
    """
    计算达到目标隐私预算(ε,δ)所需的最小噪声乘数

    参数:
        epochs: 训练轮次
        # max_grad_norm: 梯度裁剪阈值
        target_epsilon: 目标隐私预算ε
        target_delta: 目标δ值
        sample_rate: 采样率 (batch_size / dataset_size)
        dataset_size: 数据集大小
        batch_size: 批次大小
        alphas: RDP阶数列表，默认[1.5, 2, 4, 8, 16, 32, 64]
        sigma_min: 噪声乘数搜索下限
        sigma_max: 噪声乘数搜索上限
        tolerance: 精度容差
        max_iter: 最大迭代次数

    返回:
        满足隐私预算的最小噪声乘数
    """
    # 初始化RDP会计器
    accountant = RDPAccountant()

    # 设置RDP阶数(如果未提供)
    if alphas is None:
        alphas = [1.5, 2, 4, 8, 16, 32, 64]

    # 计算总步数
    steps_per_epoch = dataset_size // batch_size
    total_steps = epochs * steps_per_epoch

    # 二分搜索寻找最优噪声乘数
    low = sigma_min
    high = sigma_max
    best_sigma = None

    from tqdm import tqdm
    # 使用tqdm显示进度
    with tqdm(total=max_iter, desc="寻找最优噪声乘数") as pbar:
        for i in range(max_iter):
            # 计算中点
            sigma = (low + high) / 2.0

            # 创建新的会计器
            accountant = RDPAccountant()

            # 模拟训练过程
            for step in range(total_steps):
                accountant.step(
                    noise_multiplier=sigma,
                    sample_rate=sample_rate
                )

            # 计算当前隐私消耗
            epsilon, best_alpha = accountant.get_privacy_spent(delta=target_delta)

            # 检查是否满足目标
            if epsilon <= target_epsilon:
                best_sigma = sigma
                # 噪声过大，尝试缩小
                high = sigma - tolerance
            else:
                # 噪声不足，尝试增大
                low = sigma + tolerance

            # 检查收敛
            if high - low < tolerance:
                break

            # 更新进度条
            pbar.set_postfix({
                'epsilon': epsilon,
                'sigma': sigma,
                'range': f"[{low:.4f}, {high:.4f}]"
            })
            pbar.update(1)

    # 如果没有找到满足条件的噪声值，选择上限值
    if best_sigma is None:
        best_sigma = sigma_max
        print(f"警告: 在范围内未找到满足ε<={target_epsilon}的噪声值。使用最大值: {best_sigma}")

    return best_sigma, best_alpha



(sigma,alpha) = find_optimal_noise_multiplier(
        epochs=100,
        # max_grad_norm: float,
        target_epsilon=6,
        target_delta=1e-4,
        sample_rate=0.5,
        dataset_size= 1,
        batch_size=1
)

print(sigma, alpha)
# from flgoasyn.utils.rdp_analysis import calibrating_sampled_gaussian
# asas= calibrating_sampled_gaussian(
#      0.5,
#      5,
#      1e-4,
#      1,
#      err=1e-2
#  )
#
# print(asas)

def sigma_from_subsampled_rdp(
    eps_rdp: float,
    alpha: float,
    q: float,
    # C: float = 1.0
) -> float:
    """
    根据给定的阶数 alpha、子采样率 q，以及对应的 RDP 消耗 eps_rdp，
    反推 Gaussian 噪声倍增因子 sigma，使 Poisson 子采样 + Gaussian 机制
    满足该 eps_rdp (替换邻居定义下)。

    Args:
        eps_rdp: RDP 消耗 ε_RDP(α)
        alpha:   RDP 阶数 α (>1)
        q:       子采样率 (batch_size / dataset_size)
        # C:       L2 裁剪阈值（敏感度），默认为 1.0

    Returns:
        noise multiplier σ_actual = C * σ
    """
    if alpha <= 1:
        raise ValueError("alpha must be > 1")
    if not (0 < q <= 1):
        raise ValueError("sampling rate q must be in (0,1]")

    # 计算 raw = α/(2 σ^2)
    # raw = (1/(α-1)) * ln(1 + (e^{(α-1)*eps_rdp}-1)/q^2)
    inner = math.exp((alpha-1) * eps_rdp) - 1.0
    if q == 0:
        raise ValueError("q must be > 0 for subsampled RDP inversion")
    raw = math.log(1.0 + inner / (q*q)) / (alpha - 1)

    # σ (unit sensitivity) = sqrt(α / (2 * raw))
    sigma_unit = math.sqrt(alpha / (2.0 * raw))

    # 实际噪声倍增因子乘以敏感度 C
    return sigma_unit


alpha = 10
eps_rdp = 5
q = 0.01
sigma = sigma_from_subsampled_rdp(eps_rdp, alpha, q)
print(f"Reconstructed σ ≈ {sigma:.4f}")
#
# class RDPDynamicScheduler:
#     def __init__(
#         self,
#         target_eps: float,
#         target_delta: float,
#         orders: list,
#         T: int,
#         clip_C: float,
#         schedule_gamma: float
#     ):
#         """
#         target_eps, target_delta: 全局 (ε,δ)-DP 目标
#         orders: RDP 阶数列表（如 [2,3,5,10,20,50,100]）
#         T: 总轮数
#         clip_C: 本地裁剪阈值 C
#         schedule_gamma: 指数分配 γ（可由最终/初始比 R 确定）
#         """
#         self.T = T
#         self.C = clip_C
#         self.delta = target_delta
#
#         # 1. 全局 (ε,δ) → 遍历 α 找到最优 ε_RDP_tot(α) 和 α⋆
#         #    ε_RDP_tot(α) = ε - ln(1/δ)/(α-1)
#         best = None
#         for α in orders:
#             eps_rdp = target_eps - np.log(1/target_delta)/(α-1)
#             if best is None or eps_rdp < best[0]:
#                 best = (eps_rdp, α)
#         self.eps_rdp_tot, self.alpha_star = best
#         print(f"选定 α*={self.alpha_star}, 全局 RDP 预算 ε_RDP_tot={self.eps_rdp_tot:.4f}")
#
#         # 2. 构造指数调度 e_t，使 sum e_t = 1
#         γ = schedule_gamma
#         weights = np.exp(γ * np.arange(1, T+1))
#         self.e_t = weights / weights.sum()
#
#         # 3. 初始化 RDP 会计器
#         self.acc = RDPAccountant()
#
#     def round_noise_sigma(self, t: int) -> float:
#         """计算第 t 轮的本地噪声倍增因子 σ_local"""
#         eps_rdp_t = self.e_t[t] * self.eps_rdp_tot
#         # 对应噪声标准差 = C * sqrt(α/(2 ε_t))
#         return self.C * np.sqrt(self.alpha_star / (2 * eps_rdp_t))
#
#     def register_round(self, sample_rate: float, sigma: float):
#         """向会计器注册一次“子采样+高斯机制”调用"""
#         # 如果一轮内部只有一次 subsampled-Gaussian，用 step()；否则可多次 step()
#         self.acc.step(noise_multiplier=sigma, sample_rate=sample_rate)
#
#     def get_final_epsilon(self) -> float:
#         """聚合完 T 轮后，累积 RDP → 回 DP，得到最终 ε"""
#         eps, best_order = self.acc.get_epsilon(target_delta=self.delta)
#         print(f"累积 RDP 下最优阶数 {best_order}，最终 ε = {eps:.4f}")
#         return eps
# print(np.sqrt(4))



