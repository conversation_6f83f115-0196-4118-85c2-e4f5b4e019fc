#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
绘制多个客户端梯度范数比较折线图。
从pkl文件中读取不同客户端的梯度范数数据，并绘制范数随轮次变化的折线图。
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional, Any
import argparse


def load_grad_norms_from_pkl(file_path: str) -> List[Dict]:
    """
    从pkl文件中加载梯度范数数据。
    
    参数:
        file_path: pkl文件的路径
        
    返回:
        包含梯度范数的列表，每个元素是一个轮次的数据字典
    """
    try:
        with open(file_path, 'rb') as f:
            grad_data = pickle.load(f)
        
        # 确保返回的是列表
        if not isinstance(grad_data, list):
            grad_data = [grad_data]
            
        return grad_data
    except Exception as e:
        print(f"加载文件 {file_path} 时出错: {e}")
        return []


def extract_grad_norms_data(grad_norms_list: List[Dict]) -> Tuple[List[int], List[float]]:
    """
    从梯度范数列表中提取轮次和范数数据。
    
    参数:
        grad_norms_list: 包含梯度范数的列表，每个元素是一个轮次的数据字典
        
    返回:
        轮次列表和对应的梯度范数列表
    """
    rounds = []
    grad_norms = []
    
    for data in grad_norms_list:
        if isinstance(data, dict) and 'round' in data and 'avg_grad_norm' in data:
            rounds.append(data['round'])
            grad_norms.append(data['avg_grad_norm'])
    
    return rounds, grad_norms


def plot_grad_norms_comparison(
    clients_data: Dict[str, Tuple[List[int], List[float]]],
    title: str = "",
    xlabel: str = "Rounds",
    ylabel: str = "Average Gradient Norm",
    figsize: Tuple[float, float] = (6.4, 4.8),  # 改为matplotlib默认大小
    save_path: Optional[str] = None,
    ylim: Optional[Tuple[float, float]] = None,
    rounds_step: int = 5,
    max_rounds: Optional[int] = None,
    client_names: Optional[Dict[str, str]] = None,  # 添加参数用于自定义客户端名称
    max_clients_to_show: int = 30 # 限制图例中显示的客户端数量
) -> None:
    """
    绘制多个客户端的梯度范数比较折线图。
    
    参数:
        clients_data: 字典，格式为 {客户端ID: (轮次列表, 梯度范数列表)}
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        figsize: 图表大小，默认为 matplotlib 的标准大小 (6.4, 4.8)
        save_path: 保存路径，如果为None则不保存
        ylim: y轴范围，格式为(ymin, ymax)
        rounds_step: 轮次标签的步长
        max_rounds: 最大轮次数，如果为None则使用数据中的最大轮次
        client_names: 客户端名称映射字典，格式为 {客户端ID: 显示名称}，用于自定义图例中的客户端名称
        max_clients_to_show: 限制图例中显示的客户端数量，避免图例过大
    """
    # 设置全局字体为 Times New Roman
    plt.rcParams['font.family'] = 'Times New Roman'
    plt.rcParams['mathtext.fontset'] = 'stix'
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    plt.figure(figsize=figsize)
    
    # 定义线型和颜色
    linestyles = ['-', '--', '-.', ':']
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    # 找出所有轮次的最大值
    all_rounds = []
    for client_id, (rounds, _) in clients_data.items():
        all_rounds.extend(rounds)
    
    # 设置最大轮次
    if max_rounds is None and all_rounds:
        max_rounds = max(all_rounds) + 1  # +1 确保最大轮次能完整显示
    elif max_rounds is None:
        max_rounds = 100  # 默认值
    
    # 如果客户端数量超过限制，则只显示一部分客户端
    client_ids = list(clients_data.keys())
    if len(client_ids) > max_clients_to_show:
        # 选择一部分有代表性的客户端
        # 这里可以根据需要选择方式，例如等间隔选择
        selected_indices = np.linspace(0, len(client_ids) - 1, max_clients_to_show, dtype=int)
        selected_client_ids = [client_ids[i] for i in selected_indices]
        print(f"客户端数量过多，仅显示 {max_clients_to_show} 个有代表性的客户端: {selected_client_ids}")
    else:
        selected_client_ids = client_ids
    
    # 绘制每个客户端的折线
    for i, client_id in enumerate(selected_client_ids):
        rounds, grad_norms = clients_data[client_id]
        
        # 确保数据长度不超过最大轮次
        valid_indices = [idx for idx, r in enumerate(rounds) if r < max_rounds]
        valid_rounds = [rounds[idx] for idx in valid_indices]
        valid_grad_norms = [grad_norms[idx] for idx in valid_indices]
        
        # 使用自定义客户端名称（如果提供）
        if client_names and client_id in client_names:
            display_name = client_names[client_id]
        else:
            display_name = f"Client {client_id}"
        
        plt.plot(valid_rounds, valid_grad_norms, 
                 label=display_name,
                 linestyle=linestyles[i % len(linestyles)],
                 color=colors[i % len(colors)],
                 linewidth=2)
    
    # 设置图表属性
    plt.title(title, fontsize=18, family='Times New Roman')
    plt.xlabel(xlabel, fontsize=16, family='Times New Roman')
    plt.ylabel(ylabel, fontsize=16, family='Times New Roman')
    
    # 设置x轴刻度，步长为rounds_step
    x_ticks = list(range(0, max_rounds + 1, rounds_step))
    plt.xticks(x_ticks, fontsize=14, family='Times New Roman')
    
    # 设置y轴范围和刻度
    if ylim:
        plt.ylim(ylim)
        y_ticks = np.linspace(ylim[0], ylim[1], 6)  # 6个点
        plt.yticks(y_ticks, fontsize=14, family='Times New Roman')
    
    # 添加网格线
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 添加图例
    legend = plt.legend(fontsize=12, loc='best', prop={'family': 'Times New Roman'})
    legend.get_frame().set_linewidth(1.0)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存至: {save_path}")
    
    # 显示图表
    plt.show()


def main():
    """主函数，展示如何使用该模块进行绘图"""
    parser = argparse.ArgumentParser(description='绘制客户端梯度范数折线图')
    parser.add_argument('--data_dir', type=str, default='C:\\Users\\<USER>\\Desktop\\result\\CICDIS17\\draw',
                        help='包含pkl文件的目录')
    parser.add_argument('--output', type=str, default='grad_norms_comparison.png', 
                        help='输出图像文件名')
    parser.add_argument('--title', type=str, default='',
                        help='图表标题')
    parser.add_argument('--ymin', type=float, default=0., 
                        help='Y轴最小值')
    parser.add_argument('--ymax', type=float, default=1.5,
                        help='Y轴最大值')
    parser.add_argument('--rounds_step', type=int, default=10, 
                        help='轮次标签的步长')
    parser.add_argument('--max_rounds', type=int, default=None, 
                        help='最大轮次数')
    parser.add_argument('--max_clients', type=int, default=10,
                        help='图例中显示的最大客户端数量')
    
    args = parser.parse_args()
    
    # 从指定目录读取pkl文件并绘图
    def plot_from_directory(data_dir=args.data_dir,
                           output=args.output,
                           title=args.title,
                           ymin=args.ymin, 
                           ymax=args.ymax,
                           rounds_step=args.rounds_step, 
                           max_rounds=args.max_rounds,
                           max_clients=args.max_clients):
        """从指定目录读取pkl文件并绘制比较图"""
        # 获取目录中的所有pkl文件
        if not os.path.exists(data_dir):
            print(f"目录 {data_dir} 不存在")
            return
            
        pkl_files = [f for f in os.listdir(data_dir) if f.endswith('.pkl')]
        
        if not pkl_files:
            print(f"在目录 {data_dir} 中未找到pkl文件")
            return
        
        print(f"找到以下文件: {pkl_files}")
        
        # 读取每个客户端的数据
        clients_data = {}
        for file_name in pkl_files:
            file_path = os.path.join(data_dir, file_name)
            # 从文件名中提取客户端ID
            client_id = file_name.split('_')[1]
            
            # 加载梯度范数数据
            grad_norms_list = load_grad_norms_from_pkl(file_path)
            
            # 提取轮次和梯度范数数据
            rounds, grad_norms = extract_grad_norms_data(grad_norms_list)
            
            if rounds and grad_norms:
                clients_data[client_id] = (rounds, grad_norms)
                print(f"成功加载客户端 {client_id} 的数据，共 {len(rounds)} 个数据点")
        
        if not clients_data:
            print("未能从pkl文件中提取到有效的梯度范数数据")
            return
        
        # 自定义客户端名称映射
        custom_names = {
            "client_1_grad_norms": "Client 1",
            "client_2_grad_norms": "Client 2", 
            "client_3_grad_norms": "Client 3",
            "client_4_grad_norms": "Client 4",
            "client_5_grad_norms": "Client 5",
            "client_6_grad_norms": "Client 6",
            "client_7_grad_norms": "Client 7",
            "client_8_grad_norms": "Client 8", 
            "client_9_grad_norms": "Client 9",
            # "client_10_grad_norms": "Client 10",
            # "client_11_grad_norms": "Client 11",
            # "client_12_grad_norms": "Client 12",
            # "client_13_grad_norms": "Client 13",
            # "client_14_grad_norms": "Client 14",
            # "client_15_grad_norms": "Client 15",
            # "client_16_grad_norms": "Client 16",
            # "client_17_grad_norms": "Client 17",
            # "client_18_grad_norms": "Client 18",
            # "client_19_grad_norms": "Client 19"
        }
        
        # 确定y轴范围
        ylim = None
        if ymin is not None and ymax is not None:
            ylim = (ymin, ymax)
        
        # 绘制比较图
        save_path = os.path.join(data_dir, output)
        plot_grad_norms_comparison(
            clients_data=clients_data,
            title=title,
            save_path=save_path,
            ylim=ylim,
            rounds_step=rounds_step,
            max_rounds=max_rounds,
            client_names=custom_names,
            max_clients_to_show=max_clients
        )
    
    # 执行绘图
    plot_from_directory()


if __name__ == "__main__":
    main() 