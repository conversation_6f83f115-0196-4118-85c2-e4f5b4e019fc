import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc, precision_recall_curve
from loguru import logger

def set_seed(seed):
    """
    设置随机种子以确保实验可重复性
    
    参数:
        seed: 随机种子
    """
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
def get_device():
    """
    获取可用的计算设备
    
    返回:
        device: 计算设备
    """
    if torch.cuda.is_available():
        device = torch.device('cuda')
        logger.info(f"使用GPU: {torch.cuda.get_device_name(0)}")
    else:
        device = torch.device('cpu')
        logger.info("使用CPU")
    return device

def save_model(model, path):
    """
    保存模型
    
    参数:
        model: 模型
        path: 保存路径
    """
    os.makedirs(os.path.dirname(path), exist_ok=True)
    torch.save(model.state_dict(), path)
    logger.info(f"模型保存到 {path}")
    
def load_model(model, path, device=None):
    """
    加载模型
    
    参数:
        model: 模型
        path: 加载路径
        device: 计算设备
        
    返回:
        model: 加载后的模型
    """
    if device is None:
        device = get_device()
        
    model.load_state_dict(torch.load(path, map_location=device))
    logger.info(f"从 {path} 加载模型")
    return model

def plot_confusion_matrix(y_true, y_pred, classes, save_path=None):
    """
    绘制混淆矩阵
    
    参数:
        y_true: 真实标签
        y_pred: 预测标签
        classes: 类别名称
        save_path: 保存路径
    """
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=classes, yticklabels=classes)
    plt.xlabel('预测标签')
    plt.ylabel('真实标签')
    plt.title('混淆矩阵')
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)
        logger.info(f"混淆矩阵保存到 {save_path}")
    
    plt.close()
    
def plot_roc_curve(y_true, y_score, save_path=None):
    """
    绘制ROC曲线
    
    参数:
        y_true: 真实标签
        y_score: 预测分数
        save_path: 保存路径
    """
    # 如果是多分类问题，转换为二分类问题（一对多）
    if len(y_score.shape) > 1 and y_score.shape[1] > 2:
        # 多分类问题
        n_classes = y_score.shape[1]
        fpr = {}
        tpr = {}
        roc_auc = {}
        
        for i in range(n_classes):
            fpr[i], tpr[i], _ = roc_curve((y_true == i).astype(int), y_score[:, i])
            roc_auc[i] = auc(fpr[i], tpr[i])
            
        plt.figure(figsize=(10, 8))
        
        for i in range(n_classes):
            plt.plot(fpr[i], tpr[i], lw=2,
                     label=f'类别 {i} (AUC = {roc_auc[i]:.2f})')
    else:
        # 二分类问题
        if len(y_score.shape) > 1:
            # 使用正类的分数
            y_score = y_score[:, 1]
            
        fpr, tpr, _ = roc_curve(y_true, y_score)
        roc_auc = auc(fpr, tpr)
        
        plt.figure(figsize=(10, 8))
        plt.plot(fpr, tpr, lw=2, label=f'ROC曲线 (AUC = {roc_auc:.2f})')
    
    plt.plot([0, 1], [0, 1], 'k--', lw=2)
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('假正例率')
    plt.ylabel('真正例率')
    plt.title('接收者操作特征曲线')
    plt.legend(loc="lower right")
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)
        logger.info(f"ROC曲线保存到 {save_path}")
    
    plt.close()
    
def plot_precision_recall_curve(y_true, y_score, save_path=None):
    """
    绘制精确率-召回率曲线
    
    参数:
        y_true: 真实标签
        y_score: 预测分数
        save_path: 保存路径
    """
    # 如果是多分类问题，转换为二分类问题（一对多）
    if len(y_score.shape) > 1 and y_score.shape[1] > 2:
        # 多分类问题
        n_classes = y_score.shape[1]
        precision = {}
        recall = {}
        avg_precision = {}
        
        for i in range(n_classes):
            precision[i], recall[i], _ = precision_recall_curve((y_true == i).astype(int), y_score[:, i])
            avg_precision[i] = np.mean(precision[i])
            
        plt.figure(figsize=(10, 8))
        
        for i in range(n_classes):
            plt.plot(recall[i], precision[i], lw=2,
                     label=f'类别 {i} (AP = {avg_precision[i]:.2f})')
    else:
        # 二分类问题
        if len(y_score.shape) > 1:
            # 使用正类的分数
            y_score = y_score[:, 1]
            
        precision, recall, _ = precision_recall_curve(y_true, y_score)
        avg_precision = np.mean(precision)
        
        plt.figure(figsize=(10, 8))
        plt.plot(recall, precision, lw=2, label=f'PR曲线 (AP = {avg_precision:.2f})')
    
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('召回率')
    plt.ylabel('精确率')
    plt.title('精确率-召回率曲线')
    plt.legend(loc="lower left")
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)
        logger.info(f"精确率-召回率曲线保存到 {save_path}")
    
    plt.close()
    
def plot_training_history(history, save_path=None):
    """
    绘制训练历史
    
    参数:
        history: 训练历史字典，包含'train_loss', 'train_acc', 'val_loss', 'val_acc'
        save_path: 保存路径
    """
    plt.figure(figsize=(12, 5))
    
    # 绘制损失
    plt.subplot(1, 2, 1)
    plt.plot(history['train_loss'], label='训练损失')
    if 'val_loss' in history:
        plt.plot(history['val_loss'], label='验证损失')
    plt.xlabel('轮次')
    plt.ylabel('损失')
    plt.title('训练和验证损失')
    plt.legend()
    
    # 绘制准确率
    plt.subplot(1, 2, 2)
    plt.plot(history['train_acc'], label='训练准确率')
    if 'val_acc' in history:
        plt.plot(history['val_acc'], label='验证准确率')
    plt.xlabel('轮次')
    plt.ylabel('准确率')
    plt.title('训练和验证准确率')
    plt.legend()
    
    plt.tight_layout()
    
    if save_path:
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path)
        logger.info(f"训练历史保存到 {save_path}")
    
    plt.close()
    
def print_classification_report(y_true, y_pred, target_names=None):
    """
    打印分类报告
    
    参数:
        y_true: 真实标签
        y_pred: 预测标签
        target_names: 目标类别名称
    """
    report = classification_report(y_true, y_pred, target_names=target_names)
    logger.info(f"分类报告:\n{report}")
    
def setup_logger(log_file=None):
    """
    设置日志记录器
    
    参数:
        log_file: 日志文件路径
    """
    logger.remove()  # 移除默认处理程序
    
    # 添加控制台处理程序
    logger.add(lambda msg: print(msg, end=""), colorize=True, format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    
    # 添加文件处理程序
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        logger.add(log_file, rotation="10 MB", compression="zip", format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}")
        
    logger.info("日志记录器设置完成") 